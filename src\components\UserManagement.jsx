import React, { useState, useEffect } from 'react';
import { db } from '../database/db';

const UserManagement = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currentView, setCurrentView] = useState('users');
  const [selectedUser, setSelectedUser] = useState(null);
  const [userForm, setUserForm] = useState({
    username: '',
    email: '',
    fullName: '',
    password: '',
    role: 'data-entry',
    isActive: true,
    permissions: {
      sales: false,
      purchases: false,
      inventory: false,
      contracts: false,
      labor: false,
      payroll: false,
      reports: false,
      settings: false
    }
  });

  const roles = {
    'super-admin': 'مدير أعلى',
    'manager': 'مدير',
    'accountant': 'محاسب',
    'warehouse-keeper': 'أمين مخزن',
    'data-entry': 'إدخال بيانات'
  };

  const permissions = {
    // الصلاحيات الأساسية
    sales: 'المبيعات',
    purchases: 'المشتريات',
    inventory: 'المخزون',
    contracts: 'العقود',
    labor: 'العمالة',
    payroll: 'الرواتب',
    reports: 'التقارير',
    settings: 'الإعدادات',
    editPostedEntries: 'تعديل القيود المرحلة',
    editAllData: 'تعديل جميع البيانات',

    // صلاحيات أقسام الشاشة الرئيسية
    dashboardChartOfAccounts: 'دليل الحسابات',
    dashboardJournalEntries: 'القيود اليومية',
    dashboardAccountsTracker: 'متابعة الحسابات',
    dashboardPaymentManagement: 'إدارة المدفوعات',
    dashboardJournalReports: 'تقارير القيود',
    dashboardInventoryManagement: 'إدارة المخزون',
    dashboardSalesInvoice: 'فاتورة مبيعات',
    dashboardPurchaseInvoice: 'فاتورة مشتريات',
    dashboardCustomersSuppliers: 'العملاء والموردين',
    dashboardFinancialReports: 'التقارير المالية',
    dashboardContractManagement: 'إدارة العقود',
    dashboardLaborManagement: 'إدارة العمالة',
    dashboardPayrollSystem: 'نظام الرواتب',
    dashboardGlobalSearch: 'البحث الشامل',
    dashboardSystemSettings: 'إعدادات النظام',
    dashboardDatabaseCheck: 'فحص قاعدة البيانات'
  };

  useEffect(() => {
    loadUsers();
  }, []);

  const loadUsers = async () => {
    try {
      setLoading(true);
      const allUsers = await db.users.orderBy('createdAt').reverse().toArray();
      setUsers(allUsers);
    } catch (error) {
      console.error('خطأ في تحميل المستخدمين:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUserSubmit = async (e) => {
    e.preventDefault();

    try {
      let userData = {
        username: userForm.username,
        fullName: userForm.fullName,
        email: userForm.email,
        role: userForm.role,
        isActive: userForm.isActive,
        permissions: userForm.permissions,
        lastLogin: null
      };

      if (selectedUser) {
        // عند التعديل، نضيف كلمة السر فقط إذا تم إدخالها
        if (userForm.password && userForm.password.trim() !== '') {
          userData.password = userForm.password;
        }

        userData.updatedAt = new Date();

        await db.users.update(selectedUser.id, userData);
        alert('تم تحديث بيانات المستخدم بنجاح');
      } else {
        // عند الإضافة، كلمة السر مطلوبة
        if (!userForm.password || userForm.password.trim() === '') {
          alert('كلمة السر مطلوبة');
          return;
        }

        // التحقق من عدم تكرار اسم المستخدم
        const existingUser = await db.users.where('username').equals(userForm.username).first();
        if (existingUser) {
          alert('اسم المستخدم موجود بالفعل');
          return;
        }

        userData.password = userForm.password;
        userData.createdAt = new Date();

        await db.users.add(userData);
        alert('تم إضافة المستخدم بنجاح');
      }

      resetUserForm();
      setCurrentView('users');
      loadUsers();
    } catch (error) {
      console.error('خطأ في حفظ بيانات المستخدم:', error);
      alert('حدث خطأ أثناء حفظ بيانات المستخدم');
    }
  };

  const resetUserForm = () => {
    setUserForm({
      username: '',
      email: '',
      fullName: '',
      password: '',
      role: 'data-entry',
      isActive: true,
      permissions: {
        sales: false,
        purchases: false,
        inventory: false,
        contracts: false,
        labor: false,
        payroll: false,
        reports: false,
        settings: false,
        editPostedEntries: false,
        editAllData: false,

        // صلاحيات أقسام الشاشة الرئيسية
        dashboardChartOfAccounts: false,
        dashboardJournalEntries: false,
        dashboardAccountsTracker: false,
        dashboardPaymentManagement: false,
        dashboardJournalReports: false,
        dashboardInventoryManagement: false,
        dashboardSalesInvoice: false,
        dashboardPurchaseInvoice: false,
        dashboardCustomersSuppliers: false,
        dashboardFinancialReports: false,
        dashboardContractManagement: false,
        dashboardLaborManagement: false,
        dashboardPayrollSystem: false,
        dashboardGlobalSearch: false,
        dashboardSystemSettings: false,
        dashboardDatabaseCheck: false
      }
    });
    setSelectedUser(null);
  };

  const handleEditUser = (user) => {
    setSelectedUser(user);
    setUserForm({
      username: user.username,
      email: user.email || '',
      fullName: user.fullName || '',
      password: '', // لا نعرض كلمة السر الحالية لأسباب أمنية
      role: user.role,
      isActive: user.isActive,
      permissions: user.permissions || {}
    });
    setCurrentView('add-user');
  };

  const handleDeleteUser = async (id) => {
    if (!confirm('هل أنت متأكد من حذف هذا المستخدم؟')) return;
    
    try {
      await db.users.delete(id);
      alert('تم حذف المستخدم بنجاح');
      loadUsers();
    } catch (error) {
      console.error('خطأ في حذف المستخدم:', error);
      alert('حدث خطأ أثناء حذف المستخدم');
    }
  };

  const handleRoleChange = (role) => {
    let defaultPermissions = {};
    
    switch (role) {
      case 'super-admin':
        defaultPermissions = Object.keys(permissions).reduce((acc, key) => {
          acc[key] = true;
          return acc;
        }, {});
        break;
      case 'manager':
        defaultPermissions = Object.keys(permissions).reduce((acc, key) => {
          acc[key] = true;
          return acc;
        }, {});
        // المدير العادي لا يستطيع تعديل القيود المرحلة
        defaultPermissions.editPostedEntries = false;
        defaultPermissions.editAllData = false;
        break;
      case 'accountant':
        defaultPermissions = {
          sales: true,
          purchases: true,
          inventory: false,
          contracts: true,
          labor: true,
          payroll: true,
          reports: true,
          settings: false,
          editPostedEntries: false,
          editAllData: false,

          // صلاحيات أقسام الشاشة الرئيسية للمحاسب
          dashboardChartOfAccounts: true,
          dashboardJournalEntries: true,
          dashboardAccountsTracker: true,
          dashboardPaymentManagement: true,
          dashboardJournalReports: true,
          dashboardInventoryManagement: false,
          dashboardSalesInvoice: true,
          dashboardPurchaseInvoice: true,
          dashboardCustomersSuppliers: true,
          dashboardFinancialReports: true,
          dashboardContractManagement: true,
          dashboardLaborManagement: true,
          dashboardPayrollSystem: true,
          dashboardGlobalSearch: true,
          dashboardSystemSettings: false,
          dashboardDatabaseCheck: false
        };
        break;
      case 'warehouse-keeper':
        defaultPermissions = {
          sales: false,
          purchases: true,
          inventory: true,
          contracts: false,
          labor: false,
          payroll: false,
          reports: false,
          settings: false,
          editPostedEntries: false,
          editAllData: false,

          // صلاحيات أقسام الشاشة الرئيسية لأمين المخزن
          dashboardChartOfAccounts: false,
          dashboardJournalEntries: false,
          dashboardAccountsTracker: false,
          dashboardPaymentManagement: false,
          dashboardJournalReports: false,
          dashboardInventoryManagement: true,
          dashboardSalesInvoice: false,
          dashboardPurchaseInvoice: true,
          dashboardCustomersSuppliers: true,
          dashboardFinancialReports: false,
          dashboardContractManagement: false,
          dashboardLaborManagement: false,
          dashboardPayrollSystem: false,
          dashboardGlobalSearch: true,
          dashboardSystemSettings: false,
          dashboardDatabaseCheck: false
        };
        break;
      case 'data-entry':
        defaultPermissions = {
          sales: true,
          purchases: true,
          inventory: false,
          contracts: false,
          labor: false,
          payroll: false,
          reports: false,
          settings: false,
          editPostedEntries: false,
          editAllData: false,

          // صلاحيات أقسام الشاشة الرئيسية لإدخال البيانات
          dashboardChartOfAccounts: false,
          dashboardJournalEntries: false,
          dashboardAccountsTracker: false,
          dashboardPaymentManagement: false,
          dashboardJournalReports: false,
          dashboardInventoryManagement: false,
          dashboardSalesInvoice: true,
          dashboardPurchaseInvoice: true,
          dashboardCustomersSuppliers: true,
          dashboardFinancialReports: false,
          dashboardContractManagement: false,
          dashboardLaborManagement: false,
          dashboardPayrollSystem: false,
          dashboardGlobalSearch: true,
          dashboardSystemSettings: false,
          dashboardDatabaseCheck: false
        };
        break;
    }

    setUserForm({
      ...userForm,
      role,
      permissions: defaultPermissions
    });
  };

  const handlePermissionChange = (permission) => {
    setUserForm({
      ...userForm,
      permissions: {
        ...userForm.permissions,
        [permission]: !userForm.permissions[permission]
      }
    });
  };

  if (loading) {
    return (
      <div className="container">
        <div className="loading">
          <div className="spinner"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container">
      {/* Header */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h2 style={{ margin: 0, color: '#333' }}>👥 إدارة المستخدمين</h2>
        </div>
      </div>

      {/* Navigation */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div style={{ display: 'flex', gap: '1rem' }}>
          <button
            className={`btn ${currentView === 'users' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setCurrentView('users')}
          >
            👥 المستخدمين
          </button>
          <button
            className={`btn ${currentView === 'add-user' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => {
              setCurrentView('add-user');
              resetUserForm();
            }}
          >
            ➕ إضافة مستخدم
          </button>
        </div>
      </div>

      {/* Users List */}
      {currentView === 'users' && (
        <div className="card">
          <div className="card-title">
            المستخدمين ({users.length})
          </div>
          
          {users.length > 0 ? (
            <div className="table-responsive">
              <table className="table">
                <thead>
                  <tr>
                    <th>اسم المستخدم</th>
                    <th>الاسم الكامل</th>
                    <th>البريد الإلكتروني</th>
                    <th>الدور</th>
                    <th>الحالة</th>
                    <th>تاريخ الإنشاء</th>
                    <th>إجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {users.map(user => (
                    <tr key={user.id}>
                      <td><strong>{user.username}</strong></td>
                      <td>{user.fullName || '-'}</td>
                      <td>{user.email || '-'}</td>
                      <td>
                        <span style={{
                          padding: '0.25rem 0.5rem',
                          borderRadius: '4px',
                          fontSize: '0.8rem',
                          background: user.role === 'super-admin' ? '#e2e3e5' :
                                     user.role === 'manager' ? '#d4edda' :
                                     user.role === 'accountant' ? '#d1ecf1' :
                                     user.role === 'warehouse-keeper' ? '#fff3cd' : '#f8d7da',
                          color: user.role === 'super-admin' ? '#383d41' :
                                user.role === 'manager' ? '#155724' :
                                user.role === 'accountant' ? '#0c5460' :
                                user.role === 'warehouse-keeper' ? '#856404' : '#721c24'
                        }}>
                          {roles[user.role]}
                        </span>
                      </td>
                      <td>
                        <span style={{
                          padding: '0.25rem 0.5rem',
                          borderRadius: '4px',
                          fontSize: '0.8rem',
                          background: user.isActive ? '#d4edda' : '#f8d7da',
                          color: user.isActive ? '#155724' : '#721c24'
                        }}>
                          {user.isActive ? 'نشط' : 'غير نشط'}
                        </span>
                      </td>
                      <td>{new Date(user.createdAt).toLocaleDateString('ar-EG')}</td>
                      <td>
                        <div style={{ display: 'flex', gap: '0.5rem' }}>
                          <button
                            className="btn btn-primary btn-sm"
                            onClick={() => handleEditUser(user)}
                          >
                            تعديل
                          </button>
                          <button
                            className="btn btn-danger btn-sm"
                            onClick={() => handleDeleteUser(user.id)}
                          >
                            حذف
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div style={{ textAlign: 'center', color: '#666', padding: '2rem' }}>
              لا توجد مستخدمين مسجلين
            </div>
          )}
        </div>
      )}

      {/* Add/Edit User */}
      {currentView === 'add-user' && (
        <div className="card">
          <div className="card-title">
            {selectedUser ? 'تعديل مستخدم' : 'إضافة مستخدم جديد'}
          </div>
          
          <form onSubmit={handleUserSubmit}>
            <div className="grid grid-2">
              <div className="form-group">
                <label className="form-label">اسم المستخدم *</label>
                <input
                  type="text"
                  className="form-control"
                  value={userForm.username}
                  onChange={(e) => setUserForm({...userForm, username: e.target.value})}
                  required
                />
              </div>

              <div className="form-group">
                <label className="form-label">الاسم الكامل</label>
                <input
                  type="text"
                  className="form-control"
                  value={userForm.fullName}
                  onChange={(e) => setUserForm({...userForm, fullName: e.target.value})}
                />
              </div>

              <div className="form-group">
                <label className="form-label">البريد الإلكتروني</label>
                <input
                  type="email"
                  className="form-control"
                  value={userForm.email}
                  onChange={(e) => setUserForm({...userForm, email: e.target.value})}
                />
              </div>

              <div className="form-group">
                <label className="form-label">
                  كلمة السر {!selectedUser && '*'}
                  {selectedUser && (
                    <span style={{ fontSize: '0.8rem', color: '#6c757d' }}>
                      (اتركها فارغة للاحتفاظ بكلمة السر الحالية)
                    </span>
                  )}
                </label>
                <input
                  type="password"
                  className="form-control"
                  value={userForm.password || ''}
                  onChange={(e) => setUserForm({...userForm, password: e.target.value})}
                  required={!selectedUser}
                  placeholder={selectedUser ? "كلمة سر جديدة (اختياري)" : "كلمة السر"}
                />
              </div>

              <div className="form-group">
                <label className="form-label">الدور *</label>
                <select
                  className="form-control"
                  value={userForm.role}
                  onChange={(e) => handleRoleChange(e.target.value)}
                  required
                >
                  {Object.keys(roles).map(role => (
                    <option key={role} value={role}>
                      {roles[role]}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="form-group">
              <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <input
                  type="checkbox"
                  checked={userForm.isActive}
                  onChange={(e) => setUserForm({...userForm, isActive: e.target.checked})}
                />
                <span>نشط</span>
              </label>
            </div>

            {/* Permissions */}
            <div style={{ marginTop: '2rem' }}>
              <h4 style={{ marginBottom: '1rem', color: '#495057' }}>الصلاحيات</h4>

              {/* الصلاحيات الأساسية */}
              <div style={{ marginBottom: '1.5rem' }}>
                <h5 style={{ fontSize: '0.9rem', color: '#6c757d', marginBottom: '0.5rem', fontWeight: 'bold' }}>
                  📋 الصلاحيات الأساسية
                </h5>
                <div className="grid grid-4">
                  {Object.keys(permissions).filter(key => !key.startsWith('dashboard')).map(permission => (
                    <label
                      key={permission}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.5rem',
                        padding: '0.5rem',
                        border: '1px solid #dee2e6',
                        borderRadius: '5px',
                        cursor: 'pointer',
                        background: userForm.permissions[permission] ? '#e7f3ff' : 'white'
                      }}
                    >
                      <input
                        type="checkbox"
                        checked={userForm.permissions[permission] || false}
                        onChange={() => handlePermissionChange(permission)}
                      />
                      <span style={{ fontSize: '0.85rem' }}>{permissions[permission]}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* صلاحيات أقسام الشاشة الرئيسية */}
              <div>
                <h5 style={{ fontSize: '0.9rem', color: '#6c757d', marginBottom: '0.5rem', fontWeight: 'bold' }}>
                  🏠 صلاحيات أقسام الشاشة الرئيسية
                </h5>
                <div className="grid grid-4">
                  {Object.keys(permissions).filter(key => key.startsWith('dashboard')).map(permission => (
                    <label
                      key={permission}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.5rem',
                        padding: '0.5rem',
                        border: '1px solid #dee2e6',
                        borderRadius: '5px',
                        cursor: 'pointer',
                        background: userForm.permissions[permission] ? '#e8f5e9' : 'white'
                      }}
                    >
                      <input
                        type="checkbox"
                        checked={userForm.permissions[permission] || false}
                        onChange={() => handlePermissionChange(permission)}
                      />
                      <span style={{ fontSize: '0.85rem' }}>{permissions[permission]}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>

            <div style={{ display: 'flex', gap: '1rem', marginTop: '2rem' }}>
              <button type="submit" className="btn btn-primary">
                {selectedUser ? 'تحديث المستخدم' : 'إضافة المستخدم'}
              </button>
              <button 
                type="button" 
                className="btn btn-secondary"
                onClick={() => setCurrentView('users')}
              >
                إلغاء
              </button>
            </div>
          </form>
        </div>
      )}
    </div>
  );
};

export default UserManagement;
