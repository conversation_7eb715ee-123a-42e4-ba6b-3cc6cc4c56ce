import React, { useState } from 'react';
import { resetDatabase, clearTestData, db } from '../database/db';

const SystemReset = ({ user }) => {
  const [loading, setLoading] = useState(false);
  const [confirmText, setConfirmText] = useState('');
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [resetType, setResetType] = useState(''); // 'full' أو 'test-data' أو 'labor-data'

  const clearLaborData = async () => {
    try {
      console.log('🧹 بدء مسح بيانات العمالة...');

      // مسح جميع بيانات العمالة
      await db.workers.clear();
      await db.workSessions.clear();
      await db.laborCosts.clear();
      await db.workerVouchers.clear();

      // مسح القيود المحاسبية المتعلقة بالعمالة
      const laborEntries = await db.journalEntries.where('type').anyOf(['worker_voucher', 'worker_payment']).toArray();
      for (const entry of laborEntries) {
        await db.journalEntryDetails.where('entryId').equals(entry.id).delete();
        await db.journalEntries.delete(entry.id);
      }

      console.log('✅ تم مسح جميع بيانات العمالة');
      return true;
    } catch (error) {
      console.error('❌ خطأ في مسح بيانات العمالة:', error);
      return false;
    }
  };

  const handleResetDatabase = async () => {
    const expectedText = resetType === 'full' ? 'مسح البيانات' :
                        resetType === 'test-data' ? 'مسح البيانات التجريبية' :
                        'مسح بيانات العمالة';

    if (confirmText !== expectedText) {
      alert(`يرجى كتابة "${expectedText}" بالضبط للتأكيد`);
      return;
    }

    try {
      setLoading(true);

      let success = false;
      if (resetType === 'full') {
        console.log('بدء مسح جميع البيانات...');
        success = await resetDatabase();
      } else if (resetType === 'test-data') {
        console.log('بدء مسح البيانات التجريبية...');
        success = await clearTestData();
      } else if (resetType === 'labor-data') {
        console.log('بدء مسح بيانات العمالة...');
        success = await clearLaborData();
      }

      if (success) {
        const message = resetType === 'full'
          ? 'تم مسح جميع البيانات بنجاح!\n\nسيتم إعادة تحميل الصفحة...'
          : resetType === 'test-data'
          ? 'تم مسح البيانات التجريبية بنجاح!\n\nسيتم إعادة تحميل الصفحة...'
          : 'تم مسح بيانات العمالة بنجاح!\n\nسيتم إعادة تحميل الصفحة...';

        alert(message);

        // إعادة تحميل الصفحة لإعادة تهيئة النظام
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      } else {
        alert('حدث خطأ أثناء مسح البيانات');
      }
    } catch (error) {
      console.error('خطأ في مسح البيانات:', error);
      alert('حدث خطأ أثناء مسح البيانات: ' + error.message);
    } finally {
      setLoading(false);
      setShowConfirmation(false);
      setConfirmText('');
      setResetType('');
    }
  };

  const startReset = (type) => {
    setResetType(type);
    setShowConfirmation(true);
    setConfirmText('');
  };

  const cancelReset = () => {
    setShowConfirmation(false);
    setConfirmText('');
    setResetType('');
  };

  return (
    <div>
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        marginBottom: '2rem' 
      }}>
        <h3 style={{ margin: 0, color: '#e74c3c' }}>🔄 إعادة تعيين النظام</h3>
      </div>

      {!showConfirmation ? (
        <div className="card">
          <div className="card-title" style={{ color: '#e74c3c' }}>
            ⚠️ تحذير: مسح جميع البيانات
          </div>
          
          <div style={{ marginBottom: '2rem' }}>
            <div style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr 1fr',
              gap: '1rem',
              marginBottom: '1.5rem'
            }}>
              <div style={{
                backgroundColor: '#d1ecf1',
                border: '1px solid #bee5eb',
                borderRadius: '8px',
                padding: '1rem'
              }}>
                <h4 style={{ color: '#0c5460', marginTop: 0, fontSize: '1rem' }}>👷 مسح بيانات العمالة:</h4>
                <ul style={{ color: '#0c5460', marginBottom: 0, fontSize: '0.8rem' }}>
                  <li>جميع العمال</li>
                  <li>جلسات العمل</li>
                  <li>تكاليف العمالة</li>
                  <li>أذون العمال</li>
                  <li>القيود المحاسبية للعمالة</li>
                </ul>
                <div style={{
                  marginTop: '0.5rem',
                  padding: '0.3rem',
                  backgroundColor: '#d4edda',
                  borderRadius: '4px',
                  fontSize: '0.75rem'
                }}>
                  ✅ <strong>يحتفظ بـ:</strong> باقي البيانات
                </div>
              </div>

              <div style={{
                backgroundColor: '#fff3cd',
                border: '1px solid #ffeaa7',
                borderRadius: '8px',
                padding: '1rem'
              }}>
                <h4 style={{ color: '#856404', marginTop: 0, fontSize: '1rem' }}>🧹 مسح البيانات التجريبية:</h4>
                <ul style={{ color: '#856404', marginBottom: 0, fontSize: '0.8rem' }}>
                  <li>الفواتير والقيود</li>
                  <li>المخزون وحركاته</li>
                  <li>العملاء والموردين</li>
                  <li>العقود والعمال</li>
                  <li>أذون الصرف</li>
                  <li>كشوف المرتبات</li>
                </ul>
                <div style={{
                  marginTop: '0.5rem',
                  padding: '0.3rem',
                  backgroundColor: '#d4edda',
                  borderRadius: '4px',
                  fontSize: '0.75rem'
                }}>
                  ✅ <strong>يحتفظ بـ:</strong> دليل الحسابات، المستخدمين
                </div>
              </div>

              <div style={{
                backgroundColor: '#f8d7da',
                border: '1px solid #f5c6cb',
                borderRadius: '8px',
                padding: '1rem'
              }}>
                <h4 style={{ color: '#721c24', marginTop: 0, fontSize: '1rem' }}>🗑️ مسح جميع البيانات:</h4>
                <ul style={{ color: '#721c24', marginBottom: 0, fontSize: '0.8rem' }}>
                  <li>جميع البيانات</li>
                  <li>الإعدادات المخصصة</li>
                  <li>روابط الحسابات</li>
                  <li>بيانات الشركة</li>
                  <li>المستخدمين (عدا admin)</li>
                  <li>الفترات المحاسبية</li>
                </ul>
                <div style={{
                  marginTop: '0.5rem',
                  padding: '0.3rem',
                  backgroundColor: '#d4edda',
                  borderRadius: '4px',
                  fontSize: '0.75rem'
                }}>
                  ✅ <strong>يحتفظ بـ:</strong> دليل الحسابات، admin
                </div>
              </div>
            </div>


          </div>

          <div style={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr 1fr',
            gap: '1rem',
            marginTop: '1rem'
          }}>
            <button
              className="btn btn-info"
              onClick={() => startReset('labor-data')}
              disabled={loading}
              style={{
                padding: '1rem 1.5rem',
                fontSize: '1rem',
                fontWeight: 'bold'
              }}
            >
              👷 مسح بيانات العمالة فقط
            </button>

            <button
              className="btn btn-warning"
              onClick={() => startReset('test-data')}
              disabled={loading}
              style={{
                padding: '1rem 1.5rem',
                fontSize: '1rem',
                fontWeight: 'bold'
              }}
            >
              🧹 مسح البيانات التجريبية
            </button>

            <button
              className="btn btn-danger"
              onClick={() => startReset('full')}
              disabled={loading}
              style={{
                padding: '1rem 1.5rem',
                fontSize: '1rem',
                fontWeight: 'bold'
              }}
            >
              🗑️ مسح جميع البيانات
            </button>
          </div>
        </div>
      ) : (
        <div className="card">
          <div className="card-title" style={{
            color: resetType === 'full' ? '#e74c3c' :
                   resetType === 'test-data' ? '#f39c12' : '#17a2b8'
          }}>
            🔐 تأكيد {resetType === 'full' ? 'مسح جميع البيانات' :
                      resetType === 'test-data' ? 'مسح البيانات التجريبية' :
                      'مسح بيانات العمالة'}
          </div>

          <div style={{ marginBottom: '2rem' }}>
            <div style={{
              backgroundColor: resetType === 'full' ? '#f8d7da' :
                              resetType === 'test-data' ? '#fff3cd' : '#d1ecf1',
              border: resetType === 'full' ? '1px solid #f5c6cb' :
                     resetType === 'test-data' ? '1px solid #ffeaa7' : '1px solid #bee5eb',
              borderRadius: '8px',
              padding: '1.5rem',
              marginBottom: '1.5rem',
              textAlign: 'center'
            }}>
              <h4 style={{
                color: resetType === 'full' ? '#721c24' :
                       resetType === 'test-data' ? '#856404' : '#0c5460',
                marginTop: 0
              }}>
                ⚠️ {resetType === 'full' ? 'تحذير أخير!' :
                     resetType === 'test-data' ? 'تأكيد العملية' : 'تأكيد مسح بيانات العمالة'}
              </h4>
              <p style={{
                color: resetType === 'full' ? '#721c24' :
                       resetType === 'test-data' ? '#856404' : '#0c5460',
                marginBottom: 0, fontSize: '1.1rem'
              }}>
                {resetType === 'full' ? (
                  <>
                    هذا الإجراء <strong>لا يمكن التراجع عنه</strong>!<br/>
                    ستفقد جميع البيانات نهائياً.
                  </>
                ) : resetType === 'test-data' ? (
                  <>
                    سيتم مسح البيانات التجريبية فقط<br/>
                    مع الاحتفاظ بدليل الحسابات والإعدادات.
                  </>
                ) : (
                  <>
                    سيتم مسح جميع بيانات العمالة فقط<br/>
                    مع الاحتفاظ بباقي البيانات والإعدادات.
                  </>
                )}
              </p>
            </div>

            <div className="form-group">
              <label className="form-label" style={{
                fontWeight: 'bold',
                color: resetType === 'full' ? '#e74c3c' : '#f39c12'
              }}>
                للتأكيد، اكتب النص التالي بالضبط: <strong>
                  "{resetType === 'full' ? 'مسح البيانات' : 'مسح البيانات التجريبية'}"
                </strong>
              </label>
              <input
                type="text"
                className="form-control"
                value={confirmText}
                onChange={(e) => setConfirmText(e.target.value)}
                placeholder={`اكتب: ${resetType === 'full' ? 'مسح البيانات' : 'مسح البيانات التجريبية'}`}
                style={{
                  textAlign: 'center',
                  fontSize: '1.1rem',
                  border: confirmText === (resetType === 'full' ? 'مسح البيانات' : 'مسح البيانات التجريبية')
                    ? '2px solid #28a745' : '2px solid #dc3545'
                }}
                disabled={loading}
              />
            </div>
          </div>

          <div style={{ 
            display: 'flex', 
            gap: '1rem', 
            justifyContent: 'center' 
          }}>
            <button
              className="btn btn-secondary"
              onClick={cancelReset}
              disabled={loading}
              style={{ padding: '1rem 2rem' }}
            >
              ❌ إلغاء
            </button>
            
            <button
              className={`btn ${resetType === 'full' ? 'btn-danger' :
                               resetType === 'test-data' ? 'btn-warning' : 'btn-info'}`}
              onClick={handleResetDatabase}
              disabled={loading || confirmText !== (resetType === 'full' ? 'مسح البيانات' :
                                                   resetType === 'test-data' ? 'مسح البيانات التجريبية' :
                                                   'مسح بيانات العمالة')}
              style={{
                padding: '1rem 2rem',
                fontWeight: 'bold'
              }}
            >
              {loading ? '🔄 جاري المسح...' :
               resetType === 'full' ? '🗑️ تأكيد المسح' :
               resetType === 'test-data' ? '🧹 تأكيد المسح' : '👷 تأكيد المسح'}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default SystemReset;
