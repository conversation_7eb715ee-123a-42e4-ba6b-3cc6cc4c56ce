import React from 'react';

// إضافة CSS للأنيميشن
const styles = `
  @keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
  }

  .accounting-entry-row:hover {
    transform: translateX(5px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  }
`;

// إدراج الـ CSS في الصفحة
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.type = 'text/css';
  styleSheet.innerText = styles;
  document.head.appendChild(styleSheet);
}

const AccountingEntries = ({ entries, title }) => {
  const totalDebit = entries.reduce((sum, entry) => sum + entry.debit, 0);
  const totalCredit = entries.reduce((sum, entry) => sum + entry.credit, 0);
  const isBalanced = Math.abs(totalDebit - totalCredit) < 0.01;

  return (
    <div className="card" style={{
      minHeight: '80vh',
      background: 'white',
      border: '2px solid #007bff',
      boxShadow: '0 8px 32px rgba(0,0,0,0.15)',
      borderRadius: '12px'
    }}>
      <div className="card-title" style={{
        background: 'linear-gradient(135deg, #007bff 0%, #0056b3 100%)',
        color: 'white',
        margin: '-1rem -1rem 1.5rem -1rem',
        padding: '1.5rem',
        borderRadius: '10px 10px 0 0',
        fontSize: '1.2rem',
        fontWeight: 'bold',
        textAlign: 'center',
        boxShadow: '0 2px 10px rgba(0,123,255,0.3)'
      }}>
        📋 {title || 'القيود المحاسبية'}
      </div>

      {entries.length > 0 ? (
        <div style={{ minHeight: '60vh', overflow: 'auto' }}>
          <table className="table" style={{
            marginBottom: '1.5rem',
            fontSize: '0.95rem',
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            borderRadius: '8px',
            overflow: 'hidden'
          }}>
            <thead style={{
              position: 'sticky',
              top: 0,
              background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
              zIndex: 1,
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
            }}>
              <tr>
                <th style={{
                  width: '15%',
                  padding: '1rem 0.75rem',
                  fontWeight: 'bold',
                  color: '#495057',
                  borderBottom: '2px solid #dee2e6'
                }}>كود الحساب</th>
                <th style={{
                  width: '25%',
                  padding: '1rem 0.75rem',
                  fontWeight: 'bold',
                  color: '#495057',
                  borderBottom: '2px solid #dee2e6'
                }}>اسم الحساب</th>
                <th style={{
                  width: '30%',
                  padding: '1rem 0.75rem',
                  fontWeight: 'bold',
                  color: '#495057',
                  borderBottom: '2px solid #dee2e6'
                }}>البيان</th>
                <th style={{
                  width: '15%',
                  padding: '1rem 0.75rem',
                  fontWeight: 'bold',
                  color: '#856404',
                  borderBottom: '2px solid #dee2e6',
                  textAlign: 'center'
                }}>مدين</th>
                <th style={{
                  width: '15%',
                  padding: '1rem 0.75rem',
                  fontWeight: 'bold',
                  color: '#0c5460',
                  borderBottom: '2px solid #dee2e6',
                  textAlign: 'center'
                }}>دائن</th>
              </tr>
            </thead>
            <tbody>
              {entries.map((entry, index) => (
                <tr key={index} className="accounting-entry-row" style={{
                  background: entry.debit > 0
                    ? 'linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%)'
                    : 'linear-gradient(135deg, #d1ecf1 0%, #a8e6cf 100%)',
                  borderLeft: entry.debit > 0 ? '4px solid #856404' : '4px solid #0c5460',
                  transition: 'all 0.2s ease',
                  cursor: 'default'
                }}>
                  <td style={{
                    padding: '1rem 0.75rem',
                    fontWeight: 'bold',
                    color: '#495057'
                  }}>
                    {entry.accountCode}
                  </td>
                  <td style={{
                    padding: '1rem 0.75rem',
                    fontWeight: '500',
                    color: '#495057'
                  }}>
                    {entry.accountName}
                  </td>
                  <td style={{
                    padding: '1rem 0.75rem',
                    fontSize: '0.9rem',
                    color: '#6c757d',
                    lineHeight: '1.4'
                  }}>
                    {entry.description}
                  </td>
                  <td style={{
                    padding: '1rem 0.75rem',
                    textAlign: 'center',
                    fontWeight: 'bold',
                    fontSize: '1rem',
                    color: entry.debit > 0 ? '#856404' : '#adb5bd'
                  }}>
                    {entry.debit > 0 ? entry.debit.toLocaleString('ar-EG') : '-'}
                  </td>
                  <td style={{
                    padding: '1rem 0.75rem',
                    textAlign: 'center',
                    fontWeight: 'bold',
                    fontSize: '1rem',
                    color: entry.credit > 0 ? '#0c5460' : '#adb5bd'
                  }}>
                    {entry.credit > 0 ? entry.credit.toLocaleString('ar-EG') : '-'}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {/* إجماليات القيد */}
          <div style={{
            position: 'sticky',
            bottom: 0,
            background: 'linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%)',
            borderTop: '3px solid #007bff',
            padding: '1.5rem',
            borderRadius: '0 0 8px 8px',
            boxShadow: '0 -4px 12px rgba(0,0,0,0.1)'
          }}>
            <div className="grid grid-3" style={{ gap: '1rem' }}>
              <div style={{
                background: 'linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%)',
                padding: '1.25rem',
                borderRadius: '12px',
                textAlign: 'center',
                border: '2px solid #856404',
                boxShadow: '0 4px 12px rgba(133,100,4,0.2)'
              }}>
                <div style={{
                  fontSize: '1rem',
                  color: '#856404',
                  fontWeight: '600',
                  marginBottom: '0.5rem'
                }}>
                  💰 إجمالي المدين
                </div>
                <div style={{
                  fontSize: '1.4rem',
                  fontWeight: 'bold',
                  color: '#856404'
                }}>
                  {totalDebit.toLocaleString('ar-EG')} ج.م
                </div>
              </div>

              <div style={{
                background: 'linear-gradient(135deg, #d1ecf1 0%, #a8e6cf 100%)',
                padding: '1.25rem',
                borderRadius: '12px',
                textAlign: 'center',
                border: '2px solid #0c5460',
                boxShadow: '0 4px 12px rgba(12,84,96,0.2)'
              }}>
                <div style={{
                  fontSize: '1rem',
                  color: '#0c5460',
                  fontWeight: '600',
                  marginBottom: '0.5rem'
                }}>
                  💳 إجمالي الدائن
                </div>
                <div style={{
                  fontSize: '1.4rem',
                  fontWeight: 'bold',
                  color: '#0c5460'
                }}>
                  {totalCredit.toLocaleString('ar-EG')} ج.م
                </div>
              </div>

              <div style={{
                background: isBalanced
                  ? 'linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%)'
                  : 'linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%)',
                padding: '1.25rem',
                borderRadius: '12px',
                textAlign: 'center',
                border: isBalanced ? '2px solid #155724' : '2px solid #721c24',
                boxShadow: isBalanced
                  ? '0 4px 12px rgba(21,87,36,0.2)'
                  : '0 4px 12px rgba(114,28,36,0.2)'
              }}>
                <div style={{
                  fontSize: '1rem',
                  color: isBalanced ? '#155724' : '#721c24',
                  fontWeight: '600',
                  marginBottom: '0.5rem'
                }}>
                  ⚖️ حالة القيد
                </div>
                <div style={{
                  fontSize: '1.4rem',
                  fontWeight: 'bold',
                  color: isBalanced ? '#155724' : '#721c24'
                }}>
                  {isBalanced ? '✅ متوازن' : '❌ غير متوازن'}
                </div>
              </div>
            </div>

            {!isBalanced && (
              <div style={{
                background: 'linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%)',
                border: '2px solid #721c24',
                borderRadius: '12px',
                padding: '1.25rem',
                marginTop: '1.5rem',
                textAlign: 'center',
                color: '#721c24',
                boxShadow: '0 4px 12px rgba(114,28,36,0.2)',
                animation: 'pulse 2s infinite'
              }}>
                <div style={{ fontSize: '1.1rem', fontWeight: 'bold', marginBottom: '0.5rem' }}>
                  ⚠️ تحذير: القيد غير متوازن
                </div>
                <div style={{ fontSize: '1rem' }}>
                  الفرق: <strong>{Math.abs(totalDebit - totalCredit).toLocaleString('ar-EG')} ج.م</strong>
                </div>
              </div>
            )}
          </div>
        </div>
      ) : (
        <div style={{
          textAlign: 'center',
          color: '#6c757d',
          padding: '4rem 2rem',
          minHeight: '60vh',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
          borderRadius: '12px',
          border: '2px dashed #dee2e6'
        }}>
          <div style={{
            fontSize: '4rem',
            marginBottom: '1.5rem',
            opacity: 0.6,
            background: 'linear-gradient(135deg, #007bff 0%, #0056b3 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text'
          }}>
            📋
          </div>
          <div style={{
            fontSize: '1.3rem',
            fontWeight: '600',
            marginBottom: '1rem',
            color: '#495057'
          }}>
            لا توجد قيود محاسبية
          </div>
          <div style={{
            fontSize: '1rem',
            lineHeight: '1.6',
            maxWidth: '400px',
            color: '#6c757d'
          }}>
            سيتم إنشاء القيود تلقائياً عند ملء بيانات الفاتورة واختيار العميل/المورد والأصناف
          </div>
          <div style={{
            marginTop: '2rem',
            padding: '0.75rem 1.5rem',
            background: 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)',
            borderRadius: '25px',
            fontSize: '0.9rem',
            color: '#1976d2',
            border: '1px solid #2196f3'
          }}>
            💡 نصيحة: املأ بيانات الفاتورة لرؤية القيود المحاسبية
          </div>
        </div>
      )}
    </div>
  );
};

export default AccountingEntries;
