import React, { useState, useEffect } from 'react';
import { db } from '../database/db';

const BackupManagement = ({ user }) => {
  const [backups, setBackups] = useState([]);
  const [loading, setLoading] = useState(false);
  const [autoBackup, setAutoBackup] = useState(true);
  const [dataStats, setDataStats] = useState({});

  const loadDataStats = async () => {
    try {
      const stats = {};
      const tables = {
        users: 'المستخدمين',
        accounts: 'الحسابات',
        customers: 'العملاء',
        suppliers: 'الموردين',
        items: 'الأصناف',
        salesInvoices: 'فواتير المبيعات',
        purchaseInvoices: 'فواتير المشتريات',
        journalEntries: 'القيود اليومية',
        contracts: 'العقود',
        workers: 'العمال',
        settings: 'الإعدادات',
        accountingPeriods: 'الفترات المحاسبية',
        laborVouchers: 'سندات العمالة'
      };

      for (const [tableName, displayName] of Object.entries(tables)) {
        try {
          if (db[tableName] && typeof db[tableName].count === 'function') {
            stats[tableName] = {
              count: await db[tableName].count(),
              displayName
            };
          } else {
            stats[tableName] = { count: 0, displayName };
          }
        } catch (error) {
          stats[tableName] = { count: 0, displayName };
        }
      }

      setDataStats(stats);
    } catch (error) {
      console.error('خطأ في تحميل إحصائيات البيانات:', error);
    }
  };

  useEffect(() => {
    loadBackups();
    loadSettings();
    loadDataStats();
  }, []);

  const loadBackups = async () => {
    try {
      setLoading(true);
      // التحقق من وجود جدول النسخ الاحتياطية
      if (db.backups) {
        const allBackups = await db.backups.toArray();
        setBackups(allBackups.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt)));
      } else {
        setBackups([]);
      }
    } catch (error) {
      console.error('خطأ في تحميل النسخ الاحتياطية:', error);
      setBackups([]);
    } finally {
      setLoading(false);
    }
  };

  const loadSettings = async () => {
    try {
      if (db.settings) {
        const setting = await db.settings.where('key').equals('backup_auto').first();
        if (setting) {
          setAutoBackup(setting.value === 'true');
        }
      }
    } catch (error) {
      console.error('خطأ في تحميل إعدادات النسخ الاحتياطي:', error);
    }
  };

  const createBackup = async () => {
    try {
      setLoading(true);

      // جمع جميع البيانات بشكل آمن
      const data = {};

      // قائمة الجداول المتاحة
      const tables = {
        users: 'users',
        accounts: 'accounts',
        customers: 'customers',
        suppliers: 'suppliers',
        items: 'items',
        salesInvoices: 'salesInvoices',
        purchaseInvoices: 'purchaseInvoices',
        journalEntries: 'journalEntries',
        contracts: 'contracts',
        workers: 'workers',
        settings: 'settings',
        accountingPeriods: 'accountingPeriods',
        laborVouchers: 'laborVouchers'
      };

      // جمع البيانات من كل جدول بشكل آمن
      for (const [key, tableName] of Object.entries(tables)) {
        data[key] = [];
        try {
          if (db[tableName] && typeof db[tableName].toArray === 'function') {
            data[key] = await db[tableName].toArray();
          }
        } catch (error) {
          console.warn(`تعذر الوصول إلى جدول ${tableName}:`, error.message);
        }
      }

      const backupData = JSON.stringify(data);
      const size = new Blob([backupData]).size;
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      
      const backup = {
        name: `backup-${timestamp}`,
        data: backupData,
        size: Math.round(size / 1024), // بالكيلوبايت
        createdAt: new Date(),
        userId: user.id
      };

      // حفظ النسخة الاحتياطية في قاعدة البيانات إذا كان الجدول متاحاً
      try {
        if (db.backups) {
          await db.backups.add(backup);
        }
      } catch (error) {
        console.warn('لا يمكن حفظ النسخة الاحتياطية في قاعدة البيانات:', error);
      }
      
      // تنزيل النسخة الاحتياطية
      const blob = new Blob([backupData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${backup.name}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      alert('تم إنشاء النسخة الاحتياطية وتنزيلها بنجاح');
      await loadBackups();
    } catch (error) {
      console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
      alert('حدث خطأ أثناء إنشاء النسخة الاحتياطية');
    } finally {
      setLoading(false);
    }
  };

  const downloadBackup = async (backup) => {
    try {
      const blob = new Blob([backup.data], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${backup.name}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('خطأ في تنزيل النسخة الاحتياطية:', error);
      alert('حدث خطأ أثناء تنزيل النسخة الاحتياطية');
    }
  };

  const restoreBackup = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    if (!confirm('هل تريد استعادة النسخة الاحتياطية؟ سيتم حذف جميع البيانات الحالية!')) {
      event.target.value = '';
      return;
    }

    try {
      setLoading(true);
      
      const text = await file.text();
      const data = JSON.parse(text);

      // مسح البيانات الحالية واستعادة النسخة الاحتياطية
      const availableTables = [];

      // التحقق من الجداول المتاحة
      if (db.users) availableTables.push(db.users);
      if (db.accounts) availableTables.push(db.accounts);
      if (db.customers) availableTables.push(db.customers);
      if (db.suppliers) availableTables.push(db.suppliers);
      if (db.items) availableTables.push(db.items);
      if (db.salesInvoices) availableTables.push(db.salesInvoices);
      if (db.purchaseInvoices) availableTables.push(db.purchaseInvoices);
      if (db.journalEntries) availableTables.push(db.journalEntries);
      if (db.contracts) availableTables.push(db.contracts);
      if (db.workers) availableTables.push(db.workers);
      if (db.settings) availableTables.push(db.settings);
      if (db.accountingPeriods) availableTables.push(db.accountingPeriods);
      if (db.laborVouchers) availableTables.push(db.laborVouchers);

      await db.transaction('rw', availableTables, async () => {
        // مسح البيانات الحالية
        try { await db.users.clear(); } catch (e) {}
        try { await db.accounts.clear(); } catch (e) {}
        try { await db.customers.clear(); } catch (e) {}
        try { await db.suppliers.clear(); } catch (e) {}
        try { await db.items.clear(); } catch (e) {}
        try { await db.salesInvoices.clear(); } catch (e) {}
        try { await db.purchaseInvoices.clear(); } catch (e) {}
        try { await db.journalEntries.clear(); } catch (e) {}
        try { await db.contracts.clear(); } catch (e) {}
        try { await db.workers.clear(); } catch (e) {}
        try { await db.settings.clear(); } catch (e) {}
        try { await db.accountingPeriods.clear(); } catch (e) {}
        try { await db.laborVouchers.clear(); } catch (e) {}

        // استعادة البيانات
        try { if (data.users && data.users.length > 0) await db.users.bulkAdd(data.users); } catch (e) {}
        try { if (data.accounts && data.accounts.length > 0) await db.accounts.bulkAdd(data.accounts); } catch (e) {}
        try { if (data.customers && data.customers.length > 0) await db.customers.bulkAdd(data.customers); } catch (e) {}
        try { if (data.suppliers && data.suppliers.length > 0) await db.suppliers.bulkAdd(data.suppliers); } catch (e) {}
        try { if (data.items && data.items.length > 0) await db.items.bulkAdd(data.items); } catch (e) {}
        try { if (data.salesInvoices && data.salesInvoices.length > 0) await db.salesInvoices.bulkAdd(data.salesInvoices); } catch (e) {}
        try { if (data.purchaseInvoices && data.purchaseInvoices.length > 0) await db.purchaseInvoices.bulkAdd(data.purchaseInvoices); } catch (e) {}
        try { if (data.journalEntries && data.journalEntries.length > 0) await db.journalEntries.bulkAdd(data.journalEntries); } catch (e) {}
        try { if (data.contracts && data.contracts.length > 0) await db.contracts.bulkAdd(data.contracts); } catch (e) {}
        try { if (data.workers && data.workers.length > 0) await db.workers.bulkAdd(data.workers); } catch (e) {}
        try { if (data.settings && data.settings.length > 0) await db.settings.bulkAdd(data.settings); } catch (e) {}
        try { if (data.accountingPeriods && data.accountingPeriods.length > 0) await db.accountingPeriods.bulkAdd(data.accountingPeriods); } catch (e) {}
        try { if (data.laborVouchers && data.laborVouchers.length > 0) await db.laborVouchers.bulkAdd(data.laborVouchers); } catch (e) {}
      });

      alert('تم استعادة النسخة الاحتياطية بنجاح');
      window.location.reload(); // إعادة تحميل الصفحة
    } catch (error) {
      console.error('خطأ في استعادة النسخة الاحتياطية:', error);
      alert('حدث خطأ أثناء استعادة النسخة الاحتياطية');
    } finally {
      setLoading(false);
      event.target.value = '';
    }
  };

  const deleteBackup = async (backupId) => {
    if (!confirm('هل تريد حذف هذه النسخة الاحتياطية؟')) return;

    try {
      if (db.backups) {
        await db.backups.delete(backupId);
        alert('تم حذف النسخة الاحتياطية بنجاح');
        await loadBackups();
      } else {
        alert('جدول النسخ الاحتياطية غير متاح');
      }
    } catch (error) {
      console.error('خطأ في حذف النسخة الاحتياطية:', error);
      alert('حدث خطأ أثناء حذف النسخة الاحتياطية');
    }
  };

  const toggleAutoBackup = async () => {
    try {
      const newValue = !autoBackup;
      setAutoBackup(newValue);

      // تحديث الإعدادات إذا كان الجدول متاحاً
      if (db.settings) {
        const setting = await db.settings.where('key').equals('backup_auto').first();
        if (setting) {
          await db.settings.update(setting.id, {
            value: newValue.toString(),
            updatedAt: new Date()
          });
        } else {
          await db.settings.add({
            key: 'backup_auto',
            value: newValue.toString(),
            description: 'النسخ الاحتياطي التلقائي',
            userId: user.id,
            updatedAt: new Date()
          });
        }
      }
    } catch (error) {
      console.error('خطأ في تحديث إعدادات النسخ الاحتياطي:', error);
    }
  };

  const deleteOldData = async () => {
    if (!confirm('هل تريد حذف البيانات القديمة؟ سيتم حذف جميع البيانات ما عدا المستخدمين والإعدادات!')) return;

    try {
      setLoading(true);

      // قائمة الجداول التي سيتم حذف بياناتها (ما عدا المستخدمين والإعدادات)
      const tablesToClear = [
        'accounts', 'customers', 'suppliers', 'items',
        'salesInvoices', 'purchaseInvoices', 'journalEntries',
        'contracts', 'workers', 'accountingPeriods', 'laborVouchers'
      ];

      // حذف البيانات من كل جدول بشكل آمن
      for (const tableName of tablesToClear) {
        try {
          if (db[tableName] && typeof db[tableName].clear === 'function') {
            await db[tableName].clear();
          }
        } catch (error) {
          console.warn(`تعذر حذف بيانات جدول ${tableName}:`, error.message);
        }
      }

      alert('تم حذف البيانات القديمة بنجاح');
      await loadDataStats(); // تحديث الإحصائيات
    } catch (error) {
      console.error('خطأ في حذف البيانات القديمة:', error);
      alert('حدث خطأ أثناء حذف البيانات القديمة');
    } finally {
      setLoading(false);
    }
  };

  const resetDatabase = async () => {
    if (!confirm('هل تريد إعادة تعيين قاعدة البيانات بالكامل؟ سيتم حذف جميع البيانات نهائياً!')) return;

    const confirmText = prompt('اكتب "إعادة تعيين" للتأكيد:');
    if (confirmText !== 'إعادة تعيين') {
      alert('تم إلغاء العملية');
      return;
    }

    try {
      setLoading(true);

      // قائمة جميع الجداول
      const allTables = [
        'users', 'accounts', 'customers', 'suppliers', 'items',
        'salesInvoices', 'purchaseInvoices', 'journalEntries',
        'contracts', 'workers', 'settings', 'accountingPeriods',
        'laborVouchers', 'backups'
      ];

      // حذف البيانات من جميع الجداول
      for (const tableName of allTables) {
        try {
          if (db[tableName] && typeof db[tableName].clear === 'function') {
            await db[tableName].clear();
          }
        } catch (error) {
          console.warn(`تعذر حذف بيانات جدول ${tableName}:`, error.message);
        }
      }

      alert('تم إعادة تعيين قاعدة البيانات بنجاح. سيتم إعادة تحميل الصفحة.');
      window.location.reload();
    } catch (error) {
      console.error('خطأ في إعادة تعيين قاعدة البيانات:', error);
      alert('حدث خطأ أثناء إعادة تعيين قاعدة البيانات');
    } finally {
      setLoading(false);
    }
  };

  const formatFileSize = (sizeInKB) => {
    if (sizeInKB < 1024) {
      return `${sizeInKB} ك.ب`;
    } else {
      return `${(sizeInKB / 1024).toFixed(1)} م.ب`;
    }
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '3rem' }}>
        <div>جاري التحميل...</div>
      </div>
    );
  }

  return (
    <div>
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        marginBottom: '2rem' 
      }}>
        <h2 style={{ margin: 0, color: '#2c3e50' }}>إدارة النسخ الاحتياطية</h2>
      </div>

      {/* إحصائيات البيانات */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div className="card-title">إحصائيات البيانات الحالية</div>

        {Object.keys(dataStats).length > 0 ? (
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem', marginBottom: '1rem' }}>
            {Object.entries(dataStats).map(([key, { count, displayName }]) => (
              <div
                key={key}
                style={{
                  padding: '0.75rem',
                  background: count > 0 ? '#e7f3ff' : '#f8f9fa',
                  border: '1px solid #dee2e6',
                  borderRadius: '5px',
                  textAlign: 'center'
                }}
              >
                <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: count > 0 ? '#0066cc' : '#6c757d' }}>
                  {count}
                </div>
                <div style={{ fontSize: '0.85rem', color: '#6c757d' }}>
                  {displayName}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div style={{ textAlign: 'center', padding: '1rem', color: '#6c757d' }}>
            جاري تحميل الإحصائيات...
          </div>
        )}

        <div style={{ textAlign: 'center', marginTop: '1rem' }}>
          <button
            className="btn btn-secondary"
            onClick={loadDataStats}
            disabled={loading}
            style={{ padding: '0.5rem 1rem' }}
          >
            🔄 تحديث الإحصائيات
          </button>
        </div>
      </div>

      {/* إعدادات النسخ الاحتياطي */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div className="card-title">إعدادات النسخ الاحتياطي</div>
        
        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginBottom: '1rem' }}>
          <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', cursor: 'pointer' }}>
            <input
              type="checkbox"
              checked={autoBackup}
              onChange={toggleAutoBackup}
            />
            <span>تفعيل النسخ الاحتياطي التلقائي</span>
          </label>
        </div>

        <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>
          <button
            className="btn btn-primary"
            onClick={createBackup}
            disabled={loading}
          >
            💾 إنشاء نسخة احتياطية
          </button>

          <label className="btn btn-success" style={{ cursor: 'pointer' }}>
            📁 استعادة نسخة احتياطية
            <input
              type="file"
              accept=".json"
              onChange={restoreBackup}
              style={{ display: 'none' }}
            />
          </label>

          <button
            className="btn btn-warning"
            onClick={deleteOldData}
            disabled={loading}
            style={{ background: '#ffc107', color: '#212529' }}
          >
            🗑️ حذف البيانات القديمة
          </button>

          <button
            className="btn btn-danger"
            onClick={resetDatabase}
            disabled={loading}
          >
            ⚠️ إعادة تعيين قاعدة البيانات
          </button>
        </div>
      </div>

      {/* قائمة النسخ الاحتياطية */}
      <div className="card">
        <div className="card-title">النسخ الاحتياطية المحفوظة ({backups.length})</div>

        {backups.length === 0 ? (
          <div style={{ textAlign: 'center', padding: '3rem', color: '#6c757d' }}>
            <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>💾</div>
            <div style={{ fontSize: '1.2rem', marginBottom: '0.5rem' }}>لا توجد نسخ احتياطية</div>
            <div>قم بإنشاء أول نسخة احتياطية</div>
          </div>
        ) : (
          <div style={{ overflow: 'auto' }}>
            <table className="table">
              <thead>
                <tr>
                  <th>اسم النسخة</th>
                  <th>الحجم</th>
                  <th>تاريخ الإنشاء</th>
                  <th>المنشئ</th>
                  <th>إجراءات</th>
                </tr>
              </thead>
              <tbody>
                {backups.map(backup => (
                  <tr key={backup.id}>
                    <td style={{ fontWeight: 'bold' }}>
                      {backup.name}
                    </td>
                    <td>{formatFileSize(backup.size)}</td>
                    <td>{new Date(backup.createdAt).toLocaleString('ar-EG')}</td>
                    <td>المستخدم {backup.userId}</td>
                    <td>
                      <div style={{ display: 'flex', gap: '0.5rem' }}>
                        <button
                          className="btn btn-info btn-sm"
                          onClick={() => downloadBackup(backup)}
                        >
                          📥 تنزيل
                        </button>
                        <button
                          className="btn btn-danger btn-sm"
                          onClick={() => deleteBackup(backup.id)}
                        >
                          🗑️ حذف
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* معلومات مهمة */}
      <div className="card" style={{ marginTop: '2rem', background: '#fff3cd', border: '1px solid #ffeaa7' }}>
        <div className="card-title" style={{ color: '#856404' }}>⚠️ معلومات مهمة</div>
        <ul style={{ margin: 0, paddingLeft: '1.5rem', color: '#856404' }}>
          <li>يتم حفظ النسخ الاحتياطية محلياً في المتصفح</li>
          <li>يُنصح بتنزيل النسخ الاحتياطية وحفظها في مكان آمن</li>
          <li>استعادة النسخة الاحتياطية سيحذف جميع البيانات الحالية</li>
          <li>تأكد من صحة النسخة الاحتياطية قبل الاستعادة</li>
        </ul>
      </div>

      {/* معلومات إضافية حول الأزرار الجديدة */}
      <div className="card" style={{ marginTop: '1rem', background: '#f8d7da', border: '1px solid #f5c6cb' }}>
        <div className="card-title" style={{ color: '#721c24' }}>🔧 أدوات إدارة البيانات</div>
        <div style={{ color: '#721c24' }}>
          <div style={{ marginBottom: '0.5rem' }}>
            <strong>🗑️ حذف البيانات القديمة:</strong> يحذف جميع البيانات ما عدا المستخدمين والإعدادات
          </div>
          <div>
            <strong>⚠️ إعادة تعيين قاعدة البيانات:</strong> يحذف جميع البيانات نهائياً ويعيد النظام لحالته الأولى
          </div>
        </div>
      </div>
    </div>
  );
};

export default BackupManagement;
