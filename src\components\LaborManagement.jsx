import React, { useState, useEffect } from 'react';
import { db } from '../database/db';
import WorkerVouchers from './WorkerVouchers';

const LaborManagement = () => {
  const [currentView, setCurrentView] = useState('workers');
  const [workers, setWorkers] = useState([]);
  const [contracts, setContracts] = useState([]);
  const [laborCosts, setLaborCosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [sourceFilter, setSourceFilter] = useState('all');
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');
  const [selectedWorker, setSelectedWorker] = useState(null);
  const [selectedContract, setSelectedContract] = useState(null);
  
  const [workerForm, setWorkerForm] = useState({
    code: '',
    name: '',
    phone: '',
    address: '',
    dailyRate: 0,
    hourlyRate: 0,
    isActive: true
  });

  const [laborForm, setLaborForm] = useState({
    workerId: '',
    contractId: '',
    date: new Date().toISOString().split('T')[0],
    hours: 0,
    days: 0,
    description: '',
    isPaid: false
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadWorkers(),
        loadContracts(),
        loadLaborCosts()
      ]);
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadWorkers = async () => {
    try {
      const allWorkers = await db.workers.orderBy('createdAt').reverse().toArray();
      setWorkers(allWorkers);
    } catch (error) {
      console.error('خطأ في تحميل العمال:', error);
    }
  };

  const loadContracts = async () => {
    try {
      const allContracts = await db.contracts.where('status').equals('active').toArray();
      setContracts(allContracts);
    } catch (error) {
      console.error('خطأ في تحميل العقود:', error);
    }
  };

  const loadLaborCosts = async () => {
    try {
      // تحميل تكاليف العمالة من الجدول المخصص
      const directLaborCosts = await db.laborCosts.orderBy('createdAt').reverse().toArray();

      // تحميل العمالة من أذون الصرف
      const voucherExpenses = await db.contractExpenses
        .where('type')
        .equals('voucher')
        .toArray();

      // استخراج العمالة من أذون الصرف
      const voucherLaborCosts = [];
      voucherExpenses.forEach(expense => {
        if (expense.items && Array.isArray(expense.items)) {
          expense.items.forEach(item => {
            if (item.type === 'labor' && item.workerId) {
              voucherLaborCosts.push({
                id: `voucher_${expense.id}_${item.id}`,
                date: expense.date,
                workerId: parseInt(item.workerId),
                contractId: expense.contractId,
                hoursWorked: item.quantity,
                hourlyRate: item.unitCost,
                totalCost: item.totalCost,
                description: `${item.description} (إذن صرف: ${expense.voucherNumber})`,
                isPaid: false,
                source: 'voucher',
                voucherNumber: expense.voucherNumber,
                createdAt: expense.createdAt || new Date(expense.date)
              });
            }
          });
        }
      });

      // دمج البيانات وترتيبها حسب التاريخ
      const allLaborCosts = [...directLaborCosts, ...voucherLaborCosts]
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

      setLaborCosts(allLaborCosts);
    } catch (error) {
      console.error('خطأ في تحميل تكاليف العمالة:', error);
    }
  };

  const generateWorkerCode = async () => {
    const count = await db.workers.count();
    return `WRK${String(count + 1).padStart(4, '0')}`;
  };

  const handleWorkerSubmit = async (e) => {
    e.preventDefault();
    
    try {
      if (!workerForm.code) {
        workerForm.code = await generateWorkerCode();
      }

      const workerData = {
        ...workerForm,
        dailyRate: parseFloat(workerForm.dailyRate) || 0,
        hourlyRate: parseFloat(workerForm.hourlyRate) || 0,
        createdAt: new Date()
      };

      if (selectedWorker) {
        await db.workers.update(selectedWorker.id, {
          ...workerData,
          updatedAt: new Date()
        });
        alert('تم تحديث بيانات العامل بنجاح');
      } else {
        await db.workers.add(workerData);
        alert('تم إضافة العامل بنجاح');
      }

      resetWorkerForm();
      loadWorkers();
    } catch (error) {
      console.error('خطأ في حفظ بيانات العامل:', error);
      alert('حدث خطأ أثناء حفظ بيانات العامل');
    }
  };

  const handleLaborSubmit = async (e) => {
    e.preventDefault();
    
    try {
      const worker = workers.find(w => w.id === parseInt(laborForm.workerId));
      if (!worker) {
        alert('يرجى اختيار عامل صحيح');
        return;
      }

      const hours = parseFloat(laborForm.hours) || 0;
      const days = parseFloat(laborForm.days) || 0;
      
      // حساب التكلفة الإجمالية
      const hoursCost = hours * worker.hourlyRate;
      const daysCost = days * worker.dailyRate;
      const totalCost = hoursCost + daysCost;

      const laborData = {
        workerId: parseInt(laborForm.workerId),
        contractId: parseInt(laborForm.contractId),
        date: laborForm.date,
        hoursWorked: hours,
        hourlyRate: worker.hourlyRate,
        totalCost: totalCost,
        description: laborForm.description,
        isPaid: laborForm.isPaid,
        userId: 1,
        createdAt: new Date()
      };

      await db.laborCosts.add(laborData);
      
      // إضافة سجل دفع للعامل
      await db.workerPayments.add({
        workerId: parseInt(laborForm.workerId),
        contractId: parseInt(laborForm.contractId),
        date: laborForm.date,
        hours: hours,
        days: days,
        amount: totalCost,
        description: laborForm.description,
        isPaid: laborForm.isPaid,
        userId: 1,
        createdAt: new Date()
      });

      alert('تم تسجيل تكلفة العمالة بنجاح');
      resetLaborForm();
      loadLaborCosts();
    } catch (error) {
      console.error('خطأ في تسجيل تكلفة العمالة:', error);
      alert('حدث خطأ أثناء تسجيل تكلفة العمالة');
    }
  };

  const resetWorkerForm = () => {
    setWorkerForm({
      code: '',
      name: '',
      phone: '',
      address: '',
      dailyRate: 0,
      hourlyRate: 0,
      isActive: true
    });
    setSelectedWorker(null);
  };

  const resetLaborForm = () => {
    setLaborForm({
      workerId: '',
      contractId: '',
      date: new Date().toISOString().split('T')[0],
      hours: 0,
      days: 0,
      description: '',
      isPaid: false
    });
  };

  const handleEditWorker = (worker) => {
    setSelectedWorker(worker);
    setWorkerForm({
      code: worker.code,
      name: worker.name,
      phone: worker.phone,
      address: worker.address,
      dailyRate: worker.dailyRate,
      hourlyRate: worker.hourlyRate,
      isActive: worker.isActive
    });
  };

  const handleDeleteWorker = async (id) => {
    if (!confirm('هل أنت متأكد من حذف هذا العامل؟')) return;
    
    try {
      await db.workers.delete(id);
      alert('تم حذف العامل بنجاح');
      loadWorkers();
    } catch (error) {
      console.error('خطأ في حذف العامل:', error);
      alert('حدث خطأ أثناء حذف العامل');
    }
  };

  const getWorkerName = (workerId) => {
    const worker = workers.find(w => w.id === workerId);
    return worker ? worker.name : 'غير محدد';
  };

  const getContractName = (contractId) => {
    const contract = contracts.find(c => c.id === contractId);
    return contract ? contract.name : 'غير محدد';
  };

  const exportToExcel = () => {
    try {
      // إنشاء البيانات للتصدير
      const exportData = filteredLaborCosts.map(cost => ({
        'التاريخ': new Date(cost.date).toLocaleDateString('ar-EG'),
        'العامل': getWorkerName(cost.workerId),
        'العقد': getContractName(cost.contractId),
        'الساعات': cost.hoursWorked,
        'التكلفة': cost.totalCost,
        'المصدر': cost.source === 'voucher' ? 'إذن صرف' : 'مباشر',
        'الحالة': cost.isPaid ? 'مدفوع' : 'غير مدفوع',
        'الوصف': cost.description || '-'
      }));

      // إضافة صف الإجمالي
      exportData.push({
        'التاريخ': '',
        'العامل': '',
        'العقد': '',
        'الساعات': 'الإجمالي:',
        'التكلفة': filteredLaborCosts.reduce((sum, cost) => sum + (cost.totalCost || 0), 0),
        'المصدر': '',
        'الحالة': '',
        'الوصف': ''
      });

      // تحويل إلى CSV
      const headers = Object.keys(exportData[0]);
      const csvContent = [
        headers.join(','),
        ...exportData.map(row =>
          headers.map(header => `"${row[header]}"`).join(',')
        )
      ].join('\n');

      // تحميل الملف
      const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `تكاليف_العمالة_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      alert('تم تصدير البيانات بنجاح');
    } catch (error) {
      console.error('خطأ في التصدير:', error);
      alert('حدث خطأ أثناء تصدير البيانات');
    }
  };

  const printReport = () => {
    try {
      const printWindow = window.open('', '_blank');
      const currentDate = new Date().toLocaleDateString('ar-EG');

      const printContent = `
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
          <meta charset="UTF-8">
          <title>تقرير تكاليف العمالة</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .header h1 { color: #333; margin-bottom: 10px; }
            .header p { color: #666; margin: 5px 0; }
            .filters { background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
            .filters h3 { margin-top: 0; color: #495057; }
            table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
            th { background-color: #f8f9fa; font-weight: bold; }
            .total-row { background-color: #e9ecef; font-weight: bold; }
            .summary { background: #f8f9fa; padding: 15px; border-radius: 5px; }
            .summary h3 { margin-top: 0; color: #495057; }
            @media print {
              body { margin: 0; }
              .no-print { display: none; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>تقرير تكاليف العمالة</h1>
            <p>تاريخ التقرير: ${currentDate}</p>
            <p>عدد السجلات: ${filteredLaborCosts.length}</p>
          </div>

          <div class="filters">
            <h3>فلاتر التقرير:</h3>
            <p><strong>البحث:</strong> ${searchTerm || 'غير محدد'}</p>
            <p><strong>الحالة:</strong> ${statusFilter === 'all' ? 'جميع الحالات' : statusFilter === 'paid' ? 'مدفوع' : 'غير مدفوع'}</p>
            <p><strong>المصدر:</strong> ${sourceFilter === 'all' ? 'جميع المصادر' : sourceFilter === 'direct' ? 'مباشر' : 'إذن صرف'}</p>
            <p><strong>من تاريخ:</strong> ${dateFrom || 'غير محدد'}</p>
            <p><strong>إلى تاريخ:</strong> ${dateTo || 'غير محدد'}</p>
          </div>

          <table>
            <thead>
              <tr>
                <th>التاريخ</th>
                <th>العامل</th>
                <th>العقد</th>
                <th>الساعات</th>
                <th>التكلفة</th>
                <th>المصدر</th>
                <th>الحالة</th>
                <th>الوصف</th>
              </tr>
            </thead>
            <tbody>
              ${filteredLaborCosts.map(cost => `
                <tr>
                  <td>${new Date(cost.date).toLocaleDateString('ar-EG')}</td>
                  <td>${getWorkerName(cost.workerId)}</td>
                  <td>${getContractName(cost.contractId)}</td>
                  <td>${cost.hoursWorked}</td>
                  <td>${cost.totalCost.toLocaleString('ar-EG')} ج.م</td>
                  <td>${cost.source === 'voucher' ? 'إذن صرف' : 'مباشر'}</td>
                  <td>${cost.isPaid ? 'مدفوع' : 'غير مدفوع'}</td>
                  <td>${cost.description || '-'}</td>
                </tr>
              `).join('')}
              <tr class="total-row">
                <td colspan="4">إجمالي تكاليف العمالة:</td>
                <td>${filteredLaborCosts.reduce((sum, cost) => sum + (cost.totalCost || 0), 0).toLocaleString('ar-EG')} ج.م</td>
                <td colspan="3"></td>
              </tr>
            </tbody>
          </table>

          <div class="summary">
            <h3>ملخص التكاليف:</h3>
            <p><strong>إجمالي التكاليف:</strong> ${filteredLaborCosts.reduce((sum, cost) => sum + (cost.totalCost || 0), 0).toLocaleString('ar-EG')} ج.م</p>
            <p><strong>التكاليف المباشرة:</strong> ${filteredLaborCosts.filter(c => c.source !== 'voucher').reduce((sum, cost) => sum + (cost.totalCost || 0), 0).toLocaleString('ar-EG')} ج.م</p>
            <p><strong>تكاليف أذون الصرف:</strong> ${filteredLaborCosts.filter(c => c.source === 'voucher').reduce((sum, cost) => sum + (cost.totalCost || 0), 0).toLocaleString('ar-EG')} ج.م</p>
            <p><strong>المدفوع:</strong> ${filteredLaborCosts.filter(c => c.isPaid).reduce((sum, cost) => sum + (cost.totalCost || 0), 0).toLocaleString('ar-EG')} ج.م</p>
            <p><strong>غير المدفوع:</strong> ${filteredLaborCosts.filter(c => !c.isPaid).reduce((sum, cost) => sum + (cost.totalCost || 0), 0).toLocaleString('ar-EG')} ج.م</p>
          </div>
        </body>
        </html>
      `;

      printWindow.document.write(printContent);
      printWindow.document.close();
      printWindow.focus();

      setTimeout(() => {
        printWindow.print();
      }, 250);
    } catch (error) {
      console.error('خطأ في الطباعة:', error);
      alert('حدث خطأ أثناء إعداد الطباعة');
    }
  };

  const filteredWorkers = workers.filter(worker =>
    worker.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    worker.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
    worker.phone.includes(searchTerm)
  );

  const filteredLaborCosts = laborCosts.filter(cost => {
    const workerName = getWorkerName(cost.workerId);
    const contractName = getContractName(cost.contractId);

    // فلتر النص
    const textMatch = searchTerm === '' ||
      workerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      contractName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (cost.description && cost.description.toLowerCase().includes(searchTerm.toLowerCase()));

    // فلتر الحالة
    const statusMatch = statusFilter === 'all' ||
      (statusFilter === 'paid' && cost.isPaid) ||
      (statusFilter === 'unpaid' && !cost.isPaid);

    // فلتر المصدر
    const sourceMatch = sourceFilter === 'all' ||
      (sourceFilter === 'direct' && cost.source !== 'voucher') ||
      (sourceFilter === 'voucher' && cost.source === 'voucher');

    // فلتر التاريخ
    const costDate = new Date(cost.date);
    const fromMatch = !dateFrom || costDate >= new Date(dateFrom);
    const toMatch = !dateTo || costDate <= new Date(dateTo);

    return textMatch && statusMatch && sourceMatch && fromMatch && toMatch;
  });

  if (loading) {
    return (
      <div className="container">
        <div className="loading">
          <div className="spinner"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container">
      {/* Header */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h2 style={{ margin: 0, color: '#333' }}>إدارة العمالة وتتبع التكاليف</h2>
        </div>
      </div>

      {/* Navigation */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div style={{ display: 'flex', gap: '1rem', marginBottom: '1rem' }}>
          <button
            className={`btn ${currentView === 'workers' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setCurrentView('workers')}
          >
            👷 إدارة العمال
          </button>
          <button
            className={`btn ${currentView === 'labor-costs' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setCurrentView('labor-costs')}
          >
            💰 تكاليف العمالة
          </button>
          <button
            className={`btn ${currentView === 'add-labor' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setCurrentView('add-labor')}
          >
            ➕ تسجيل عمالة
          </button>
          <button
            className={`btn ${currentView === 'worker-vouchers' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => {
              console.log('تم النقر على تبويب أذون العمال');
              setCurrentView('worker-vouchers');
            }}
          >
            📋 أذون العمال
          </button>
        </div>

        <div style={{ marginTop: '1rem', padding: '0.5rem', background: '#f8f9fa', borderRadius: '3px' }}>
          <strong>العرض الحالي:</strong> {currentView}
        </div>

        {/* Search and Filters */}
        <div className="form-group">
          <label className="form-label">البحث والفلترة</label>
          <div className="grid grid-2">
            <input
              type="text"
              className="form-control"
              placeholder="ابحث بالاسم أو الكود أو رقم الهاتف..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />

            {currentView === 'labor-costs' && (
              <div style={{ display: 'flex', gap: '1rem' }}>
                <button
                  className="btn btn-success"
                  onClick={() => exportToExcel()}
                  style={{ padding: '0.5rem 1rem' }}
                >
                  📊 تصدير Excel
                </button>
                <button
                  className="btn btn-info"
                  onClick={() => printReport()}
                  style={{ padding: '0.5rem 1rem' }}
                >
                  🖨️ طباعة
                </button>
              </div>
            )}
          </div>

          {/* Advanced Filters for Labor Costs */}
          {currentView === 'labor-costs' && (
            <div style={{ marginTop: '1rem', padding: '1rem', background: '#f8f9fa', borderRadius: '5px' }}>
              <div className="grid grid-4">
                <div className="form-group">
                  <label className="form-label">الحالة</label>
                  <select
                    className="form-control"
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                  >
                    <option value="all">جميع الحالات</option>
                    <option value="paid">مدفوع</option>
                    <option value="unpaid">غير مدفوع</option>
                  </select>
                </div>

                <div className="form-group">
                  <label className="form-label">المصدر</label>
                  <select
                    className="form-control"
                    value={sourceFilter}
                    onChange={(e) => setSourceFilter(e.target.value)}
                  >
                    <option value="all">جميع المصادر</option>
                    <option value="direct">مباشر</option>
                    <option value="voucher">إذن صرف</option>
                  </select>
                </div>

                <div className="form-group">
                  <label className="form-label">من تاريخ</label>
                  <input
                    type="date"
                    className="form-control"
                    value={dateFrom}
                    onChange={(e) => setDateFrom(e.target.value)}
                  />
                </div>

                <div className="form-group">
                  <label className="form-label">إلى تاريخ</label>
                  <input
                    type="date"
                    className="form-control"
                    value={dateTo}
                    onChange={(e) => setDateTo(e.target.value)}
                  />
                </div>
              </div>

              <div style={{ marginTop: '1rem', display: 'flex', gap: '1rem' }}>
                <button
                  className="btn btn-secondary"
                  onClick={() => {
                    setStatusFilter('all');
                    setSourceFilter('all');
                    setDateFrom('');
                    setDateTo('');
                    setSearchTerm('');
                  }}
                  style={{ padding: '0.5rem 1rem' }}
                >
                  🔄 إعادة تعيين الفلاتر
                </button>

                <div style={{
                  padding: '0.5rem 1rem',
                  background: '#e9ecef',
                  borderRadius: '4px',
                  fontSize: '0.9rem',
                  color: '#495057'
                }}>
                  📊 النتائج: {filteredLaborCosts.length} من {laborCosts.length}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Workers Management */}
      {currentView === 'workers' && (
        <div className="card">
          <div className="card-title">
            إدارة العمال ({filteredWorkers.length})
          </div>

          {/* Add Worker Form */}
          <div style={{ marginBottom: '2rem', padding: '1rem', background: '#f8f9fa', borderRadius: '5px' }}>
            <h4 style={{ margin: '0 0 1rem 0', fontSize: '1rem' }}>
              {selectedWorker ? 'تعديل بيانات العامل' : 'إضافة عامل جديد'}
            </h4>
            
            <form onSubmit={handleWorkerSubmit}>
              <div className="grid grid-3">
                <div className="form-group">
                  <label className="form-label">كود العامل</label>
                  <input
                    type="text"
                    className="form-control"
                    value={workerForm.code}
                    onChange={(e) => setWorkerForm({...workerForm, code: e.target.value})}
                    placeholder="سيتم إنشاؤه تلقائياً"
                  />
                </div>

                <div className="form-group">
                  <label className="form-label">اسم العامل *</label>
                  <input
                    type="text"
                    className="form-control"
                    value={workerForm.name}
                    onChange={(e) => setWorkerForm({...workerForm, name: e.target.value})}
                    required
                  />
                </div>

                <div className="form-group">
                  <label className="form-label">رقم الهاتف</label>
                  <input
                    type="text"
                    className="form-control"
                    value={workerForm.phone}
                    onChange={(e) => setWorkerForm({...workerForm, phone: e.target.value})}
                  />
                </div>

                <div className="form-group">
                  <label className="form-label">العنوان</label>
                  <input
                    type="text"
                    className="form-control"
                    value={workerForm.address}
                    onChange={(e) => setWorkerForm({...workerForm, address: e.target.value})}
                  />
                </div>

                <div className="form-group">
                  <label className="form-label">الأجر اليومي</label>
                  <input
                    type="number"
                    className="form-control"
                    value={workerForm.dailyRate}
                    onChange={(e) => setWorkerForm({...workerForm, dailyRate: e.target.value})}
                    min="0"
                    step="0.01"
                  />
                </div>

                <div className="form-group">
                  <label className="form-label">الأجر بالساعة</label>
                  <input
                    type="number"
                    className="form-control"
                    value={workerForm.hourlyRate}
                    onChange={(e) => setWorkerForm({...workerForm, hourlyRate: e.target.value})}
                    min="0"
                    step="0.01"
                  />
                </div>
              </div>

              <div className="form-group">
                <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                  <input
                    type="checkbox"
                    checked={workerForm.isActive}
                    onChange={(e) => setWorkerForm({...workerForm, isActive: e.target.checked})}
                  />
                  <span>نشط</span>
                </label>
              </div>

              <div style={{ display: 'flex', gap: '1rem', marginTop: '1rem' }}>
                <button type="submit" className="btn btn-primary">
                  {selectedWorker ? 'تحديث البيانات' : 'إضافة العامل'}
                </button>
                {selectedWorker && (
                  <button 
                    type="button" 
                    className="btn btn-secondary"
                    onClick={resetWorkerForm}
                  >
                    إلغاء التعديل
                  </button>
                )}
              </div>
            </form>
          </div>

          {/* Workers List */}
          {filteredWorkers.length > 0 ? (
            <div style={{ overflow: 'auto' }}>
              <table className="table">
                <thead>
                  <tr>
                    <th>الكود</th>
                    <th>الاسم</th>
                    <th>الهاتف</th>
                    <th>الأجر اليومي</th>
                    <th>الأجر بالساعة</th>
                    <th>الحالة</th>
                    <th>الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredWorkers.map(worker => (
                    <tr key={worker.id}>
                      <td><strong>{worker.code}</strong></td>
                      <td>{worker.name}</td>
                      <td>{worker.phone}</td>
                      <td>{worker.dailyRate.toLocaleString('ar-EG')} ج.م</td>
                      <td>{worker.hourlyRate.toLocaleString('ar-EG')} ج.م</td>
                      <td>
                        <span style={{
                          padding: '0.25rem 0.5rem',
                          borderRadius: '4px',
                          fontSize: '0.8rem',
                          background: worker.isActive ? '#d4edda' : '#f8d7da',
                          color: worker.isActive ? '#155724' : '#721c24'
                        }}>
                          {worker.isActive ? 'نشط' : 'غير نشط'}
                        </span>
                      </td>
                      <td>
                        <div style={{ display: 'flex', gap: '0.5rem' }}>
                          <button
                            className="btn btn-primary"
                            style={{ padding: '0.25rem 0.5rem', fontSize: '0.8rem' }}
                            onClick={() => handleEditWorker(worker)}
                          >
                            تعديل
                          </button>
                          <button
                            className="btn btn-danger"
                            style={{ padding: '0.25rem 0.5rem', fontSize: '0.8rem' }}
                            onClick={() => handleDeleteWorker(worker.id)}
                          >
                            حذف
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div style={{ textAlign: 'center', color: '#666', padding: '2rem' }}>
              لا توجد عمال مسجلين
            </div>
          )}
        </div>
      )}

      {/* Add Labor Cost */}
      {currentView === 'add-labor' && (
        <div className="card">
          <div className="card-title">تسجيل تكلفة عمالة</div>
          
          <form onSubmit={handleLaborSubmit}>
            <div className="grid grid-2">
              <div className="form-group">
                <label className="form-label">العامل *</label>
                <select
                  className="form-control"
                  value={laborForm.workerId}
                  onChange={(e) => setLaborForm({...laborForm, workerId: e.target.value})}
                  required
                >
                  <option value="">اختر العامل...</option>
                  {workers.filter(w => w.isActive).map(worker => (
                    <option key={worker.id} value={worker.id}>
                      {worker.code} - {worker.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label className="form-label">العقد *</label>
                <select
                  className="form-control"
                  value={laborForm.contractId}
                  onChange={(e) => setLaborForm({...laborForm, contractId: e.target.value})}
                  required
                >
                  <option value="">اختر العقد...</option>
                  {contracts.map(contract => (
                    <option key={contract.id} value={contract.id}>
                      {contract.contractNumber} - {contract.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label className="form-label">التاريخ *</label>
                <input
                  type="date"
                  className="form-control"
                  value={laborForm.date}
                  onChange={(e) => setLaborForm({...laborForm, date: e.target.value})}
                  required
                />
              </div>

              <div className="form-group">
                <label className="form-label">عدد الساعات</label>
                <input
                  type="number"
                  className="form-control"
                  value={laborForm.hours}
                  onChange={(e) => setLaborForm({...laborForm, hours: e.target.value})}
                  min="0"
                  step="0.5"
                />
              </div>

              <div className="form-group">
                <label className="form-label">عدد الأيام</label>
                <input
                  type="number"
                  className="form-control"
                  value={laborForm.days}
                  onChange={(e) => setLaborForm({...laborForm, days: e.target.value})}
                  min="0"
                  step="0.5"
                />
              </div>

              <div className="form-group">
                <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                  <input
                    type="checkbox"
                    checked={laborForm.isPaid}
                    onChange={(e) => setLaborForm({...laborForm, isPaid: e.target.checked})}
                  />
                  <span>تم الدفع</span>
                </label>
              </div>
            </div>

            <div className="form-group">
              <label className="form-label">وصف العمل</label>
              <textarea
                className="form-control"
                rows="3"
                value={laborForm.description}
                onChange={(e) => setLaborForm({...laborForm, description: e.target.value})}
                placeholder="وصف العمل المنجز..."
              />
            </div>

            {/* Cost Preview */}
            {laborForm.workerId && (
              <div style={{ 
                marginTop: '1rem', 
                padding: '1rem', 
                background: '#f8f9fa', 
                borderRadius: '5px' 
              }}>
                <h4 style={{ margin: '0 0 1rem 0', fontSize: '1rem' }}>معاينة التكلفة:</h4>
                {(() => {
                  const worker = workers.find(w => w.id === parseInt(laborForm.workerId));
                  if (!worker) return null;
                  
                  const hours = parseFloat(laborForm.hours) || 0;
                  const days = parseFloat(laborForm.days) || 0;
                  const hoursCost = hours * worker.hourlyRate;
                  const daysCost = days * worker.dailyRate;
                  const totalCost = hoursCost + daysCost;
                  
                  return (
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <span>تكلفة الساعات ({hours} × {worker.hourlyRate}):</span>
                        <span>{hoursCost.toLocaleString('ar-EG')} ج.م</span>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <span>تكلفة الأيام ({days} × {worker.dailyRate}):</span>
                        <span>{daysCost.toLocaleString('ar-EG')} ج.م</span>
                      </div>
                      <div style={{ 
                        display: 'flex', 
                        justifyContent: 'space-between', 
                        fontWeight: 'bold',
                        borderTop: '1px solid #ddd',
                        paddingTop: '0.5rem',
                        marginTop: '0.5rem'
                      }}>
                        <span>إجمالي التكلفة:</span>
                        <span>{totalCost.toLocaleString('ar-EG')} ج.م</span>
                      </div>
                    </div>
                  );
                })()}
              </div>
            )}

            <div style={{ display: 'flex', gap: '1rem', marginTop: '2rem' }}>
              <button type="submit" className="btn btn-primary">
                تسجيل التكلفة
              </button>
              <button 
                type="button" 
                className="btn btn-secondary"
                onClick={resetLaborForm}
              >
                إعادة تعيين
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Labor Costs List */}
      {currentView === 'labor-costs' && (
        <div className="card">
          <div className="card-title">
            تكاليف العمالة ({filteredLaborCosts.length})
          </div>
          
          {filteredLaborCosts.length > 0 ? (
            <div style={{ overflow: 'auto' }}>
              <table className="table">
                <thead>
                  <tr>
                    <th>التاريخ</th>
                    <th>العامل</th>
                    <th>العقد</th>
                    <th>الساعات</th>
                    <th>التكلفة</th>
                    <th>المصدر</th>
                    <th>الحالة</th>
                    <th>الوصف</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredLaborCosts.map(cost => (
                    <tr key={cost.id}>
                      <td>{new Date(cost.date).toLocaleDateString('ar-EG')}</td>
                      <td>{getWorkerName(cost.workerId)}</td>
                      <td>{getContractName(cost.contractId)}</td>
                      <td>{cost.hoursWorked}</td>
                      <td style={{ fontWeight: 'bold', color: '#007bff' }}>
                        {cost.totalCost.toLocaleString('ar-EG')} ج.م
                      </td>
                      <td>
                        <span style={{
                          padding: '0.25rem 0.5rem',
                          borderRadius: '4px',
                          fontSize: '0.8rem',
                          background: cost.source === 'voucher' ? '#e7f3ff' : '#f8f9fa',
                          color: cost.source === 'voucher' ? '#0066cc' : '#495057',
                          border: cost.source === 'voucher' ? '1px solid #b3d9ff' : '1px solid #dee2e6'
                        }}>
                          {cost.source === 'voucher' ? '📄 إذن صرف' : '📝 مباشر'}
                        </span>
                      </td>
                      <td>
                        <span style={{
                          padding: '0.25rem 0.5rem',
                          borderRadius: '4px',
                          fontSize: '0.8rem',
                          background: cost.isPaid ? '#d4edda' : '#fff3cd',
                          color: cost.isPaid ? '#155724' : '#856404'
                        }}>
                          {cost.isPaid ? 'مدفوع' : 'غير مدفوع'}
                        </span>
                      </td>
                      <td>{cost.description || '-'}</td>
                    </tr>
                  ))}
                </tbody>
                <tfoot>
                  <tr style={{ background: '#f8f9fa', fontWeight: 'bold' }}>
                    <td colSpan="4">إجمالي تكاليف العمالة:</td>
                    <td style={{ color: '#007bff', fontSize: '1.1rem' }}>
                      {filteredLaborCosts.reduce((sum, cost) => sum + (cost.totalCost || 0), 0).toLocaleString('ar-EG')} ج.م
                    </td>
                    <td colSpan="3">
                      <div style={{ fontSize: '0.8rem', color: '#666' }}>
                        مباشر: {filteredLaborCosts.filter(c => c.source !== 'voucher').reduce((sum, cost) => sum + (cost.totalCost || 0), 0).toLocaleString('ar-EG')} ج.م
                        {' | '}
                        أذون صرف: {filteredLaborCosts.filter(c => c.source === 'voucher').reduce((sum, cost) => sum + (cost.totalCost || 0), 0).toLocaleString('ar-EG')} ج.م
                      </div>
                    </td>
                  </tr>
                </tfoot>
              </table>
            </div>
          ) : (
            <div style={{ textAlign: 'center', color: '#666', padding: '2rem' }}>
              لا توجد تكاليف عمالة مسجلة
            </div>
          )}
        </div>
      )}

      {/* Worker Vouchers */}
      {currentView === 'worker-vouchers' && (
        <div>
          <div style={{ background: '#e3f2fd', padding: '1rem', marginBottom: '1rem', borderRadius: '5px' }}>
            <strong>تشخيص:</strong> تم تحميل مكون أذون العمال - العرض الحالي: {currentView}
          </div>
          <WorkerVouchers onRefresh={loadData} />
        </div>
      )}
    </div>
  );
};

export default LaborManagement;
