import React, { useState, useEffect } from 'react';
import { db, dbHelpers } from '../database/db';

const InventoryReports = () => {
  const [loading, setLoading] = useState(false);
  const [currentReport, setCurrentReport] = useState('stock-status');
  const [reportData, setReportData] = useState({});
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');

  const reportTypes = {
    'stock-status': 'تقرير حالة المخزون',
    'stock-movements': 'تقرير حركات المخزون',
    'low-stock': 'تقرير المخزون المنخفض',
    'stock-valuation': 'تقرير تقييم المخزون',
    'abc-analysis': 'تحليل ABC للمخزون'
  };

  useEffect(() => {
    // تعيين التواريخ الافتراضية (آخر شهر)
    const today = new Date();
    const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
    setDateFrom(lastMonth.toISOString().split('T')[0]);
    setDateTo(today.toISOString().split('T')[0]);
  }, []);

  useEffect(() => {
    if (dateFrom && dateTo) {
      generateReport();
    }
  }, [currentReport, dateFrom, dateTo]);

  const generateReport = async () => {
    try {
      setLoading(true);
      
      switch (currentReport) {
        case 'stock-status':
          await generateStockStatusReport();
          break;
        case 'stock-movements':
          await generateStockMovementsReport();
          break;
        case 'low-stock':
          await generateLowStockReport();
          break;
        case 'stock-valuation':
          await generateStockValuationReport();
          break;
        case 'abc-analysis':
          await generateABCAnalysisReport();
          break;
      }
    } catch (error) {
      console.error('خطأ في إنشاء التقرير:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateStockStatusReport = async () => {
    const items = await db.items.toArray();
    const itemsWithStock = await Promise.all(
      items.map(async (item) => {
        const currentStock = await dbHelpers.getItemStock(item.id);
        const currentCost = await dbHelpers.getItemCost(item.id);
        return {
          ...item,
          currentStock,
          currentCost,
          totalValue: currentStock * currentCost,
          status: currentStock === 0 ? 'نفد' : 
                 currentStock <= item.minStock ? 'منخفض' : 'متوفر'
        };
      })
    );

    const summary = {
      totalItems: items.length,
      inStock: itemsWithStock.filter(item => item.currentStock > 0).length,
      outOfStock: itemsWithStock.filter(item => item.currentStock === 0).length,
      lowStock: itemsWithStock.filter(item => item.currentStock > 0 && item.currentStock <= item.minStock).length,
      totalValue: itemsWithStock.reduce((sum, item) => sum + item.totalValue, 0)
    };

    setReportData({
      type: 'stock-status',
      items: itemsWithStock,
      summary
    });
  };

  const generateStockMovementsReport = async () => {
    const fromDate = new Date(dateFrom);
    const toDate = new Date(dateTo);
    
    const movements = await db.stockMovements
      .where('date')
      .between(fromDate, toDate, true, true)
      .toArray();

    const items = await db.items.toArray();
    const itemsMap = items.reduce((map, item) => {
      map[item.id] = item;
      return map;
    }, {});

    const movementsWithItems = movements.map(movement => ({
      ...movement,
      itemName: itemsMap[movement.itemId]?.name || 'غير معروف',
      itemCode: itemsMap[movement.itemId]?.code || 'غير معروف'
    }));

    const summary = {
      totalMovements: movements.length,
      inMovements: movements.filter(m => m.type === 'in').length,
      outMovements: movements.filter(m => m.type === 'out').length,
      totalInQuantity: movements.filter(m => m.type === 'in').reduce((sum, m) => sum + m.quantity, 0),
      totalOutQuantity: movements.filter(m => m.type === 'out').reduce((sum, m) => sum + m.quantity, 0)
    };

    setReportData({
      type: 'stock-movements',
      movements: movementsWithItems,
      summary
    });
  };

  const generateLowStockReport = async () => {
    const items = await db.items.toArray();
    const lowStockItems = [];

    for (const item of items) {
      const currentStock = await dbHelpers.getItemStock(item.id);
      if (currentStock <= item.minStock) {
        const currentCost = await dbHelpers.getItemCost(item.id);
        lowStockItems.push({
          ...item,
          currentStock,
          currentCost,
          shortage: Math.max(0, item.minStock - currentStock),
          reorderValue: Math.max(0, item.minStock - currentStock) * currentCost
        });
      }
    }

    const summary = {
      totalLowStockItems: lowStockItems.length,
      totalShortage: lowStockItems.reduce((sum, item) => sum + item.shortage, 0),
      totalReorderValue: lowStockItems.reduce((sum, item) => sum + item.reorderValue, 0)
    };

    setReportData({
      type: 'low-stock',
      items: lowStockItems,
      summary
    });
  };

  const generateStockValuationReport = async () => {
    const items = await db.items.toArray();
    const categories = {};
    let totalValue = 0;

    for (const item of items) {
      const currentStock = await dbHelpers.getItemStock(item.id);
      const currentCost = await dbHelpers.getItemCost(item.id);
      const itemValue = currentStock * currentCost;
      
      totalValue += itemValue;
      
      const category = item.category || 'غير مصنف';
      if (!categories[category]) {
        categories[category] = {
          name: category,
          items: 0,
          totalValue: 0,
          totalQuantity: 0
        };
      }
      
      categories[category].items++;
      categories[category].totalValue += itemValue;
      categories[category].totalQuantity += currentStock;
    }

    setReportData({
      type: 'stock-valuation',
      categories: Object.values(categories),
      totalValue,
      totalItems: items.length
    });
  };

  const generateABCAnalysisReport = async () => {
    const items = await db.items.toArray();
    const itemsWithValue = [];

    for (const item of items) {
      const currentStock = await dbHelpers.getItemStock(item.id);
      const currentCost = await dbHelpers.getItemCost(item.id);
      const totalValue = currentStock * currentCost;
      
      itemsWithValue.push({
        ...item,
        currentStock,
        currentCost,
        totalValue
      });
    }

    // ترتيب حسب القيمة
    itemsWithValue.sort((a, b) => b.totalValue - a.totalValue);
    
    const totalValue = itemsWithValue.reduce((sum, item) => sum + item.totalValue, 0);
    let cumulativeValue = 0;
    
    const classifiedItems = itemsWithValue.map(item => {
      cumulativeValue += item.totalValue;
      const percentage = (cumulativeValue / totalValue) * 100;
      
      let classification = 'C';
      if (percentage <= 80) classification = 'A';
      else if (percentage <= 95) classification = 'B';
      
      return {
        ...item,
        classification,
        valuePercentage: (item.totalValue / totalValue) * 100,
        cumulativePercentage: percentage
      };
    });

    const summary = {
      classA: classifiedItems.filter(item => item.classification === 'A').length,
      classB: classifiedItems.filter(item => item.classification === 'B').length,
      classC: classifiedItems.filter(item => item.classification === 'C').length,
      totalItems: items.length
    };

    setReportData({
      type: 'abc-analysis',
      items: classifiedItems,
      summary
    });
  };

  const exportReport = () => {
    try {
      const reportContent = generateReportContent();
      
      const printWindow = window.open('', '_blank');
      printWindow.document.write(reportContent);
      printWindow.document.close();
      printWindow.focus();
      
      setTimeout(() => {
        printWindow.print();
      }, 250);
    } catch (error) {
      console.error('خطأ في تصدير التقرير:', error);
      alert('حدث خطأ أثناء تصدير التقرير');
    }
  };

  const exportToCSV = () => {
    try {
      let csvData = [];
      
      switch (reportData.type) {
        case 'stock-status':
          csvData = reportData.items.map(item => ({
            'كود الصنف': item.code,
            'اسم الصنف': item.name,
            'الوحدة': item.unit,
            'الفئة': item.category || '',
            'الرصيد الحالي': item.currentStock,
            'التكلفة': item.currentCost,
            'إجمالي القيمة': item.totalValue,
            'الحد الأدنى': item.minStock,
            'الحالة': item.status
          }));
          break;
        case 'stock-movements':
          csvData = reportData.movements.map(movement => ({
            'التاريخ': new Date(movement.date).toLocaleDateString('ar-EG'),
            'كود الصنف': movement.itemCode,
            'اسم الصنف': movement.itemName,
            'نوع الحركة': movement.type === 'in' ? 'وارد' : 'صادر',
            'الكمية': movement.quantity,
            'التكلفة': movement.cost || 0,
            'المرجع': movement.reference || '',
            'الملاحظات': movement.notes || ''
          }));
          break;
      }

      if (csvData.length === 0) return;

      const headers = Object.keys(csvData[0]);
      const csvContent = [
        headers.join(','),
        ...csvData.map(row => 
          headers.map(header => `"${row[header]}"`).join(',')
        )
      ].join('\n');

      const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${reportTypes[currentReport]}_${new Date().toISOString().split('T')[0]}.csv`;
      link.click();
      URL.revokeObjectURL(url);

      alert('تم تصدير التقرير بنجاح');
    } catch (error) {
      console.error('خطأ في تصدير التقرير:', error);
      alert('حدث خطأ أثناء تصدير التقرير');
    }
  };

  const generateReportContent = () => {
    const currentDate = new Date().toLocaleDateString('ar-EG');
    const reportTitle = reportTypes[currentReport];
    
    return `
      <!DOCTYPE html>
      <html dir="rtl">
      <head>
        <meta charset="UTF-8">
        <title>${reportTitle}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .header { text-align: center; margin-bottom: 30px; }
          .header h1 { color: #333; margin-bottom: 10px; }
          .header p { color: #666; margin: 5px 0; }
          .summary { background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
          .summary h3 { margin-top: 0; color: #495057; }
          table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
          th { background-color: #f8f9fa; font-weight: bold; }
          @media print {
            body { margin: 0; }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>${reportTitle}</h1>
          <p>من ${dateFrom} إلى ${dateTo}</p>
          <p>تاريخ التقرير: ${currentDate}</p>
        </div>
        ${renderReportContent()}
      </body>
      </html>
    `;
  };

  const renderReportContent = () => {
    if (!reportData.type) return '';

    switch (reportData.type) {
      case 'stock-status':
        return `
          <div class="summary">
            <h3>ملخص حالة المخزون</h3>
            <p>إجمالي الأصناف: ${reportData.summary.totalItems}</p>
            <p>متوفر: ${reportData.summary.inStock}</p>
            <p>نفد من المخزون: ${reportData.summary.outOfStock}</p>
            <p>مخزون منخفض: ${reportData.summary.lowStock}</p>
            <p>إجمالي قيمة المخزون: ${reportData.summary.totalValue.toLocaleString('ar-EG')} ج.م</p>
          </div>
        `;
      default:
        return '<p>لا توجد بيانات متاحة</p>';
    }
  };

  if (loading) {
    return (
      <div className="card">
        <div style={{ textAlign: 'center', padding: '2rem' }}>
          <div className="spinner"></div>
          <div>جاري إنشاء التقرير...</div>
        </div>
      </div>
    );
  }

  const renderReportDisplay = () => {
    if (!reportData.type) return null;

    switch (reportData.type) {
      case 'stock-status':
        return (
          <div>
            {/* ملخص */}
            <div style={{ marginBottom: '2rem' }}>
              <h4 style={{ marginBottom: '1rem', color: '#495057' }}>ملخص حالة المخزون</h4>
              <div className="grid grid-4">
                <div className="stat-card">
                  <div className="stat-icon">📦</div>
                  <div className="stat-content">
                    <div className="stat-number">{reportData.summary.totalItems}</div>
                    <div className="stat-label">إجمالي الأصناف</div>
                  </div>
                </div>
                <div className="stat-card">
                  <div className="stat-icon">✅</div>
                  <div className="stat-content">
                    <div className="stat-number">{reportData.summary.inStock}</div>
                    <div className="stat-label">متوفر</div>
                  </div>
                </div>
                <div className="stat-card">
                  <div className="stat-icon">❌</div>
                  <div className="stat-content">
                    <div className="stat-number">{reportData.summary.outOfStock}</div>
                    <div className="stat-label">نفد من المخزون</div>
                  </div>
                </div>
                <div className="stat-card">
                  <div className="stat-icon">⚠️</div>
                  <div className="stat-content">
                    <div className="stat-number">{reportData.summary.lowStock}</div>
                    <div className="stat-label">مخزون منخفض</div>
                  </div>
                </div>
              </div>
              <div style={{ textAlign: 'center', marginTop: '1rem' }}>
                <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#007bff' }}>
                  إجمالي قيمة المخزون: {reportData.summary.totalValue.toLocaleString('ar-EG')} ج.م
                </div>
              </div>
            </div>

            {/* جدول الأصناف */}
            <div style={{ overflow: 'auto' }}>
              <table className="table">
                <thead>
                  <tr>
                    <th>الكود</th>
                    <th>اسم الصنف</th>
                    <th>الوحدة</th>
                    <th>الفئة</th>
                    <th>الرصيد الحالي</th>
                    <th>الحد الأدنى</th>
                    <th>التكلفة</th>
                    <th>إجمالي القيمة</th>
                    <th>الحالة</th>
                  </tr>
                </thead>
                <tbody>
                  {reportData.items.map(item => (
                    <tr key={item.id}>
                      <td>{item.code}</td>
                      <td>{item.name}</td>
                      <td>{item.unit}</td>
                      <td>{item.category || '-'}</td>
                      <td>{item.currentStock}</td>
                      <td>{item.minStock}</td>
                      <td>{item.currentCost.toLocaleString('ar-EG')} ج.م</td>
                      <td>{item.totalValue.toLocaleString('ar-EG')} ج.م</td>
                      <td>
                        <span style={{
                          padding: '0.25rem 0.5rem',
                          borderRadius: '4px',
                          fontSize: '0.8rem',
                          background: item.status === 'متوفر' ? '#d4edda' :
                                     item.status === 'منخفض' ? '#fff3cd' : '#f8d7da',
                          color: item.status === 'متوفر' ? '#155724' :
                                item.status === 'منخفض' ? '#856404' : '#721c24'
                        }}>
                          {item.status}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        );

      case 'low-stock':
        return (
          <div>
            <div style={{ marginBottom: '2rem' }}>
              <h4 style={{ marginBottom: '1rem', color: '#495057' }}>ملخص المخزون المنخفض</h4>
              <div className="grid grid-3">
                <div className="stat-card">
                  <div className="stat-icon">⚠️</div>
                  <div className="stat-content">
                    <div className="stat-number">{reportData.summary.totalLowStockItems}</div>
                    <div className="stat-label">أصناف منخفضة</div>
                  </div>
                </div>
                <div className="stat-card">
                  <div className="stat-icon">📉</div>
                  <div className="stat-content">
                    <div className="stat-number">{reportData.summary.totalShortage}</div>
                    <div className="stat-label">إجمالي النقص</div>
                  </div>
                </div>
                <div className="stat-card">
                  <div className="stat-icon">💰</div>
                  <div className="stat-content">
                    <div className="stat-number">{reportData.summary.totalReorderValue.toLocaleString('ar-EG')}</div>
                    <div className="stat-label">قيمة إعادة الطلب (ج.م)</div>
                  </div>
                </div>
              </div>
            </div>

            <div style={{ overflow: 'auto' }}>
              <table className="table">
                <thead>
                  <tr>
                    <th>الكود</th>
                    <th>اسم الصنف</th>
                    <th>الرصيد الحالي</th>
                    <th>الحد الأدنى</th>
                    <th>النقص</th>
                    <th>التكلفة</th>
                    <th>قيمة إعادة الطلب</th>
                  </tr>
                </thead>
                <tbody>
                  {reportData.items.map(item => (
                    <tr key={item.id}>
                      <td>{item.code}</td>
                      <td>{item.name}</td>
                      <td style={{ color: '#dc3545', fontWeight: 'bold' }}>{item.currentStock}</td>
                      <td>{item.minStock}</td>
                      <td style={{ color: '#dc3545', fontWeight: 'bold' }}>{item.shortage}</td>
                      <td>{item.currentCost.toLocaleString('ar-EG')} ج.م</td>
                      <td style={{ color: '#007bff', fontWeight: 'bold' }}>
                        {item.reorderValue.toLocaleString('ar-EG')} ج.م
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        );

      default:
        return (
          <div style={{ textAlign: 'center', color: '#666', padding: '2rem' }}>
            <div style={{ fontSize: '3rem', marginBottom: '1rem', opacity: 0.5 }}>📊</div>
            <div style={{ fontSize: '1.2rem', marginBottom: '1rem' }}>
              {reportTypes[currentReport]}
            </div>
            <div>التقرير جاهز للعرض والطباعة</div>
          </div>
        );
    }
  };

  return (
    <div>
      {/* إعدادات التقرير */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div className="card-title">📋 تقارير المخزون</div>
        
        <div className="grid grid-4">
          <div className="form-group">
            <label className="form-label">نوع التقرير</label>
            <select
              className="form-control"
              value={currentReport}
              onChange={(e) => setCurrentReport(e.target.value)}
            >
              {Object.keys(reportTypes).map(key => (
                <option key={key} value={key}>
                  {reportTypes[key]}
                </option>
              ))}
            </select>
          </div>

          <div className="form-group">
            <label className="form-label">من تاريخ</label>
            <input
              type="date"
              className="form-control"
              value={dateFrom}
              onChange={(e) => setDateFrom(e.target.value)}
            />
          </div>

          <div className="form-group">
            <label className="form-label">إلى تاريخ</label>
            <input
              type="date"
              className="form-control"
              value={dateTo}
              onChange={(e) => setDateTo(e.target.value)}
            />
          </div>

          <div className="form-group">
            <label className="form-label">إجراءات</label>
            <div style={{ display: 'flex', gap: '0.5rem' }}>
              <button className="btn btn-primary" onClick={exportReport}>
                🖨️ طباعة
              </button>
              <button className="btn btn-success" onClick={exportToCSV}>
                📊 تصدير
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* محتوى التقرير */}
      {reportData.type && (
        <div className="card">
          <div className="card-title">
            {reportTypes[currentReport]} - من {dateFrom} إلى {dateTo}
          </div>
          
          {renderReportDisplay()}
        </div>
      )}
    </div>
  );
};

export default InventoryReports;
