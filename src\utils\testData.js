import { db } from '../database/db';

export const addTestData = async () => {
  try {
    // إضافة حسابات تجريبية أولاً
    const testAccounts = [
      { code: '111', name: 'النقدية بالصندوق', type: 'أصول', level: 3, parentId: null, isActive: true },
      { code: '112', name: 'البنك - الحساب الجاري', type: 'أصول', level: 3, parentId: null, isActive: true },
      { code: '211', name: 'حسابات دائنة - موردين', type: 'التزامات', level: 3, parentId: null, isActive: true },
      { code: '121', name: 'حسابات مدينة - عملاء', type: 'أصول', level: 3, parentId: null, isActive: true },
      { code: '411', name: 'إير<PERSON><PERSON><PERSON> المبيعات', type: 'إيرادات', level: 3, parentId: null, isActive: true },
      { code: '511', name: 'مصروفات المشتريات', type: 'مصروفات', level: 3, parentId: null, isActive: true },
      { code: '512', name: 'مصروفات العمالة', type: 'مصروفات', level: 3, parentId: null, isActive: true },
      { code: '513', name: 'مصروفات إدارية', type: 'مصروفات', level: 3, parentId: null, isActive: true },
      { code: '521', name: 'مصاريف العقود', type: 'مصروفات', level: 3, parentId: null, isActive: true },
      { code: '531', name: 'مصاريف العمالة', type: 'مصروفات', level: 3, parentId: null, isActive: true }
    ];

    for (const account of testAccounts) {
      const existing = await db.accounts.where('code').equals(account.code).first();
      if (!existing) {
        await db.accounts.add({
          ...account,
          createdAt: new Date(),
          balance: 0
        });
      }
    }

    // إضافة موردين تجريبيين
    const suppliers = [
      {
        name: 'شركة المواد الإنشائية',
        phone: '***********',
        address: 'القاهرة',
        email: '<EMAIL>',
        isActive: true,
        createdAt: new Date()
      },
      {
        name: 'مؤسسة الحديد والصلب',
        phone: '***********',
        address: 'الجيزة',
        email: '<EMAIL>',
        isActive: true,
        createdAt: new Date()
      }
    ];

    for (const supplier of suppliers) {
      const existing = await db.suppliers.where('name').equals(supplier.name).first();
      if (!existing) {
        await db.suppliers.add(supplier);
      }
    }

    // إضافة عقود تجريبية
    const contracts = [
      {
        contractNumber: 'CON-001',
        name: 'مشروع بناء مجمع سكني',
        clientName: 'شركة التطوير العقاري',
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-12-31'),
        totalValue: 5000000,
        status: 'active',
        description: 'بناء مجمع سكني 50 وحدة',
        isActive: true,
        createdAt: new Date()
      },
      {
        contractNumber: 'CON-002',
        name: 'مشروع تشطيب مكاتب إدارية',
        clientName: 'شركة الأعمال المتقدمة',
        startDate: new Date('2024-02-01'),
        endDate: new Date('2024-08-31'),
        totalValue: 2500000,
        status: 'active',
        description: 'تشطيب مكاتب إدارية 3 طوابق',
        isActive: true,
        createdAt: new Date()
      }
    ];

    for (const contract of contracts) {
      const existing = await db.contracts?.where('contractNumber').equals(contract.contractNumber).first();
      if (!existing && db.contracts) {
        await db.contracts.add(contract);
      }
    }

    // إضافة عملاء تجريبيين
    const customers = [
      {
        name: 'شركة المقاولات الحديثة',
        phone: '01111111111',
        address: 'الإسكندرية',
        email: '<EMAIL>',
        isActive: true,
        createdAt: new Date()
      },
      {
        name: 'مؤسسة البناء والتعمير',
        phone: '01222222222',
        address: 'المنصورة',
        email: '<EMAIL>',
        isActive: true,
        createdAt: new Date()
      }
    ];

    for (const customer of customers) {
      const existing = await db.customers.where('name').equals(customer.name).first();
      if (!existing) {
        await db.customers.add(customer);
      }
    }

    // إضافة فواتير مشتريات تجريبية
    const purchaseInvoices = [
      {
        invoiceNumber: 'PUR-001',
        supplierId: 1,
        date: new Date('2024-01-15'),
        totalAmount: 50000,
        paidAmount: 20000,
        paymentStatus: 'partial',
        items: [
          { name: 'حديد تسليح', quantity: 100, price: 500 }
        ],
        createdAt: new Date()
      },
      {
        invoiceNumber: 'PUR-002',
        supplierId: 2,
        date: new Date('2024-01-20'),
        totalAmount: 75000,
        paidAmount: 0,
        paymentStatus: 'pending',
        items: [
          { name: 'أسمنت', quantity: 200, price: 375 }
        ],
        createdAt: new Date()
      }
    ];

    for (const invoice of purchaseInvoices) {
      const existing = await db.purchaseInvoices.where('invoiceNumber').equals(invoice.invoiceNumber).first();
      if (!existing) {
        await db.purchaseInvoices.add(invoice);
      }
    }

    // إضافة فواتير مبيعات تجريبية
    const salesInvoices = [
      {
        invoiceNumber: 'SAL-001',
        customerId: 1,
        date: new Date('2024-01-18'),
        totalAmount: 80000,
        paidAmount: 30000,
        paymentStatus: 'partial',
        items: [
          { name: 'خدمات إنشائية', quantity: 1, price: 80000 }
        ],
        createdAt: new Date()
      },
      {
        invoiceNumber: 'SAL-002',
        customerId: 2,
        date: new Date('2024-01-25'),
        totalAmount: 120000,
        paidAmount: 0,
        paymentStatus: 'pending',
        items: [
          { name: 'أعمال تشطيب', quantity: 1, price: 120000 }
        ],
        createdAt: new Date()
      }
    ];

    for (const invoice of salesInvoices) {
      const existing = await db.salesInvoices.where('invoiceNumber').equals(invoice.invoiceNumber).first();
      if (!existing) {
        await db.salesInvoices.add(invoice);
      }
    }

    // إضافة أذون صرف تجريبية
    const workerVouchers = [
      {
        voucherNumber: 'WV-001',
        workerName: 'أحمد محمد',
        date: new Date('2024-01-10'),
        amount: 5000,
        paidAmount: 2000,
        paymentStatus: 'partial',
        description: 'أعمال بناء - الأسبوع الأول',
        createdAt: new Date()
      },
      {
        voucherNumber: 'WV-002',
        workerName: 'محمد علي',
        date: new Date('2024-01-12'),
        amount: 7500,
        paidAmount: 0,
        paymentStatus: 'pending',
        description: 'أعمال تشطيب - الأسبوع الأول',
        createdAt: new Date()
      },
      {
        voucherNumber: 'WV-003',
        workerName: 'علي حسن',
        date: new Date('2024-01-15'),
        amount: 6000,
        paidAmount: 0,
        paymentStatus: 'pending',
        description: 'أعمال كهرباء',
        createdAt: new Date()
      }
    ];

    // محاولة إضافة أذون الصرف في جداول مختلفة
    for (const voucher of workerVouchers) {
      try {
        if (db.workerVouchers) {
          const existing = await db.workerVouchers.where('voucherNumber').equals(voucher.voucherNumber).first();
          if (!existing) {
            await db.workerVouchers.add(voucher);
          }
        } else if (db.issueVouchers) {
          const existing = await db.issueVouchers.where('voucherNumber').equals(voucher.voucherNumber).first();
          if (!existing) {
            await db.issueVouchers.add(voucher);
          }
        }
      } catch (error) {
        console.log('تم تخطي إضافة أذون الصرف:', error.message);
      }
    }

    console.log('تم إضافة البيانات التجريبية بنجاح');
    return true;
  } catch (error) {
    console.error('خطأ في إضافة البيانات التجريبية:', error);
    return false;
  }
};

export const clearTestData = async () => {
  try {
    // مسح الحسابات التجريبية
    const testAccountCodes = ['111', '112', '211', '121', '411', '511', '512', '513', '521', '531'];
    for (const code of testAccountCodes) {
      const account = await db.accounts.where('code').equals(code).first();
      if (account) {
        await db.accounts.delete(account.id);
      }
    }

    await db.suppliers.clear();
    await db.customers.clear();
    await db.purchaseInvoices.clear();
    await db.salesInvoices.clear();

    if (db.contracts) {
      await db.contracts.clear();
    }

    if (db.workerVouchers) {
      await db.workerVouchers.clear();
    }

    console.log('تم مسح البيانات التجريبية');
    return true;
  } catch (error) {
    console.error('خطأ في مسح البيانات التجريبية:', error);
    return false;
  }
};
