# 🏢 نظام المحاسبة الشامل | Comprehensive Accounting System

نظام محاسبة متكامل مبني بتقنيات الويب الحديثة باللغة العربية مع دعم المعايير المحاسبية المصرية.

A comprehensive accounting system built with React and modern web technologies, featuring Arabic language support and Egyptian accounting standards.

## ✨ المميزات | Features

### 🏢 **المحاسبة العامة | General Accounting**
- 📊 دليل الحسابات (5 مستويات) | Chart of Accounts (5 levels)
- 📝 القيود اليومية | Journal Entries
- 📈 التقارير المالية | Financial Reports
- 💰 تقارير التدفق النقدي | Cash Flow Reports

### 💼 **المبيعات والمشتريات | Sales & Purchases**
- 🧾 فواتير المبيعات | Sales Invoices
- 📋 فواتير المشتريات | Purchase Invoices
- 👥 إدارة العملاء والموردين | Customer & Supplier Management
- 💳 إدارة المدفوعات | Payment Management

### 📦 **إدارة المخزون | Inventory Management**
- 📊 إدارة الأصناف | Item Management
- 📈 تقارير المخزون | Inventory Reports
- 🔄 حركات المخزون | Stock Movements
- 📋 سندات الصرف | Issue Vouchers

### 👷 **إدارة المشاريع والعمالة | Project & Labor Management**
- 📋 إدارة العقود | Contract Management
- 👷 إدارة العمالة | Labor Management
- 💰 نظام الرواتب | Payroll System
- 📊 سندات العمالة | Worker Vouchers

### 🔐 **الأمان والمستخدمين | Security & Users**
- 👤 إدارة المستخدمين | User Management
- 🔒 صلاحيات متدرجة | Role-based Permissions
- 🔐 تسجيل دخول آمن | Secure Authentication

### 💾 **النسخ الاحتياطية | Backup & Data**
- 💾 نسخ احتياطية تلقائية | Automatic Backups
- 📤 تصدير Excel | Excel Export
- 🔍 فحص قاعدة البيانات | Database Integrity Check

## 🚀 التثبيت والتشغيل | Installation & Setup

### متطلبات النظام | System Requirements
- Node.js 16+ 
- Windows 7+ / macOS / Linux
- 4GB RAM minimum
- 1GB free disk space

### التثبيت السريع | Quick Installation

```bash
# Clone the repository
git clone https://github.com/YOUR_USERNAME/accounting-system.git

# Navigate to project directory
cd accounting-system

# Install dependencies
npm install

# Start development server
npm run dev
```

### بيانات الدخول الافتراضية | Default Login
- **Username:** admin
- **Password:** admin123

## 🛠️ التقنيات المستخدمة | Technologies Used

- **Frontend:** React 18, Vite
- **Database:** IndexedDB (Dexie.js)
- **Styling:** CSS3, Responsive Design
- **Icons:** Lucide React
- **Export:** XLSX (Excel export)
- **Date Handling:** date-fns
- **Routing:** React Router DOM

## 📁 هيكل المشروع | Project Structure

```
src/
├── components/          # React components
│   ├── Login.jsx       # Authentication
│   ├── Dashboard.jsx   # Main dashboard
│   ├── ChartOfAccounts.jsx
│   ├── SalesInvoice.jsx
│   └── ...
├── contexts/           # React contexts
│   └── AuthContext.jsx
├── database/           # Database configuration
│   └── db.js
└── utils/             # Utility functions
    └── testData.js
```

## 🎯 الاستخدام | Usage

### للمطورين | For Developers
```bash
# Development mode
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

### للمستخدمين النهائيين | For End Users
1. Download the distribution package
2. Install Node.js from https://nodejs.org/
3. Run `start.bat` (Windows) or follow installation guide
4. Open browser to http://localhost:3001
5. Login with default credentials

## 📦 التوزيع | Distribution

يمكن توزيع النظام بطريقتين:

### 1. نسخة المطورين | Developer Version
- Clone من GitHub
- تثبيت dependencies
- تشغيل في وضع التطوير

### 2. نسخة المستخدمين | End User Version
- مجلد جاهز للتوزيع
- ملف تشغيل بسيط
- لا يحتاج معرفة تقنية

## 🔧 الإعدادات | Configuration

### إعدادات قاعدة البيانات | Database Settings
- البيانات محفوظة محلياً في IndexedDB
- نسخ احتياطية تلقائية
- إمكانية تصدير/استيراد البيانات

### إعدادات النظام | System Settings
- إدارة الشركة
- إعدادات الضرائب (14% VAT)
- إعدادات المستخدمين
- إعدادات التقارير

## 🤝 المساهمة | Contributing

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء branch جديد (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للـ branch (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## 📄 الترخيص | License

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم | Support

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/YOUR_USERNAME/accounting-system/issues)
- 📖 Documentation: [Wiki](https://github.com/YOUR_USERNAME/accounting-system/wiki)

## 🙏 شكر وتقدير | Acknowledgments

- React Team for the amazing framework
- Dexie.js for IndexedDB wrapper
- Lucide for beautiful icons
- All contributors and users

---

**Made with ❤️ for the Arabic accounting community**
