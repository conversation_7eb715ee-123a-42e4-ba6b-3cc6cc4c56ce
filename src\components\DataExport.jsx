import React, { useState } from 'react';
import { db } from '../database/db';

const DataExport = () => {
  const [loading, setLoading] = useState(false);
  const [selectedTables, setSelectedTables] = useState({
    customers: true,
    suppliers: true,
    items: true,
    contracts: true,
    workers: true,
    salesInvoices: true,
    purchaseInvoices: true,
    journalEntries: true,
    stockMovements: true,
    contractExpenses: true,
    laborCosts: true,
    payrolls: true
  });

  const tableNames = {
    customers: 'العملاء',
    suppliers: 'الموردين',
    items: 'الأصناف',
    contracts: 'العقود',
    workers: 'العمال',
    salesInvoices: 'فواتير البيع',
    purchaseInvoices: 'فواتير الشراء',
    journalEntries: 'القيود المحاسبية',
    stockMovements: 'حركات المخزون',
    contractExpenses: 'مصروفات العقود',
    laborCosts: 'تكاليف العمالة',
    payrolls: 'الرواتب'
  };

  const handleTableToggle = (tableName) => {
    setSelectedTables(prev => ({
      ...prev,
      [tableName]: !prev[tableName]
    }));
  };

  const selectAll = () => {
    const allSelected = Object.keys(selectedTables).reduce((acc, key) => {
      acc[key] = true;
      return acc;
    }, {});
    setSelectedTables(allSelected);
  };

  const selectNone = () => {
    const noneSelected = Object.keys(selectedTables).reduce((acc, key) => {
      acc[key] = false;
      return acc;
    }, {});
    setSelectedTables(noneSelected);
  };

  const exportToExcel = async () => {
    try {
      setLoading(true);
      
      const selectedTableNames = Object.keys(selectedTables).filter(
        table => selectedTables[table]
      );

      if (selectedTableNames.length === 0) {
        alert('يرجى اختيار جدول واحد على الأقل للتصدير');
        return;
      }

      // جمع البيانات من الجداول المحددة
      const exportData = {};
      
      for (const tableName of selectedTableNames) {
        try {
          const data = await db[tableName].toArray();
          exportData[tableNames[tableName]] = data.map(record => {
            // تحويل التواريخ إلى نص قابل للقراءة
            const processedRecord = { ...record };
            Object.keys(processedRecord).forEach(key => {
              if (processedRecord[key] instanceof Date) {
                processedRecord[key] = processedRecord[key].toLocaleString('ar-EG');
              } else if (typeof processedRecord[key] === 'object' && processedRecord[key] !== null) {
                processedRecord[key] = JSON.stringify(processedRecord[key]);
              }
            });
            return processedRecord;
          });
        } catch (error) {
          console.error(`خطأ في تصدير جدول ${tableName}:`, error);
        }
      }

      // إنشاء ملف Excel (CSV)
      let csvContent = '';
      
      Object.keys(exportData).forEach(tableName => {
        const data = exportData[tableName];
        if (data.length > 0) {
          csvContent += `\n\n=== ${tableName} ===\n`;
          
          // إضافة رؤوس الأعمدة
          const headers = Object.keys(data[0]);
          csvContent += headers.map(header => `"${header}"`).join(',') + '\n';
          
          // إضافة البيانات
          data.forEach(row => {
            const values = headers.map(header => {
              const value = row[header] || '';
              return `"${value.toString().replace(/"/g, '""')}"`;
            });
            csvContent += values.join(',') + '\n';
          });
        }
      });

      // تحميل الملف
      const blob = new Blob(['\ufeff' + csvContent], { 
        type: 'text/csv;charset=utf-8;' 
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `تصدير_البيانات_${new Date().toISOString().split('T')[0]}.csv`;
      link.click();
      URL.revokeObjectURL(url);

      alert('تم تصدير البيانات بنجاح');
    } catch (error) {
      console.error('خطأ في تصدير البيانات:', error);
      alert('حدث خطأ أثناء تصدير البيانات');
    } finally {
      setLoading(false);
    }
  };

  const exportToJSON = async () => {
    try {
      setLoading(true);
      
      const selectedTableNames = Object.keys(selectedTables).filter(
        table => selectedTables[table]
      );

      if (selectedTableNames.length === 0) {
        alert('يرجى اختيار جدول واحد على الأقل للتصدير');
        return;
      }

      // جمع البيانات من الجداول المحددة
      const exportData = {};
      
      for (const tableName of selectedTableNames) {
        try {
          exportData[tableName] = await db[tableName].toArray();
        } catch (error) {
          console.error(`خطأ في تصدير جدول ${tableName}:`, error);
        }
      }

      // إضافة معلومات التصدير
      const finalData = {
        exportInfo: {
          date: new Date().toISOString(),
          tables: selectedTableNames,
          totalRecords: Object.values(exportData).reduce(
            (total, table) => total + table.length, 0
          )
        },
        data: exportData
      };

      // تحميل الملف
      const blob = new Blob([JSON.stringify(finalData, null, 2)], { 
        type: 'application/json' 
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `تصدير_البيانات_${new Date().toISOString().split('T')[0]}.json`;
      link.click();
      URL.revokeObjectURL(url);

      alert('تم تصدير البيانات بنجاح');
    } catch (error) {
      console.error('خطأ في تصدير البيانات:', error);
      alert('حدث خطأ أثناء تصدير البيانات');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="card">
      <div className="card-title">📊 تصدير البيانات</div>
      
      <div style={{ marginBottom: '2rem' }}>
        <div style={{ 
          background: '#d1ecf1', 
          border: '1px solid #bee5eb', 
          borderRadius: '5px', 
          padding: '1rem',
          marginBottom: '1rem'
        }}>
          <div style={{ color: '#0c5460', fontWeight: 'bold', marginBottom: '0.5rem' }}>
            ℹ️ معلومات التصدير
          </div>
          <div style={{ color: '#0c5460', fontSize: '0.9rem' }}>
            • يمكنك اختيار الجداول التي تريد تصديرها<br/>
            • تصدير Excel: ملف CSV يمكن فتحه في Excel<br/>
            • تصدير JSON: ملف JSON للنسخ الاحتياطي أو التكامل
          </div>
        </div>

        <div style={{ display: 'flex', gap: '1rem', marginBottom: '1rem' }}>
          <button className="btn btn-primary" onClick={selectAll}>
            ✅ تحديد الكل
          </button>
          <button className="btn btn-secondary" onClick={selectNone}>
            ❌ إلغاء التحديد
          </button>
        </div>
      </div>

      <div className="grid grid-3" style={{ marginBottom: '2rem' }}>
        {Object.keys(selectedTables).map(tableName => (
          <label 
            key={tableName}
            style={{ 
              display: 'flex', 
              alignItems: 'center', 
              gap: '0.5rem',
              padding: '0.5rem',
              border: '1px solid #dee2e6',
              borderRadius: '5px',
              cursor: 'pointer',
              background: selectedTables[tableName] ? '#e7f3ff' : 'white'
            }}
          >
            <input
              type="checkbox"
              checked={selectedTables[tableName]}
              onChange={() => handleTableToggle(tableName)}
            />
            <span>{tableNames[tableName]}</span>
          </label>
        ))}
      </div>

      <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center' }}>
        <button
          className="btn btn-success"
          onClick={exportToExcel}
          disabled={loading}
          style={{ padding: '1rem 2rem' }}
        >
          📊 تصدير إلى Excel
        </button>
        
        <button
          className="btn btn-info"
          onClick={exportToJSON}
          disabled={loading}
          style={{ padding: '1rem 2rem' }}
        >
          📄 تصدير إلى JSON
        </button>
      </div>

      {loading && (
        <div style={{ textAlign: 'center', marginTop: '1rem' }}>
          <div className="spinner"></div>
          <div>جاري تصدير البيانات...</div>
        </div>
      )}
    </div>
  );
};

export default DataExport;
