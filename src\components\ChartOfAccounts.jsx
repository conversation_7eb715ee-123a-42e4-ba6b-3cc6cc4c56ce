import React, { useState, useEffect } from 'react';
import { db } from '../database/db';
import AccountsSummary from './AccountsSummary';
import AccountDetails from './AccountDetails';

const ChartOfAccounts = () => {
  const [accounts, setAccounts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingAccount, setEditingAccount] = useState(null);
  const [expandedNodes, setExpandedNodes] = useState(new Set([1, 2, 3, 4, 5])); // توسيع الحسابات الرئيسية افتراضياً

  const [formData, setFormData] = useState({
    code: '',
    name: '',
    parentId: null,
    type: '',
    level: 1
  });

  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('');
  const [filterLevel, setFilterLevel] = useState('');
  const [selectedAccountId, setSelectedAccountId] = useState(null);

  useEffect(() => {
    loadAccounts();
    initializeDefaultAccounts();
    cleanupDuplicateAccounts();
  }, []);

  const cleanupDuplicateAccounts = async () => {
    try {
      console.log('فحص الحسابات المكررة...');

      // البحث عن الحسابات المكررة حسب الكود
      const allAccounts = await db.accounts.toArray();
      const accountGroups = {};

      // تجميع الحسابات حسب الكود
      allAccounts.forEach(account => {
        if (!accountGroups[account.code]) {
          accountGroups[account.code] = [];
        }
        accountGroups[account.code].push(account);
      });

      // حذف المكررات (الاحتفاظ بالأقدم)
      for (const code in accountGroups) {
        const accounts = accountGroups[code];
        if (accounts.length > 1) {
          console.log(`وجدت ${accounts.length} حسابات مكررة للكود ${code}`);

          // ترتيب حسب تاريخ الإنشاء والاحتفاظ بالأول
          accounts.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
          const accountsToDelete = accounts.slice(1); // حذف كل شيء عدا الأول

          for (const account of accountsToDelete) {
            await db.accounts.delete(account.id);
            console.log(`تم حذف الحساب المكرر: ${account.name} (ID: ${account.id})`);
          }
        }
      }

      console.log('تم الانتهاء من تنظيف الحسابات المكررة');
    } catch (error) {
      console.error('خطأ في تنظيف الحسابات المكررة:', error);
    }
  };

  const initializeDefaultAccounts = async () => {
    try {
      const accountsCount = await db.accounts.count();
      if (accountsCount === 0) {
        console.log('إنشاء الحسابات الافتراضية...');

        // الحسابات الرئيسية
        const defaultAccounts = [
          { code: '1', name: 'الأصول', type: 'أصول', level: 1, parentId: null },
          { code: '2', name: 'الالتزامات', type: 'التزامات', level: 1, parentId: null },
          { code: '3', name: 'حقوق الملكية', type: 'حقوق ملكية', level: 1, parentId: null },
          { code: '4', name: 'الإيرادات', type: 'إيرادات', level: 1, parentId: null },
          { code: '5', name: 'المصروفات', type: 'مصروفات', level: 1, parentId: null }
        ];

        // التحقق من عدم وجود كل حساب قبل إضافته
        for (const account of defaultAccounts) {
          const existingAccount = await db.accounts
            .where('code')
            .equals(account.code)
            .first();

          if (!existingAccount) {
            await db.accounts.add({
              ...account,
              isActive: true,
              balance: 0,
              createdAt: new Date()
            });
            console.log(`تم إنشاء الحساب: ${account.name}`);
          } else {
            console.log(`الحساب موجود بالفعل: ${account.name}`);
          }
        }

        console.log('تم التحقق من الحسابات الافتراضية');
        loadAccounts();
      }
    } catch (error) {
      console.error('خطأ في إنشاء الحسابات الافتراضية:', error);
    }
  };

  const loadAccounts = async () => {
    try {
      setLoading(true);
      console.log('بدء تحميل الحسابات...');

      // التحقق من وجود جدول الحسابات
      const allAccounts = await db.accounts.orderBy('code').toArray();
      console.log('الحسابات المحملة:', allAccounts);

      setAccounts(allAccounts);
    } catch (error) {
      console.error('خطأ في تحميل الحسابات:', error);
      alert('حدث خطأ في تحميل الحسابات: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const buildAccountTree = (accounts, parentId = null) => {
    return accounts
      .filter(account => account.parentId === parentId)
      .map(account => ({
        ...account,
        children: buildAccountTree(accounts, account.id)
      }));
  };

  const filterAccounts = (accounts) => {
    return accounts.filter(account => {
      const matchesSearch = !searchTerm ||
        account.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        account.code.includes(searchTerm);

      const matchesType = !filterType || account.type === filterType;
      const matchesLevel = !filterLevel || account.level.toString() === filterLevel;

      return matchesSearch && matchesType && matchesLevel;
    });
  };

  const toggleNode = (accountId) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(accountId)) {
      newExpanded.delete(accountId);
    } else {
      newExpanded.add(accountId);
    }
    setExpandedNodes(newExpanded);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      if (editingAccount) {
        // تحديث حساب موجود
        await db.accounts.update(editingAccount.id, {
          ...formData,
          updatedAt: new Date()
        });
      } else {
        // إضافة حساب جديد
        await db.accounts.add({
          ...formData,
          isActive: true,
          balance: 0,
          createdAt: new Date()
        });
      }
      
      await loadAccounts();
      resetForm();
    } catch (error) {
      console.error('خطأ في حفظ الحساب:', error);
      alert('حدث خطأ أثناء حفظ الحساب');
    }
  };

  const handleEdit = (account) => {
    setEditingAccount(account);
    setFormData({
      code: account.code,
      name: account.name,
      parentId: account.parentId,
      type: account.type,
      level: account.level
    });
    setShowAddForm(true);
  };

  const handleDelete = async (accountId) => {
    if (!confirm('هل أنت متأكد من حذف هذا الحساب؟')) return;
    
    try {
      // التحقق من وجود حسابات فرعية
      const childAccounts = await db.accounts.where('parentId').equals(accountId).count();
      if (childAccounts > 0) {
        alert('لا يمكن حذف حساب يحتوي على حسابات فرعية');
        return;
      }
      
      await db.accounts.delete(accountId);
      await loadAccounts();
    } catch (error) {
      console.error('خطأ في حذف الحساب:', error);
      alert('حدث خطأ أثناء حذف الحساب');
    }
  };

  const resetForm = () => {
    setFormData({
      code: '',
      name: '',
      parentId: null,
      type: '',
      level: 1
    });
    setEditingAccount(null);
    setShowAddForm(false);
  };

  const generateAccountCode = (parentAccount, level) => {
    if (level === 1) {
      // الحسابات الرئيسية (1-5)
      const existingMainAccounts = accounts.filter(acc => acc.level === 1);
      return String(existingMainAccounts.length + 1);
    } else {
      // الحسابات الفرعية
      const parentCode = parentAccount.code;
      const siblingAccounts = accounts.filter(acc => acc.parentId === parentAccount.id);
      const nextNumber = siblingAccounts.length + 1;
      return `${parentCode}${String(nextNumber).padStart(2, '0')}`;
    }
  };

  const getAccountTypeOptions = (level) => {
    if (level === 1) {
      return [
        { value: 'أصول', label: 'أصول' },
        { value: 'التزامات', label: 'التزامات' },
        { value: 'حقوق ملكية', label: 'حقوق ملكية' },
        { value: 'إيرادات', label: 'إيرادات' },
        { value: 'مصروفات', label: 'مصروفات' }
      ];
    } else {
      // للمستويات الفرعية، نأخذ نوع الحساب الأب
      const parentAccount = accounts.find(acc => acc.id === formData.parentId);
      return parentAccount ? [{ value: parentAccount.type, label: parentAccount.type }] : [];
    }
  };

  const renderAccountNode = (account, level = 0) => {
    const hasChildren = account.children && account.children.length > 0;
    const isExpanded = expandedNodes.has(account.id);
    const indent = level * 30;

    return (
      <div key={account.id} style={{ marginBottom: '0.5rem' }}>
        <div 
          style={{
            display: 'flex',
            alignItems: 'center',
            padding: '0.75rem',
            background: level === 0 ? '#f8f9fa' : 'white',
            border: '1px solid #dee2e6',
            borderRadius: '5px',
            marginRight: `${indent}px`,
            cursor: hasChildren ? 'pointer' : 'default'
          }}
          onClick={() => hasChildren && toggleNode(account.id)}
        >
          <div style={{ display: 'flex', alignItems: 'center', flex: 1 }}>
            {hasChildren && (
              <span style={{ marginLeft: '0.5rem', fontSize: '0.8rem' }}>
                {isExpanded ? '▼' : '▶'}
              </span>
            )}
            <div style={{ flex: 1 }}>
              <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
                <strong>{account.code}</strong>
                <span>{account.name}</span>
                <span style={{ 
                  background: getTypeColor(account.type), 
                  color: 'white', 
                  padding: '0.25rem 0.5rem', 
                  borderRadius: '3px',
                  fontSize: '0.8rem'
                }}>
                  {account.type}
                </span>
                <span style={{ color: '#666', fontSize: '0.9rem' }}>
                  المستوى {account.level}
                </span>
              </div>
            </div>
          </div>
          
          <div style={{ display: 'flex', gap: '0.5rem' }}>
            <button
              className="btn"
              style={{
                padding: '0.25rem 0.5rem',
                fontSize: '0.8rem',
                background: '#17a2b8',
                color: 'white'
              }}
              onClick={(e) => {
                e.stopPropagation();
                setSelectedAccountId(account.id);
              }}
            >
              التفاصيل
            </button>
            <button
              className="btn btn-primary"
              style={{ padding: '0.25rem 0.5rem', fontSize: '0.8rem' }}
              onClick={(e) => {
                e.stopPropagation();
                setFormData({
                  code: generateAccountCode(account, account.level + 1),
                  name: '',
                  parentId: account.id,
                  type: account.type,
                  level: account.level + 1
                });
                setShowAddForm(true);
              }}
            >
              إضافة فرعي
            </button>
            <button
              className="btn btn-secondary"
              style={{ padding: '0.25rem 0.5rem', fontSize: '0.8rem' }}
              onClick={(e) => {
                e.stopPropagation();
                handleEdit(account);
              }}
            >
              تعديل
            </button>
            {account.level > 1 && (
              <button
                className="btn btn-danger"
                style={{ padding: '0.25rem 0.5rem', fontSize: '0.8rem' }}
                onClick={(e) => {
                  e.stopPropagation();
                  handleDelete(account.id);
                }}
              >
                حذف
              </button>
            )}
          </div>
        </div>
        
        {hasChildren && isExpanded && (
          <div style={{ marginTop: '0.5rem' }}>
            {account.children.map(child => renderAccountNode(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  const getTypeColor = (type) => {
    const colors = {
      'أصول': '#007bff',
      'التزامات': '#dc3545',
      'حقوق ملكية': '#28a745',
      'إيرادات': '#17a2b8',
      'مصروفات': '#ffc107'
    };
    return colors[type] || '#6c757d';
  };

  const exportToExcel = () => {
    try {
      // تحضير البيانات للتصدير
      const exportData = accounts.map(account => ({
        'رمز الحساب': account.code,
        'اسم الحساب': account.name,
        'نوع الحساب': account.type,
        'المستوى': account.level,
        'الحساب الأب': account.parentId ? accounts.find(p => p.id === account.parentId)?.name || '' : '',
        'الرصيد': account.balance || 0,
        'نشط': account.isActive ? 'نعم' : 'لا',
        'تاريخ الإنشاء': new Date(account.createdAt).toLocaleDateString('ar-EG')
      }));

      // تحويل إلى CSV
      const headers = Object.keys(exportData[0]);
      const csvContent = [
        headers.join(','),
        ...exportData.map(row =>
          headers.map(header => `"${row[header]}"`).join(',')
        )
      ].join('\n');

      // تحميل الملف
      const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `دليل_الحسابات_${new Date().toISOString().split('T')[0]}.csv`;
      link.click();
      URL.revokeObjectURL(url);

      alert('تم تصدير دليل الحسابات بنجاح');
    } catch (error) {
      console.error('خطأ في تصدير البيانات:', error);
      alert('حدث خطأ أثناء تصدير البيانات');
    }
  };

  if (loading) {
    return (
      <div className="container">
        <div className="loading">
          <div className="spinner"></div>
        </div>
      </div>
    );
  }

  const filteredAccounts = filterAccounts(accounts);
  const accountTree = buildAccountTree(filteredAccounts);

  return (
    <div className="container">
      {/* ملخص الحسابات */}
      <AccountsSummary />

      <div className="card">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1.5rem' }}>
          <div className="card-title">دليل الحسابات</div>
          <div style={{ display: 'flex', gap: '0.5rem' }}>
            <button
              className="btn btn-success"
              onClick={exportToExcel}
            >
              تصدير Excel
            </button>
            <button
              className="btn btn-warning"
              onClick={async () => {
                if (confirm('هل تريد تنظيف الحسابات المكررة؟')) {
                  await cleanupDuplicateAccounts();
                  await loadAccounts();
                  alert('تم تنظيف الحسابات المكررة بنجاح');
                }
              }}
            >
              تنظيف المكررات
            </button>
            <button
              className="btn btn-primary"
              onClick={() => {
                setFormData({
                  code: String(accounts.filter(acc => acc.level === 1).length + 1),
                  name: '',
                  parentId: null,
                  type: '',
                  level: 1
                });
                setShowAddForm(true);
              }}
            >
              إضافة حساب رئيسي
            </button>
          </div>
        </div>

        {/* أدوات البحث والتصفية */}
        <div className="grid grid-4" style={{ marginBottom: '1.5rem' }}>
          <div className="form-group">
            <label className="form-label">البحث</label>
            <input
              type="text"
              className="form-control"
              placeholder="ابحث بالاسم أو الرمز..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <div className="form-group">
            <label className="form-label">تصفية حسب النوع</label>
            <select
              className="form-control"
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
            >
              <option value="">جميع الأنواع</option>
              <option value="أصول">أصول</option>
              <option value="التزامات">التزامات</option>
              <option value="حقوق ملكية">حقوق ملكية</option>
              <option value="إيرادات">إيرادات</option>
              <option value="مصروفات">مصروفات</option>
            </select>
          </div>

          <div className="form-group">
            <label className="form-label">تصفية حسب المستوى</label>
            <select
              className="form-control"
              value={filterLevel}
              onChange={(e) => setFilterLevel(e.target.value)}
            >
              <option value="">جميع المستويات</option>
              <option value="1">المستوى 1</option>
              <option value="2">المستوى 2</option>
              <option value="3">المستوى 3</option>
              <option value="4">المستوى 4</option>
              <option value="5">المستوى 5</option>
            </select>
          </div>

          <div className="form-group">
            <label className="form-label">إجراءات</label>
            <button
              className="btn btn-secondary"
              style={{ width: '100%' }}
              onClick={() => {
                setSearchTerm('');
                setFilterType('');
                setFilterLevel('');
              }}
            >
              مسح التصفية
            </button>
          </div>
        </div>

        {/* شجرة الحسابات */}
        <div style={{ marginBottom: '2rem' }}>
          {accountTree.map(account => renderAccountNode(account))}
        </div>
      </div>

      {/* نموذج إضافة/تعديل حساب */}
      {showAddForm && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            padding: '2rem',
            borderRadius: '10px',
            width: '90%',
            maxWidth: '500px',
            maxHeight: '90vh',
            overflow: 'auto'
          }}>
            <h3 style={{ marginBottom: '1.5rem' }}>
              {editingAccount ? 'تعديل حساب' : 'إضافة حساب جديد'}
            </h3>

            <form onSubmit={handleSubmit}>
              <div className="form-group">
                <label className="form-label">رمز الحساب</label>
                <input
                  type="text"
                  className="form-control"
                  value={formData.code}
                  onChange={(e) => setFormData({...formData, code: e.target.value})}
                  required
                />
              </div>

              <div className="form-group">
                <label className="form-label">اسم الحساب</label>
                <input
                  type="text"
                  className="form-control"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  required
                />
              </div>

              <div className="form-group">
                <label className="form-label">نوع الحساب</label>
                <select
                  className="form-control"
                  value={formData.type}
                  onChange={(e) => setFormData({...formData, type: e.target.value})}
                  required
                >
                  <option value="">اختر نوع الحساب</option>
                  {getAccountTypeOptions(formData.level).map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div style={{ display: 'flex', gap: '1rem', marginTop: '1.5rem' }}>
                <button type="submit" className="btn btn-primary">
                  {editingAccount ? 'تحديث' : 'إضافة'}
                </button>
                <button 
                  type="button" 
                  className="btn btn-secondary"
                  onClick={resetForm}
                >
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* نافذة تفاصيل الحساب */}
      {selectedAccountId && (
        <AccountDetails
          accountId={selectedAccountId}
          onClose={() => setSelectedAccountId(null)}
        />
      )}
    </div>
  );
};

export default ChartOfAccounts;
