import React, { useState, useEffect } from 'react';
import { db } from '../database/db';
import { useAuth } from '../contexts/AuthContext';
import AccountsSummary from './AccountsSummary';
import AccountDetails from './AccountDetails';
import AccountLinks from './AccountLinks';
import * as XLSX from 'xlsx';

const ChartOfAccounts = () => {
  const { hasPermission } = useAuth();
  const [currentTab, setCurrentTab] = useState('accounts'); // 'accounts', 'links', 'reports'
  const [accounts, setAccounts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingAccount, setEditingAccount] = useState(null);
  const [expandedNodes, setExpandedNodes] = useState(new Set([1, 2, 3, 4, 5])); // توسيع الحسابات الرئيسية افتراضياً

  const [formData, setFormData] = useState({
    code: '',
    name: '',
    parentId: null,
    type: '',
    level: 1
  });

  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('');
  const [filterLevel, setFilterLevel] = useState('');
  const [selectedAccountId, setSelectedAccountId] = useState(null);

  useEffect(() => {
    loadAccounts();
    initializeDefaultAccounts();
    cleanupDuplicateAccounts();
  }, []);

  const cleanupDuplicateAccounts = async () => {
    try {
      console.log('فحص الحسابات المكررة...');

      // البحث عن الحسابات المكررة حسب الكود
      const allAccounts = await db.accounts.toArray();
      const accountGroups = {};

      // تجميع الحسابات حسب الكود
      allAccounts.forEach(account => {
        if (!accountGroups[account.code]) {
          accountGroups[account.code] = [];
        }
        accountGroups[account.code].push(account);
      });

      // حذف المكررات (الاحتفاظ بالأقدم)
      for (const code in accountGroups) {
        const accounts = accountGroups[code];
        if (accounts.length > 1) {
          console.log(`وجدت ${accounts.length} حسابات مكررة للكود ${code}`);

          // ترتيب حسب تاريخ الإنشاء والاحتفاظ بالأول
          accounts.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
          const accountsToDelete = accounts.slice(1); // حذف كل شيء عدا الأول

          for (const account of accountsToDelete) {
            await db.accounts.delete(account.id);
            console.log(`تم حذف الحساب المكرر: ${account.name} (ID: ${account.id})`);
          }
        }
      }

      console.log('تم الانتهاء من تنظيف الحسابات المكررة');
    } catch (error) {
      console.error('خطأ في تنظيف الحسابات المكررة:', error);
    }
  };

  const initializeDefaultAccounts = async () => {
    try {
      const accountsCount = await db.accounts.count();
      if (accountsCount === 0) {
        console.log('إنشاء الحسابات الافتراضية...');

        // الحسابات الرئيسية
        const defaultAccounts = [
          { code: '1', name: 'الأصول', type: 'أصول', level: 1, parentId: null },
          { code: '2', name: 'الالتزامات', type: 'التزامات', level: 1, parentId: null },
          { code: '3', name: 'حقوق الملكية', type: 'حقوق ملكية', level: 1, parentId: null },
          { code: '4', name: 'الإيرادات', type: 'إيرادات', level: 1, parentId: null },
          { code: '5', name: 'المصروفات', type: 'مصروفات', level: 1, parentId: null }
        ];

        // التحقق من عدم وجود كل حساب قبل إضافته
        for (const account of defaultAccounts) {
          const existingAccount = await db.accounts
            .where('code')
            .equals(account.code)
            .first();

          if (!existingAccount) {
            await db.accounts.add({
              ...account,
              isActive: true,
              balance: 0,
              createdAt: new Date()
            });
            console.log(`تم إنشاء الحساب: ${account.name}`);
          } else {
            console.log(`الحساب موجود بالفعل: ${account.name}`);
          }
        }

        console.log('تم التحقق من الحسابات الافتراضية');
        loadAccounts();
      }
    } catch (error) {
      console.error('خطأ في إنشاء الحسابات الافتراضية:', error);
    }
  };

  const loadAccounts = async () => {
    try {
      setLoading(true);
      console.log('بدء تحميل الحسابات...');

      // التحقق من وجود جدول الحسابات
      const allAccounts = await db.accounts.orderBy('code').toArray();
      console.log('الحسابات المحملة:', allAccounts);

      setAccounts(allAccounts);
    } catch (error) {
      console.error('خطأ في تحميل الحسابات:', error);
      alert('حدث خطأ في تحميل الحسابات: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const buildAccountTree = (accounts, parentId = null) => {
    return accounts
      .filter(account => account.parentId === parentId)
      .map(account => ({
        ...account,
        children: buildAccountTree(accounts, account.id)
      }));
  };

  const filterAccounts = (accounts) => {
    return accounts.filter(account => {
      const matchesSearch = !searchTerm ||
        account.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        account.code.includes(searchTerm);

      const matchesType = !filterType || account.type === filterType;
      const matchesLevel = !filterLevel || account.level.toString() === filterLevel;

      return matchesSearch && matchesType && matchesLevel;
    });
  };

  const toggleNode = (accountId) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(accountId)) {
      newExpanded.delete(accountId);
    } else {
      newExpanded.add(accountId);
    }
    setExpandedNodes(newExpanded);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      if (editingAccount) {
        // تحديث حساب موجود
        await db.accounts.update(editingAccount.id, {
          ...formData,
          updatedAt: new Date()
        });
      } else {
        // إضافة حساب جديد
        await db.accounts.add({
          ...formData,
          isActive: true,
          balance: 0,
          createdAt: new Date()
        });
      }
      
      await loadAccounts();
      resetForm();
    } catch (error) {
      console.error('خطأ في حفظ الحساب:', error);
      alert('حدث خطأ أثناء حفظ الحساب');
    }
  };

  const handleEdit = (account) => {
    setEditingAccount(account);
    setFormData({
      code: account.code,
      name: account.name,
      parentId: account.parentId,
      type: account.type,
      level: account.level
    });
    setShowAddForm(true);
  };

  const handleDelete = async (accountId) => {
    if (!confirm('هل أنت متأكد من حذف هذا الحساب؟')) return;
    
    try {
      // التحقق من وجود حسابات فرعية
      const childAccounts = await db.accounts.where('parentId').equals(accountId).count();
      if (childAccounts > 0) {
        alert('لا يمكن حذف حساب يحتوي على حسابات فرعية');
        return;
      }
      
      await db.accounts.delete(accountId);
      await loadAccounts();
    } catch (error) {
      console.error('خطأ في حذف الحساب:', error);
      alert('حدث خطأ أثناء حذف الحساب');
    }
  };

  const resetForm = () => {
    setFormData({
      code: '',
      name: '',
      parentId: null,
      type: '',
      level: 1
    });
    setEditingAccount(null);
    setShowAddForm(false);
  };

  const generateAccountCode = (parentAccount, level) => {
    if (level === 1) {
      // الحسابات الرئيسية (1-5)
      const existingMainAccounts = accounts.filter(acc => acc.level === 1);
      return String(existingMainAccounts.length + 1);
    } else {
      // الحسابات الفرعية
      const parentCode = parentAccount.code;
      const siblingAccounts = accounts.filter(acc => acc.parentId === parentAccount.id);
      const nextNumber = siblingAccounts.length + 1;
      return `${parentCode}${String(nextNumber).padStart(2, '0')}`;
    }
  };

  const getAccountTypeOptions = (level) => {
    if (level === 1) {
      return [
        { value: 'أصول', label: 'أصول' },
        { value: 'التزامات', label: 'التزامات' },
        { value: 'حقوق ملكية', label: 'حقوق ملكية' },
        { value: 'إيرادات', label: 'إيرادات' },
        { value: 'مصروفات', label: 'مصروفات' }
      ];
    } else {
      // للمستويات الفرعية، نأخذ نوع الحساب الأب
      const parentAccount = accounts.find(acc => acc.id === formData.parentId);
      return parentAccount ? [{ value: parentAccount.type, label: parentAccount.type }] : [];
    }
  };

  const renderAccountNode = (account, level = 0) => {
    const hasChildren = account.children && account.children.length > 0;
    const isExpanded = expandedNodes.has(account.id);
    const indent = level * 30;

    return (
      <div key={account.id} style={{ marginBottom: '0.5rem' }}>
        <div 
          style={{
            display: 'flex',
            alignItems: 'center',
            padding: '0.75rem',
            background: level === 0 ? '#f8f9fa' : 'white',
            border: '1px solid #dee2e6',
            borderRadius: '5px',
            marginRight: `${indent}px`,
            cursor: hasChildren ? 'pointer' : 'default'
          }}
          onClick={() => hasChildren && toggleNode(account.id)}
        >
          <div style={{ display: 'flex', alignItems: 'center', flex: 1 }}>
            {hasChildren && (
              <span style={{ marginLeft: '0.5rem', fontSize: '0.8rem' }}>
                {isExpanded ? '▼' : '▶'}
              </span>
            )}
            <div style={{ flex: 1 }}>
              <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
                <strong>{account.code}</strong>
                <span>{account.name}</span>
                <span style={{ 
                  background: getTypeColor(account.type), 
                  color: 'white', 
                  padding: '0.25rem 0.5rem', 
                  borderRadius: '3px',
                  fontSize: '0.8rem'
                }}>
                  {account.type}
                </span>
                <span style={{ color: '#666', fontSize: '0.9rem' }}>
                  المستوى {account.level}
                </span>
              </div>
            </div>
          </div>
          
          <div style={{ display: 'flex', gap: '0.5rem' }}>
            <button
              className="btn"
              style={{
                padding: '0.25rem 0.5rem',
                fontSize: '0.8rem',
                background: '#17a2b8',
                color: 'white'
              }}
              onClick={(e) => {
                e.stopPropagation();
                setSelectedAccountId(account.id);
              }}
            >
              التفاصيل
            </button>
            <button
              className="btn btn-primary"
              style={{ padding: '0.25rem 0.5rem', fontSize: '0.8rem' }}
              onClick={(e) => {
                e.stopPropagation();
                setFormData({
                  code: generateAccountCode(account, account.level + 1),
                  name: '',
                  parentId: account.id,
                  type: account.type,
                  level: account.level + 1
                });
                setShowAddForm(true);
              }}
            >
              إضافة فرعي
            </button>
            <button
              className="btn btn-secondary"
              style={{ padding: '0.25rem 0.5rem', fontSize: '0.8rem' }}
              onClick={(e) => {
                e.stopPropagation();
                handleEdit(account);
              }}
            >
              تعديل
            </button>
            {account.level > 1 && (
              <button
                className="btn btn-danger"
                style={{ padding: '0.25rem 0.5rem', fontSize: '0.8rem' }}
                onClick={(e) => {
                  e.stopPropagation();
                  handleDelete(account.id);
                }}
              >
                حذف
              </button>
            )}
          </div>
        </div>
        
        {hasChildren && isExpanded && (
          <div style={{ marginTop: '0.5rem' }}>
            {account.children.map(child => renderAccountNode(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  const getTypeColor = (type) => {
    const colors = {
      'أصول': '#007bff',
      'التزامات': '#dc3545',
      'حقوق ملكية': '#28a745',
      'إيرادات': '#17a2b8',
      'مصروفات': '#ffc107'
    };
    return colors[type] || '#6c757d';
  };

  const exportToExcel = () => {
    try {
      // تحضير البيانات للتصدير
      const exportData = accounts.map(account => ({
        'رمز الحساب': account.code,
        'اسم الحساب': account.name,
        'نوع الحساب': account.type,
        'المستوى': account.level,
        'رمز الحساب الأب': account.parentId ? accounts.find(p => p.id === account.parentId)?.code || '' : '',
        'اسم الحساب الأب': account.parentId ? accounts.find(p => p.id === account.parentId)?.name || '' : '',
        'الرصيد': account.balance || 0,
        'نشط': account.isActive ? 'نعم' : 'لا',
        'تاريخ الإنشاء': new Date(account.createdAt).toLocaleDateString('ar-EG')
      }));

      // إنشاء ملف Excel
      const ws = XLSX.utils.json_to_sheet(exportData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'دليل الحسابات');

      const fileName = `دليل_الحسابات_${new Date().toISOString().split('T')[0]}.xlsx`;
      XLSX.writeFile(wb, fileName);

      alert('تم تصدير دليل الحسابات بنجاح');
    } catch (error) {
      console.error('خطأ في تصدير البيانات:', error);
      alert('حدث خطأ أثناء تصدير البيانات');
    }
  };

  const importFromExcel = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.xlsx,.xls';
    input.onchange = handleFileImport;
    input.click();
  };

  const handleFileImport = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
      setLoading(true);

      const data = await file.arrayBuffer();
      const workbook = XLSX.read(data);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet);

      if (jsonData.length === 0) {
        alert('الملف فارغ أو لا يحتوي على بيانات صالحة');
        return;
      }

      // التحقق من الأعمدة المطلوبة
      const requiredColumns = ['رمز الحساب', 'اسم الحساب', 'نوع الحساب', 'المستوى'];
      const firstRow = jsonData[0];
      const missingColumns = requiredColumns.filter(col => !(col in firstRow));

      if (missingColumns.length > 0) {
        alert(`الأعمدة التالية مفقودة في الملف: ${missingColumns.join(', ')}\n\nالأعمدة المطلوبة: ${requiredColumns.join(', ')}`);
        return;
      }

      // معالجة البيانات وإدراجها
      const importResults = await processImportData(jsonData);

      // إعادة تحميل البيانات
      await loadAccounts();

      alert(`تم استيراد البيانات بنجاح!\n\nالنتائج:\n- تم إضافة: ${importResults.added} حساب\n- تم تحديث: ${importResults.updated} حساب\n- تم تجاهل: ${importResults.skipped} حساب (مكرر أو خطأ)`);

    } catch (error) {
      console.error('خطأ في استيراد البيانات:', error);
      alert('حدث خطأ أثناء استيراد البيانات. تأكد من صحة تنسيق الملف.');
    } finally {
      setLoading(false);
    }
  };

  const processImportData = async (jsonData) => {
    let added = 0, updated = 0, skipped = 0;

    // إنشاء خريطة للحسابات الموجودة
    const existingAccounts = new Map();
    accounts.forEach(acc => {
      existingAccounts.set(acc.code, acc);
    });

    for (const row of jsonData) {
      try {
        const accountCode = String(row['رمز الحساب']).trim();
        const accountName = String(row['اسم الحساب']).trim();
        const accountType = String(row['نوع الحساب']).trim();
        const level = parseInt(row['المستوى']) || 1;
        const parentCode = row['رمز الحساب الأب'] ? String(row['رمز الحساب الأب']).trim() : '';
        const balance = parseFloat(row['الرصيد']) || 0;
        const isActive = row['نشط'] !== 'لا';

        // التحقق من صحة البيانات
        if (!accountCode || !accountName || !accountType) {
          skipped++;
          continue;
        }

        // البحث عن الحساب الأب
        let parentId = null;
        if (parentCode) {
          const parentAccount = existingAccounts.get(parentCode) ||
                               accounts.find(acc => acc.code === parentCode);
          if (parentAccount) {
            parentId = parentAccount.id;
          }
        }

        // التحقق من وجود الحساب
        const existingAccount = existingAccounts.get(accountCode);

        if (existingAccount) {
          // تحديث الحساب الموجود
          await db.accounts.update(existingAccount.id, {
            name: accountName,
            type: accountType,
            level: level,
            parentId: parentId,
            balance: balance,
            isActive: isActive,
            updatedAt: new Date()
          });
          updated++;
        } else {
          // إضافة حساب جديد
          const newAccount = await db.accounts.add({
            code: accountCode,
            name: accountName,
            type: accountType,
            level: level,
            parentId: parentId,
            balance: balance,
            isActive: isActive,
            createdAt: new Date()
          });

          // إضافة الحساب الجديد للخريطة
          existingAccounts.set(accountCode, { id: newAccount, code: accountCode });
          added++;
        }

      } catch (error) {
        console.error('خطأ في معالجة السطر:', row, error);
        skipped++;
      }
    }

    return { added, updated, skipped };
  };

  const downloadTemplate = () => {
    try {
      // إنشاء بيانات نموذجية
      const templateData = [
        {
          'رمز الحساب': '1000',
          'اسم الحساب': 'الأصول',
          'نوع الحساب': 'أصول',
          'المستوى': 1,
          'رمز الحساب الأب': '',
          'اسم الحساب الأب': '',
          'الرصيد': 0,
          'نشط': 'نعم',
          'ملاحظات': 'حساب رئيسي للأصول'
        },
        {
          'رمز الحساب': '1100',
          'اسم الحساب': 'الأصول المتداولة',
          'نوع الحساب': 'أصول',
          'المستوى': 2,
          'رمز الحساب الأب': '1000',
          'اسم الحساب الأب': 'الأصول',
          'الرصيد': 0,
          'نشط': 'نعم',
          'ملاحظات': 'مجموعة الأصول المتداولة'
        },
        {
          'رمز الحساب': '1110',
          'اسم الحساب': 'النقدية',
          'نوع الحساب': 'أصول',
          'المستوى': 3,
          'رمز الحساب الأب': '1100',
          'اسم الحساب الأب': 'الأصول المتداولة',
          'الرصيد': 50000,
          'نشط': 'نعم',
          'ملاحظات': 'النقدية في الصندوق والبنك'
        }
      ];

      // إنشاء ورقة عمل للتعليمات
      const instructionsData = [
        ['تعليمات استيراد دليل الحسابات'],
        [''],
        ['الأعمدة المطلوبة:'],
        ['1. رمز الحساب - رقم فريد للحساب (مطلوب)'],
        ['2. اسم الحساب - اسم الحساب (مطلوب)'],
        ['3. نوع الحساب - نوع الحساب (أصول، خصوم، حقوق ملكية، إيرادات، مصروفات) (مطلوب)'],
        ['4. المستوى - مستوى الحساب في الشجرة (1-5) (مطلوب)'],
        ['5. رمز الحساب الأب - رمز الحساب الأب (اختياري)'],
        ['6. اسم الحساب الأب - اسم الحساب الأب (اختياري)'],
        ['7. الرصيد - الرصيد الافتتاحي (اختياري)'],
        ['8. نشط - حالة الحساب (نعم/لا) (اختياري)'],
        ['9. ملاحظات - ملاحظات إضافية (اختياري)'],
        [''],
        ['ملاحظات مهمة:'],
        ['- يجب أن يكون رمز الحساب فريد'],
        ['- يجب إنشاء الحسابات الأب قبل الحسابات الفرعية'],
        ['- المستوى 1 للحسابات الرئيسية، 2-5 للحسابات الفرعية'],
        ['- إذا لم يتم تحديد "نشط"، سيكون الحساب نشط افتراضياً'],
        ['- إذا كان الحساب موجود، سيتم تحديثه'],
        ['- إذا كان الحساب غير موجود، سيتم إنشاؤه']
      ];

      // إنشاء ملف Excel
      const wb = XLSX.utils.book_new();

      // إضافة ورقة البيانات النموذجية
      const ws1 = XLSX.utils.json_to_sheet(templateData);
      XLSX.utils.book_append_sheet(wb, ws1, 'نموذج البيانات');

      // إضافة ورقة التعليمات
      const ws2 = XLSX.utils.aoa_to_sheet(instructionsData);
      XLSX.utils.book_append_sheet(wb, ws2, 'التعليمات');

      const fileName = `نموذج_استيراد_دليل_الحسابات_${new Date().toISOString().split('T')[0]}.xlsx`;
      XLSX.writeFile(wb, fileName);

      alert('تم تحميل النموذج بنجاح! يمكنك تعديل البيانات في ورقة "نموذج البيانات" ثم استيرادها.');

    } catch (error) {
      console.error('خطأ في إنشاء النموذج:', error);
      alert('حدث خطأ أثناء إنشاء النموذج');
    }
  };

  if (loading) {
    return (
      <div className="container">
        <div className="loading">
          <div className="spinner"></div>
        </div>
      </div>
    );
  }

  const filteredAccounts = filterAccounts(accounts);
  const accountTree = buildAccountTree(filteredAccounts);

  const renderTabContent = () => {
    switch (currentTab) {
      case 'accounts':
        return renderAccountsTab();
      case 'links':
        return <AccountLinks />;
      case 'reports':
        return renderReportsTab();
      default:
        return renderAccountsTab();
    }
  };

  const renderAccountsTab = () => (
    <div>
      <div className="card">
        <div style={{ marginBottom: '1.5rem' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
            <div className="card-title">دليل الحسابات</div>
            <div style={{ display: 'flex', gap: '0.5rem', flexWrap: 'wrap' }}>
              <button
                className="btn btn-secondary"
                onClick={downloadTemplate}
                title="تحميل ملف Excel نموذجي للاستيراد"
              >
                📋 تحميل نموذج
              </button>
              <button
                className="btn btn-info"
                onClick={importFromExcel}
                disabled={loading}
                title="استيراد الحسابات من ملف Excel"
              >
                📥 استيراد Excel
              </button>
              <button
                className="btn btn-success"
                onClick={exportToExcel}
                title="تصدير دليل الحسابات إلى Excel"
              >
                📤 تصدير Excel
              </button>
              <button
                className="btn btn-warning"
                onClick={async () => {
                  if (confirm('هل تريد تنظيف الحسابات المكررة؟')) {
                    await cleanupDuplicateAccounts();
                    await loadAccounts();
                    alert('تم تنظيف الحسابات المكررة بنجاح');
                  }
                }}
              >
                تنظيف المكررات
              </button>
              <button
                className="btn btn-primary"
                onClick={() => {
                  setFormData({
                    code: String(accounts.filter(acc => acc.level === 1).length + 1),
                    name: '',
                    parentId: null,
                    type: '',
                    level: 1
                  });
                  setShowAddForm(true);
                }}
              >
                إضافة حساب رئيسي
              </button>
            </div>
          </div>

          {/* رسالة توضيحية حول الاستيراد */}
          <div style={{
            background: '#e7f3ff',
            border: '1px solid #b3d9ff',
            borderRadius: '5px',
            padding: '0.75rem',
            fontSize: '0.9rem',
            color: '#0066cc',
            marginTop: '1rem'
          }}>
            <strong>💡 استيراد الحسابات من Excel:</strong>
            <span style={{ marginLeft: '0.5rem' }}>
              1. احمل النموذج أولاً
              2. عدل البيانات في ورقة "نموذج البيانات"
              3. احفظ الملف واستيرده
            </span>
          </div>
        </div>

        {/* أدوات البحث والتصفية */}
        <div className="grid grid-4" style={{ marginBottom: '1.5rem' }}>
          <div className="form-group">
            <label className="form-label">البحث</label>
            <input
              type="text"
              className="form-control"
              placeholder="ابحث بالاسم أو الرمز..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <div className="form-group">
            <label className="form-label">تصفية حسب النوع</label>
            <select
              className="form-control"
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
            >
              <option value="">جميع الأنواع</option>
              <option value="أصول">أصول</option>
              <option value="التزامات">التزامات</option>
              <option value="حقوق ملكية">حقوق ملكية</option>
              <option value="إيرادات">إيرادات</option>
              <option value="مصروفات">مصروفات</option>
            </select>
          </div>

          <div className="form-group">
            <label className="form-label">تصفية حسب المستوى</label>
            <select
              className="form-control"
              value={filterLevel}
              onChange={(e) => setFilterLevel(e.target.value)}
            >
              <option value="">جميع المستويات</option>
              <option value="1">المستوى 1</option>
              <option value="2">المستوى 2</option>
              <option value="3">المستوى 3</option>
              <option value="4">المستوى 4</option>
              <option value="5">المستوى 5</option>
            </select>
          </div>

          <div className="form-group">
            <label className="form-label">إجراءات</label>
            <button
              className="btn btn-secondary"
              style={{ width: '100%' }}
              onClick={() => {
                setSearchTerm('');
                setFilterType('');
                setFilterLevel('');
              }}
            >
              مسح التصفية
            </button>
          </div>
        </div>

        {/* شجرة الحسابات */}
        <div style={{ marginBottom: '2rem' }}>
          {accountTree.map(account => renderAccountNode(account))}
        </div>
      </div>

      {/* نموذج إضافة/تعديل حساب */}
      {showAddForm && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            padding: '2rem',
            borderRadius: '10px',
            width: '90%',
            maxWidth: '500px',
            maxHeight: '90vh',
            overflow: 'auto'
          }}>
            <h3 style={{ marginBottom: '1.5rem' }}>
              {editingAccount ? 'تعديل حساب' : 'إضافة حساب جديد'}
            </h3>

            <form onSubmit={handleSubmit}>
              <div className="form-group">
                <label className="form-label">رمز الحساب</label>
                <input
                  type="text"
                  className="form-control"
                  value={formData.code}
                  onChange={(e) => setFormData({...formData, code: e.target.value})}
                  required
                />
              </div>

              <div className="form-group">
                <label className="form-label">اسم الحساب</label>
                <input
                  type="text"
                  className="form-control"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  required
                />
              </div>

              <div className="form-group">
                <label className="form-label">نوع الحساب</label>
                <select
                  className="form-control"
                  value={formData.type}
                  onChange={(e) => setFormData({...formData, type: e.target.value})}
                  required
                >
                  <option value="">اختر نوع الحساب</option>
                  {getAccountTypeOptions(formData.level).map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div style={{ display: 'flex', gap: '1rem', marginTop: '1.5rem' }}>
                <button type="submit" className="btn btn-primary">
                  {editingAccount ? 'تحديث' : 'إضافة'}
                </button>
                <button 
                  type="button" 
                  className="btn btn-secondary"
                  onClick={resetForm}
                >
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* نافذة تفاصيل الحساب */}
      {selectedAccountId && (
        <AccountDetails
          accountId={selectedAccountId}
          onClose={() => setSelectedAccountId(null)}
        />
      )}
    </div>
  );

  const renderReportsTab = () => (
    <div className="card">
      <div className="card-title">تقارير الحسابات</div>
      <div style={{ textAlign: 'center', padding: '3rem', color: '#666' }}>
        <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📊</div>
        <div style={{ fontSize: '1.2rem', marginBottom: '0.5rem' }}>
          تقارير الحسابات
        </div>
        <div>
          قريباً - ميزان المراجعة، كشف حساب، تقارير الأرصدة
        </div>
      </div>
    </div>
  );

  return (
    <div className="container">
      {/* ملخص الحسابات */}
      <AccountsSummary />

      {/* شريط التبويبات */}
      <div className="card" style={{ marginBottom: '1rem' }}>
        <div style={{ display: 'flex', borderBottom: '1px solid #dee2e6' }}>
          <button
            className={`btn ${currentTab === 'accounts' ? 'btn-primary' : 'btn-secondary'}`}
            style={{
              borderRadius: '0',
              borderBottom: currentTab === 'accounts' ? '3px solid #007bff' : 'none',
              marginBottom: '-1px'
            }}
            onClick={() => setCurrentTab('accounts')}
          >
            🌳 عرض الحسابات
          </button>

          {hasPermission('settings') && (
            <button
              className={`btn ${currentTab === 'links' ? 'btn-primary' : 'btn-secondary'}`}
              style={{
                borderRadius: '0',
                borderBottom: currentTab === 'links' ? '3px solid #007bff' : 'none',
                marginBottom: '-1px'
              }}
              onClick={() => setCurrentTab('links')}
            >
              🔗 ربط الحسابات
            </button>
          )}

          <button
            className={`btn ${currentTab === 'reports' ? 'btn-primary' : 'btn-secondary'}`}
            style={{
              borderRadius: '0',
              borderBottom: currentTab === 'reports' ? '3px solid #007bff' : 'none',
              marginBottom: '-1px'
            }}
            onClick={() => setCurrentTab('reports')}
          >
            📊 تقارير الحسابات
          </button>
        </div>
      </div>

      {/* محتوى التبويب */}
      {renderTabContent()}
    </div>
  );
};

export default ChartOfAccounts;
