import React, { useState, useEffect } from 'react';
import { db } from '../database/db';
import * as XLSX from 'xlsx';

const JournalReports = () => {
  const [journalEntries, setJournalEntries] = useState([]);
  const [filteredEntries, setFilteredEntries] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedEntry, setSelectedEntry] = useState(null);
  const [showDetails, setShowDetails] = useState(false);

  const [filters, setFilters] = useState({
    dateFrom: '',
    dateTo: '',
    type: '',
    status: '',
    searchTerm: ''
  });

  const [summary, setSummary] = useState({
    totalEntries: 0,
    totalDebit: 0,
    totalCredit: 0,
    byType: {}
  });

  useEffect(() => {
    loadJournalEntries();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [journalEntries, filters]);

  const loadJournalEntries = async () => {
    try {
      const entries = await db.journalEntries.orderBy('date').reverse().toArray();
      setJournalEntries(entries);
    } catch (error) {
      console.error('خطأ في تحميل القيود المحاسبية:', error);
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = [...journalEntries];

    // تصفية بالتاريخ
    if (filters.dateFrom) {
      filtered = filtered.filter(entry => 
        new Date(entry.date) >= new Date(filters.dateFrom)
      );
    }
    if (filters.dateTo) {
      filtered = filtered.filter(entry => 
        new Date(entry.date) <= new Date(filters.dateTo)
      );
    }

    // تصفية بالنوع
    if (filters.type) {
      filtered = filtered.filter(entry => entry.type === filters.type);
    }

    // تصفية بالحالة
    if (filters.status) {
      filtered = filtered.filter(entry => entry.status === filters.status);
    }

    // تصفية بالبحث
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      filtered = filtered.filter(entry => 
        entry.entryNumber.toLowerCase().includes(searchLower) ||
        entry.description.toLowerCase().includes(searchLower) ||
        entry.reference?.toLowerCase().includes(searchLower) ||
        entry.entries.some(e => 
          e.accountName.toLowerCase().includes(searchLower) ||
          e.description.toLowerCase().includes(searchLower)
        )
      );
    }

    setFilteredEntries(filtered);

    // حساب الملخص
    const totalDebit = filtered.reduce((sum, entry) => sum + entry.totalDebit, 0);
    const totalCredit = filtered.reduce((sum, entry) => sum + entry.totalCredit, 0);
    
    const byType = filtered.reduce((acc, entry) => {
      if (!acc[entry.type]) {
        acc[entry.type] = { count: 0, debit: 0, credit: 0 };
      }
      acc[entry.type].count++;
      acc[entry.type].debit += entry.totalDebit;
      acc[entry.type].credit += entry.totalCredit;
      return acc;
    }, {});

    setSummary({
      totalEntries: filtered.length,
      totalDebit,
      totalCredit,
      byType
    });
  };

  const getTypeLabel = (type) => {
    const types = {
      'sales': 'مبيعات',
      'purchase': 'مشتريات',
      'receipt': 'تحصيل',
      'payment': 'دفع',
      'general': 'عام',
      'adjustment': 'تسوية'
    };
    return types[type] || type;
  };

  const getStatusLabel = (status) => {
    const statuses = {
      'draft': 'مسودة',
      'posted': 'مرحل',
      'cancelled': 'ملغي'
    };
    return statuses[status] || status;
  };

  const exportToExcel = () => {
    const data = filteredEntries.map(entry => ({
      'رقم القيد': entry.entryNumber,
      'التاريخ': new Date(entry.date).toLocaleDateString('ar-EG'),
      'النوع': getTypeLabel(entry.type),
      'الوصف': entry.description,
      'المرجع': entry.reference || '',
      'إجمالي المدين': entry.totalDebit,
      'إجمالي الدائن': entry.totalCredit,
      'الحالة': getStatusLabel(entry.status),
      'تاريخ الإنشاء': new Date(entry.createdAt).toLocaleDateString('ar-EG')
    }));

    const ws = XLSX.utils.json_to_sheet(data);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'القيود المحاسبية');
    XLSX.writeFile(wb, `القيود_المحاسبية_${new Date().toLocaleDateString('ar-EG')}.xlsx`);
  };

  const exportDetailedToExcel = () => {
    const detailedData = [];
    
    filteredEntries.forEach(entry => {
      entry.entries.forEach((detail, index) => {
        detailedData.push({
          'رقم القيد': entry.entryNumber,
          'التاريخ': new Date(entry.date).toLocaleDateString('ar-EG'),
          'النوع': getTypeLabel(entry.type),
          'وصف القيد': entry.description,
          'المرجع': entry.reference || '',
          'رقم الحساب': detail.accountCode,
          'اسم الحساب': detail.accountName,
          'بيان الحساب': detail.description,
          'مدين': detail.debit || 0,
          'دائن': detail.credit || 0,
          'الحالة': getStatusLabel(entry.status)
        });
      });
    });

    const ws = XLSX.utils.json_to_sheet(detailedData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'تفاصيل القيود');
    XLSX.writeFile(wb, `تفاصيل_القيود_المحاسبية_${new Date().toLocaleDateString('ar-EG')}.xlsx`);
  };

  const viewEntryDetails = (entry) => {
    setSelectedEntry(entry);
    setShowDetails(true);
  };

  if (loading) {
    return (
      <div className="container">
        <div className="loading">
          <div className="spinner"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container">
      {/* Header */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h2 style={{ margin: 0, color: '#333' }}>📊 تقارير القيود المحاسبية</h2>
          <div style={{ display: 'flex', gap: '1rem' }}>
            <button className="btn btn-success" onClick={exportToExcel}>
              📊 تصدير ملخص
            </button>
            <button className="btn btn-info" onClick={exportDetailedToExcel}>
              📋 تصدير تفصيلي
            </button>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <h3 style={{ marginBottom: '1.5rem', color: '#007bff' }}>🔍 فلاتر البحث</h3>
        
        <div className="grid grid-3" style={{ gap: '1rem', marginBottom: '1.5rem' }}>
          <div className="form-group">
            <label className="form-label">من تاريخ</label>
            <input
              type="date"
              className="form-control"
              value={filters.dateFrom}
              onChange={(e) => setFilters({...filters, dateFrom: e.target.value})}
            />
          </div>
          
          <div className="form-group">
            <label className="form-label">إلى تاريخ</label>
            <input
              type="date"
              className="form-control"
              value={filters.dateTo}
              onChange={(e) => setFilters({...filters, dateTo: e.target.value})}
            />
          </div>

          <div className="form-group">
            <label className="form-label">نوع القيد</label>
            <select
              className="form-control"
              value={filters.type}
              onChange={(e) => setFilters({...filters, type: e.target.value})}
            >
              <option value="">جميع الأنواع</option>
              <option value="sales">مبيعات</option>
              <option value="purchase">مشتريات</option>
              <option value="receipt">تحصيل</option>
              <option value="payment">دفع</option>
              <option value="general">عام</option>
              <option value="adjustment">تسوية</option>
            </select>
          </div>
        </div>

        <div className="grid grid-2" style={{ gap: '1rem' }}>
          <div className="form-group">
            <label className="form-label">حالة القيد</label>
            <select
              className="form-control"
              value={filters.status}
              onChange={(e) => setFilters({...filters, status: e.target.value})}
            >
              <option value="">جميع الحالات</option>
              <option value="draft">مسودة</option>
              <option value="posted">مرحل</option>
              <option value="cancelled">ملغي</option>
            </select>
          </div>

          <div className="form-group">
            <label className="form-label">البحث</label>
            <input
              type="text"
              className="form-control"
              value={filters.searchTerm}
              onChange={(e) => setFilters({...filters, searchTerm: e.target.value})}
              placeholder="ابحث في رقم القيد، الوصف، المرجع، أو اسم الحساب..."
            />
          </div>
        </div>

        <div style={{ marginTop: '1rem', textAlign: 'center' }}>
          <button 
            className="btn btn-secondary"
            onClick={() => setFilters({
              dateFrom: '',
              dateTo: '',
              type: '',
              status: '',
              searchTerm: ''
            })}
          >
            🔄 مسح الفلاتر
          </button>
        </div>
      </div>

      {/* Summary */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <h3 style={{ marginBottom: '1.5rem', color: '#007bff' }}>📈 ملخص القيود</h3>
        
        <div className="grid grid-4" style={{ gap: '1rem', marginBottom: '1.5rem' }}>
          <div style={{ 
            background: '#e3f2fd', 
            padding: '1rem', 
            borderRadius: '5px', 
            textAlign: 'center' 
          }}>
            <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#1976d2' }}>
              {summary.totalEntries}
            </div>
            <div style={{ color: '#666' }}>إجمالي القيود</div>
          </div>

          <div style={{ 
            background: '#ffebee', 
            padding: '1rem', 
            borderRadius: '5px', 
            textAlign: 'center' 
          }}>
            <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#d32f2f' }}>
              {summary.totalDebit.toLocaleString('ar-EG')}
            </div>
            <div style={{ color: '#666' }}>إجمالي المدين</div>
          </div>

          <div style={{ 
            background: '#e8f5e8', 
            padding: '1rem', 
            borderRadius: '5px', 
            textAlign: 'center' 
          }}>
            <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#388e3c' }}>
              {summary.totalCredit.toLocaleString('ar-EG')}
            </div>
            <div style={{ color: '#666' }}>إجمالي الدائن</div>
          </div>

          <div style={{ 
            background: summary.totalDebit === summary.totalCredit ? '#e8f5e8' : '#ffebee', 
            padding: '1rem', 
            borderRadius: '5px', 
            textAlign: 'center' 
          }}>
            <div style={{ 
              fontSize: '1.2rem', 
              fontWeight: 'bold', 
              color: summary.totalDebit === summary.totalCredit ? '#388e3c' : '#d32f2f' 
            }}>
              {summary.totalDebit === summary.totalCredit ? '✅ متوازن' : '❌ غير متوازن'}
            </div>
            <div style={{ color: '#666' }}>حالة التوازن</div>
          </div>
        </div>

        {/* Summary by Type */}
        {Object.keys(summary.byType).length > 0 && (
          <div>
            <h4 style={{ marginBottom: '1rem', color: '#666' }}>التوزيع حسب النوع:</h4>
            <div style={{ overflow: 'auto' }}>
              <table className="table">
                <thead>
                  <tr style={{ background: '#f8f9fa' }}>
                    <th>نوع القيد</th>
                    <th>عدد القيود</th>
                    <th>إجمالي المدين</th>
                    <th>إجمالي الدائن</th>
                  </tr>
                </thead>
                <tbody>
                  {Object.entries(summary.byType).map(([type, data]) => (
                    <tr key={type}>
                      <td style={{ fontWeight: 'bold' }}>{getTypeLabel(type)}</td>
                      <td>{data.count}</td>
                      <td style={{ color: '#d32f2f', fontWeight: 'bold' }}>
                        {data.debit.toLocaleString('ar-EG')} ج.م
                      </td>
                      <td style={{ color: '#388e3c', fontWeight: 'bold' }}>
                        {data.credit.toLocaleString('ar-EG')} ج.م
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>

      {/* Journal Entries Table */}
      <div className="card">
        <h3 style={{ marginBottom: '1.5rem', color: '#007bff' }}>📋 قائمة القيود المحاسبية</h3>

        <div style={{ overflow: 'auto' }}>
          <table className="table">
            <thead>
              <tr style={{ background: '#007bff', color: 'white' }}>
                <th>رقم القيد</th>
                <th>التاريخ</th>
                <th>النوع</th>
                <th>الوصف</th>
                <th>المرجع</th>
                <th>إجمالي المدين</th>
                <th>إجمالي الدائن</th>
                <th>الحالة</th>
                <th>الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {filteredEntries.map(entry => (
                <tr key={entry.id} style={{
                  background: entry.status === 'cancelled' ? '#ffebee' : 'white'
                }}>
                  <td style={{
                    fontFamily: 'monospace',
                    fontWeight: 'bold',
                    color: '#007bff'
                  }}>
                    {entry.entryNumber}
                  </td>
                  <td>{new Date(entry.date).toLocaleDateString('ar-EG')}</td>
                  <td>
                    <span style={{
                      padding: '0.25rem 0.5rem',
                      borderRadius: '3px',
                      fontSize: '0.8rem',
                      fontWeight: 'bold',
                      background: getTypeColor(entry.type),
                      color: 'white'
                    }}>
                      {getTypeLabel(entry.type)}
                    </span>
                  </td>
                  <td style={{ maxWidth: '200px', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                    {entry.description}
                  </td>
                  <td>{entry.reference || '-'}</td>
                  <td style={{ color: '#d32f2f', fontWeight: 'bold' }}>
                    {entry.totalDebit.toLocaleString('ar-EG')} ج.م
                  </td>
                  <td style={{ color: '#388e3c', fontWeight: 'bold' }}>
                    {entry.totalCredit.toLocaleString('ar-EG')} ج.م
                  </td>
                  <td>
                    <span style={{
                      padding: '0.25rem 0.5rem',
                      borderRadius: '3px',
                      fontSize: '0.8rem',
                      fontWeight: 'bold',
                      background: getStatusColor(entry.status),
                      color: 'white'
                    }}>
                      {getStatusLabel(entry.status)}
                    </span>
                  </td>
                  <td>
                    <button
                      className="btn btn-sm btn-info"
                      onClick={() => viewEntryDetails(entry)}
                      style={{ padding: '0.25rem 0.5rem' }}
                    >
                      👁️ عرض
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {filteredEntries.length === 0 && (
            <div style={{
              textAlign: 'center',
              padding: '3rem',
              color: '#666',
              background: '#f8f9fa',
              borderRadius: '5px'
            }}>
              <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📋</div>
              <div style={{ fontSize: '1.2rem', fontWeight: 'bold', marginBottom: '0.5rem' }}>
                لا توجد قيود محاسبية
              </div>
              <div>لم يتم العثور على قيود تطابق معايير البحث المحددة</div>
            </div>
          )}
        </div>
      </div>

      {/* Entry Details Modal */}
      {showDetails && selectedEntry && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            padding: '2rem',
            borderRadius: '10px',
            width: '90%',
            maxWidth: '900px',
            maxHeight: '90vh',
            overflow: 'auto'
          }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '1.5rem',
              borderBottom: '2px solid #007bff',
              paddingBottom: '1rem'
            }}>
              <h3 style={{ margin: 0, color: '#007bff' }}>
                📋 تفاصيل القيد المحاسبي
              </h3>
              <button
                className="btn btn-secondary"
                onClick={() => setShowDetails(false)}
              >
                ✕ إغلاق
              </button>
            </div>

            {/* Entry Info */}
            <div style={{
              background: '#f8f9fa',
              padding: '1.5rem',
              borderRadius: '5px',
              marginBottom: '1.5rem'
            }}>
              <div className="grid grid-3" style={{ gap: '1rem', marginBottom: '1rem' }}>
                <div>
                  <strong>رقم القيد:</strong> {selectedEntry.entryNumber}
                </div>
                <div>
                  <strong>التاريخ:</strong> {new Date(selectedEntry.date).toLocaleDateString('ar-EG')}
                </div>
                <div>
                  <strong>النوع:</strong> {getTypeLabel(selectedEntry.type)}
                </div>
              </div>
              <div className="grid grid-2" style={{ gap: '1rem', marginBottom: '1rem' }}>
                <div>
                  <strong>المرجع:</strong> {selectedEntry.reference || '-'}
                </div>
                <div>
                  <strong>الحالة:</strong>
                  <span style={{
                    marginLeft: '0.5rem',
                    padding: '0.25rem 0.5rem',
                    borderRadius: '3px',
                    fontSize: '0.8rem',
                    fontWeight: 'bold',
                    background: getStatusColor(selectedEntry.status),
                    color: 'white'
                  }}>
                    {getStatusLabel(selectedEntry.status)}
                  </span>
                </div>
              </div>
              <div>
                <strong>الوصف:</strong> {selectedEntry.description}
              </div>
            </div>

            {/* Entry Details Table */}
            <div style={{ overflow: 'auto', marginBottom: '1.5rem' }}>
              <table className="table">
                <thead>
                  <tr style={{ background: '#007bff', color: 'white' }}>
                    <th>رقم الحساب</th>
                    <th>اسم الحساب</th>
                    <th>البيان</th>
                    <th>مدين</th>
                    <th>دائن</th>
                  </tr>
                </thead>
                <tbody>
                  {selectedEntry.entries.map((detail, index) => (
                    <tr key={index} style={{
                      background: index % 2 === 0 ? '#f8f9fa' : 'white'
                    }}>
                      <td style={{
                        fontFamily: 'monospace',
                        fontWeight: 'bold',
                        color: '#007bff'
                      }}>
                        {detail.accountCode}
                      </td>
                      <td style={{ fontWeight: 'bold' }}>
                        {detail.accountName}
                      </td>
                      <td>{detail.description}</td>
                      <td style={{
                        color: detail.debit > 0 ? '#d32f2f' : '#6c757d',
                        fontWeight: detail.debit > 0 ? 'bold' : 'normal'
                      }}>
                        {detail.debit > 0 ? detail.debit.toLocaleString('ar-EG') : '-'}
                      </td>
                      <td style={{
                        color: detail.credit > 0 ? '#388e3c' : '#6c757d',
                        fontWeight: detail.credit > 0 ? 'bold' : 'normal'
                      }}>
                        {detail.credit > 0 ? detail.credit.toLocaleString('ar-EG') : '-'}
                      </td>
                    </tr>
                  ))}
                </tbody>
                <tfoot>
                  <tr style={{ background: '#e9ecef', fontWeight: 'bold' }}>
                    <td colSpan="3" style={{ textAlign: 'center' }}>الإجمالي</td>
                    <td style={{ color: '#d32f2f' }}>
                      {selectedEntry.totalDebit.toLocaleString('ar-EG')} ج.م
                    </td>
                    <td style={{ color: '#388e3c' }}>
                      {selectedEntry.totalCredit.toLocaleString('ar-EG')} ج.م
                    </td>
                  </tr>
                </tfoot>
              </table>
            </div>

            {/* Balance Check */}
            <div style={{
              background: selectedEntry.totalDebit === selectedEntry.totalCredit ? '#d4edda' : '#f8d7da',
              border: `1px solid ${selectedEntry.totalDebit === selectedEntry.totalCredit ? '#c3e6cb' : '#f5c6cb'}`,
              padding: '1rem',
              borderRadius: '5px',
              marginBottom: '1.5rem',
              textAlign: 'center'
            }}>
              <div style={{
                color: selectedEntry.totalDebit === selectedEntry.totalCredit ? '#155724' : '#721c24',
                fontWeight: 'bold'
              }}>
                {selectedEntry.totalDebit === selectedEntry.totalCredit ? '✅ القيد متوازن' : '❌ القيد غير متوازن'}
              </div>
              <div style={{
                fontSize: '0.9rem',
                color: selectedEntry.totalDebit === selectedEntry.totalCredit ? '#155724' : '#721c24',
                marginTop: '0.5rem'
              }}>
                إجمالي المدين: {selectedEntry.totalDebit.toLocaleString('ar-EG')} ج.م |
                إجمالي الدائن: {selectedEntry.totalCredit.toLocaleString('ar-EG')} ج.م
              </div>
            </div>

            {/* Metadata */}
            <div style={{
              background: '#f8f9fa',
              padding: '1rem',
              borderRadius: '5px',
              fontSize: '0.9rem',
              color: '#666'
            }}>
              <div className="grid grid-2" style={{ gap: '1rem' }}>
                <div>
                  <strong>تاريخ الإنشاء:</strong> {new Date(selectedEntry.createdAt).toLocaleString('ar-EG')}
                </div>
                <div>
                  <strong>المستخدم:</strong> {selectedEntry.userId}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Helper functions
const getTypeColor = (type) => {
  const colors = {
    'sales': '#28a745',
    'purchase': '#dc3545',
    'receipt': '#17a2b8',
    'payment': '#fd7e14',
    'general': '#6c757d',
    'adjustment': '#6f42c1'
  };
  return colors[type] || '#6c757d';
};

const getStatusColor = (status) => {
  const colors = {
    'draft': '#ffc107',
    'posted': '#28a745',
    'cancelled': '#dc3545'
  };
  return colors[status] || '#6c757d';
};

export default JournalReports;
