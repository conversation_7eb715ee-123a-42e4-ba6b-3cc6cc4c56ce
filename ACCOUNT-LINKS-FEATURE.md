# 🔗 ميزة ربط الحسابات مع الجداول

## 📋 نظرة عامة

تم إضافة ميزة جديدة لربط الحسابات المحاسبية مع الوحدات الوظيفية في النظام. هذه الميزة تضمن التكامل الصحيح بين دليل الحسابات والعمليات المحاسبية المختلفة.

## ✨ المميزات الجديدة

### 1. **تبويب ربط الحسابات**
- تبويب جديد داخل دليل الحسابات
- واجهة سهلة لربط الحسابات مع الوحدات
- يظهر فقط للمستخدمين الذين لديهم صلاحية الإعدادات

### 2. **الحسابات المطلوب ربطها**

#### **الحسابات الإجبارية (*):**
- 🏪 **حساب المخزون** - يربط مع جميع أصناف المخزون
- 💰 **حساب ضريبة القيمة المضافة** - يستخدم في الفواتير
- 📈 **حساب المبيعات** - يستخدم في فواتير المبيعات  
- 📉 **حساب المشتريات** - يستخدم في فواتير المشتريات
- 💸 **حساب تكلفة البضاعة المباعة** - يستخدم عند البيع
- 👷 **حساب العمال والمرتبات** - يستخدم في كشوف المرتبات

#### **الحسابات الاختيارية:**
- 🚚 **حساب مصروفات النقل**
- 💳 **حساب خصومات المبيعات**
- 💳 **حساب خصومات المشتريات**
- ↩️ **حساب مردودات المبيعات**
- ↩️ **حساب مردودات المشتريات**

### 3. **التحقق من الصحة**
- ✅ التحقق من ربط الحسابات المطلوبة عند بدء النظام
- ⚠️ رسائل تحذيرية عند عدم ربط حساب مطلوب
- 🚫 منع إنشاء الفواتير إذا لم يتم ربط الحسابات المطلوبة

### 4. **إدارة الروابط**
- 💾 حفظ الروابط في جدول منفصل
- ✏️ إمكانية تعديل الروابط في أي وقت
- 🔄 إعادة تعيين الروابط للإعدادات الافتراضية
- 📊 عرض حالة كل ربط (مربوط/غير مربوط)

## 🛠️ التغييرات التقنية

### 1. **قاعدة البيانات**
```javascript
// جدول جديد لحفظ روابط الحسابات
accountLinks: '++id, linkType, accountId, accountCode, accountName, description, isRequired, createdAt, updatedAt'
```

### 2. **دوال مساعدة جديدة**
```javascript
// الحصول على الحساب المربوط
dbHelpers.getLinkedAccount(linkType)

// التحقق من الحسابات المطلوبة
dbHelpers.validateRequiredAccountLinks()

// تحديث ربط حساب
dbHelpers.updateAccountLink(linkType, accountId, accountCode, accountName)
```

### 3. **مكونات جديدة**
- `AccountLinks.jsx` - واجهة ربط الحسابات
- تحديث `ChartOfAccounts.jsx` لإضافة التبويبات
- تحديث `SalesInvoice.jsx` و `PurchaseInvoice.jsx` للتحقق من الروابط

## 📱 كيفية الاستخدام

### 1. **الوصول للميزة**
1. اذهب إلى **دليل الحسابات**
2. اختر تبويب **🔗 ربط الحسابات**
3. ستظهر قائمة بجميع الحسابات المطلوب ربطها

### 2. **ربط حساب**
1. اختر الحساب من القائمة المنسدلة
2. سيتم حفظ الربط تلقائياً
3. ستتغير حالة الحساب إلى "✓ مربوط"

### 3. **التحقق من الروابط**
- الحسابات المطلوبة تظهر بعلامة (*) حمراء
- الحسابات غير المربوطة تظهر "✗ مطلوب" أو "○ اختياري"
- رسائل تحذيرية تظهر للحسابات المفقودة

### 4. **إدارة الروابط**
- **💾 حفظ جميع الروابط** - للتأكد من حفظ التغييرات
- **🔄 تحديث البيانات** - لإعادة تحميل الروابط
- **🔄 إعادة تعيين افتراضي** - لحذف جميع الروابط وإعادة إنشائها

## ⚠️ رسائل التحذير

### عند بدء النظام:
```
⚠️ تحذير: يجب ربط الحسابات التالية:
حساب المخزون، حساب ضريبة القيمة المضافة، حساب المبيعات
```

### عند إنشاء فاتورة:
```
يجب ربط الحسابات التالية قبل إنشاء فاتورة المبيعات:
حساب المخزون، حساب المبيعات

يرجى الذهاب إلى دليل الحسابات > ربط الحسابات
```

## 🔧 الإعدادات الافتراضية

عند تشغيل النظام لأول مرة، يتم إنشاء روابط افتراضية فارغة لجميع الحسابات. يجب على المستخدم ربط الحسابات المطلوبة قبل البدء في استخدام النظام.

## 📊 فوائد الميزة

1. **التكامل الصحيح** - ضمان ربط صحيح بين الحسابات والعمليات
2. **منع الأخطاء** - تجنب إنشاء قيود محاسبية خاطئة
3. **المرونة** - إمكانية تغيير الروابط حسب احتياجات الشركة
4. **سهولة الإعداد** - واجهة بسيطة لإدارة الروابط
5. **التحقق التلقائي** - فحص الروابط عند بدء النظام وقبل العمليات

## 🚀 التطوير المستقبلي

- إضافة المزيد من أنواع الحسابات
- ربط تلقائي ذكي بناءً على أكواد الحسابات
- تقارير عن حالة الروابط
- إمكانية تصدير/استيراد إعدادات الروابط

---

**تم تطوير هذه الميزة لتحسين تجربة المستخدم وضمان دقة العمليات المحاسبية في النظام.**
