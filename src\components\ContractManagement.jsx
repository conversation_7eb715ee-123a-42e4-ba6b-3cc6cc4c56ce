import React, { useState, useEffect } from 'react';
import { db } from '../database/db';
import IssueVoucher from './IssueVoucher';

const ContractManagement = () => {
  const [currentView, setCurrentView] = useState('list');
  const [contracts, setContracts] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [items, setItems] = useState([]);
  const [workers, setWorkers] = useState([]);
  const [contractExpenses, setContractExpenses] = useState([]);
  const [contractItems, setContractItems] = useState([]);
  const [contractLabor, setContractLabor] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedContract, setSelectedContract] = useState(null);
  const [showVoucher, setShowVoucher] = useState(false);
  const [selectedVoucherId, setSelectedVoucherId] = useState(null);
  const [formData, setFormData] = useState({
    contractNumber: '',
    customerId: '',
    name: '',
    description: '',
    startDate: '',
    endDate: '',
    totalValue: 0,
    paidAmount: 0,
    status: 'active'
  });

  // نماذج إضافة المصروفات والأصناف والعمالة
  const [expenseForm, setExpenseForm] = useState({
    type: 'material',
    description: '',
    amount: 0,
    date: new Date().toISOString().split('T')[0],
    reference: ''
  });

  const [itemForm, setItemForm] = useState({
    itemId: '',
    quantity: 0,
    notes: ''
  });

  const [laborForm, setLaborForm] = useState({
    workerId: '',
    hours: 0,
    days: 0,
    date: new Date().toISOString().split('T')[0],
    description: ''
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadContracts(),
        loadCustomers(),
        loadItems(),
        loadWorkers()
      ]);
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadContracts = async () => {
    try {
      if (db.contracts) {
        const allContracts = await db.contracts.orderBy('createdAt').reverse().toArray();
        setContracts(allContracts);
      } else {
        setContracts([]);
      }
    } catch (error) {
      console.error('خطأ في تحميل العقود:', error);
      setContracts([]);
    }
  };

  const loadCustomers = async () => {
    try {
      if (db.customers) {
        const allCustomers = await db.customers.toArray();
        setCustomers(allCustomers);
      } else {
        setCustomers([]);
      }
    } catch (error) {
      console.error('خطأ في تحميل العملاء:', error);
      setCustomers([]);
    }
  };

  const loadItems = async () => {
    try {
      if (db.items) {
        const allItems = await db.items.toArray();
        setItems(allItems);
      } else {
        setItems([]);
      }
    } catch (error) {
      console.error('خطأ في تحميل الأصناف:', error);
      setItems([]);
    }
  };

  const loadWorkers = async () => {
    try {
      // تحميل جميع العمال ثم تصفيتهم محلياً لتجنب مشاكل المفاتيح
      const allWorkers = await db.workers.toArray();
      const activeWorkers = allWorkers.filter(worker => worker.isActive === true);
      setWorkers(activeWorkers);
    } catch (error) {
      console.error('خطأ في تحميل العمال:', error);
      setWorkers([]); // تعيين مصفوفة فارغة في حالة الخطأ
    }
  };

  const loadContractDetails = async (contractId) => {
    try {
      // التحقق من صحة معرف العقد
      if (!contractId || contractId === null || contractId === undefined) {
        setContractExpenses([]);
        setContractLabor([]);
        return;
      }

      // تحميل المصروفات بشكل آمن
      let expenses = [];
      try {
        if (db.contractExpenses) {
          expenses = await db.contractExpenses.where('contractId').equals(contractId).toArray();
        }
      } catch (error) {
        console.warn('تعذر تحميل مصروفات العقد:', error);
        expenses = [];
      }

      // تحميل تكاليف العمالة بشكل آمن
      let labor = [];
      try {
        if (db.laborCosts) {
          labor = await db.laborCosts.where('contractId').equals(contractId).toArray();
        }
      } catch (error) {
        console.warn('تعذر تحميل تكاليف العمالة:', error);
        labor = [];
      }

      setContractExpenses(expenses);
      setContractLabor(labor);
    } catch (error) {
      console.error('خطأ في تحميل تفاصيل العقد:', error);
      setContractExpenses([]);
      setContractLabor([]);
    }
  };

  const generateContractNumber = async () => {
    const count = await db.contracts.count();
    return `CON${String(count + 1).padStart(6, '0')}`;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      if (!formData.contractNumber) {
        formData.contractNumber = await generateContractNumber();
      }

      const contractData = {
        ...formData,
        customerId: parseInt(formData.customerId) || null,
        totalValue: parseFloat(formData.totalValue) || 0,
        paidAmount: parseFloat(formData.paidAmount) || 0,
        userId: 1,
        createdAt: new Date()
      };

      if (selectedContract) {
        await db.contracts.update(selectedContract.id, {
          ...contractData,
          updatedAt: new Date()
        });
        alert('تم تحديث العقد بنجاح');
      } else {
        await db.contracts.add(contractData);
        alert('تم إضافة العقد بنجاح');
      }

      resetForm();
      // تأخير قصير لضمان حفظ البيانات ثم إعادة التحميل
      setTimeout(() => {
        loadData(); // تحميل جميع البيانات بما في ذلك العملاء
      }, 100);
      setCurrentView('list');
    } catch (error) {
      console.error('خطأ في حفظ العقد:', error);
      alert('حدث خطأ أثناء حفظ العقد');
    }
  };

  const handleEdit = (contract) => {
    setSelectedContract(contract);
    setFormData({
      contractNumber: contract.contractNumber,
      customerId: contract.customerId ? contract.customerId.toString() : '',
      name: contract.name,
      description: contract.description,
      startDate: contract.startDate,
      endDate: contract.endDate,
      totalValue: contract.totalValue,
      paidAmount: contract.paidAmount,
      status: contract.status
    });
    setCurrentView('form');
  };

  const handleDelete = async (id) => {
    if (!confirm('هل أنت متأكد من حذف هذا العقد؟')) return;
    
    try {
      await db.contracts.delete(id);
      alert('تم حذف العقد بنجاح');
      loadContracts();
    } catch (error) {
      console.error('خطأ في حذف العقد:', error);
      alert('حدث خطأ أثناء حذف العقد');
    }
  };

  const handleAddExpense = async () => {
    if (!selectedContract) return;

    try {
      await db.contractExpenses.add({
        contractId: selectedContract.id,
        type: expenseForm.type,
        description: expenseForm.description,
        amount: parseFloat(expenseForm.amount) || 0,
        date: expenseForm.date,
        reference: expenseForm.reference,
        userId: 1,
        createdAt: new Date()
      });

      alert('تم إضافة المصروف بنجاح');
      setExpenseForm({
        type: 'material',
        description: '',
        amount: 0,
        date: new Date().toISOString().split('T')[0],
        reference: ''
      });
      loadContractDetails(selectedContract.id);
    } catch (error) {
      console.error('خطأ في إضافة المصروف:', error);
      alert('حدث خطأ أثناء إضافة المصروف');
    }
  };

  const handleAddItem = async () => {
    if (!selectedContract || !itemForm.itemId) return;

    try {
      const item = items.find(i => i.id === parseInt(itemForm.itemId));
      if (!item) {
        alert('الصنف غير موجود');
        return;
      }

      const quantity = parseFloat(itemForm.quantity) || 0;
      if (quantity <= 0) {
        alert('يرجى إدخال كمية صحيحة');
        return;
      }

      // التحقق من توفر الكمية في المخزون
      if (item.currentStock < quantity) {
        alert(`الكمية المتاحة في المخزون: ${item.currentStock} ${item.unit}`);
        return;
      }

      // خصم الكمية من المخزون
      await db.items.update(item.id, {
        currentStock: item.currentStock - quantity
      });

      // إضافة حركة مخزون
      await db.stockMovements.add({
        itemId: item.id,
        type: 'out',
        quantity: quantity,
        cost: item.lastCost || 0,
        reference: `عقد ${selectedContract.contractNumber}`,
        date: new Date(),
        userId: 1,
        notes: itemForm.notes || `صرف للعقد: ${selectedContract.name}`
      });

      // إضافة مصروف للعقد
      await db.contractExpenses.add({
        contractId: selectedContract.id,
        type: 'material',
        description: `${item.name} - ${quantity} ${item.unit}`,
        amount: quantity * (item.lastCost || 0),
        date: new Date().toISOString().split('T')[0],
        reference: `صرف مخزون`,
        userId: 1,
        createdAt: new Date()
      });

      alert('تم صرف الصنف من المخزون بنجاح');
      setItemForm({
        itemId: '',
        quantity: 0,
        notes: ''
      });
      loadContractDetails(selectedContract.id);
      loadItems(); // إعادة تحميل المخزون
    } catch (error) {
      console.error('خطأ في صرف الصنف:', error);
      alert('حدث خطأ أثناء صرف الصنف');
    }
  };

  const handleAddLabor = async () => {
    if (!selectedContract || !laborForm.workerId) return;

    try {
      const worker = workers.find(w => w.id === parseInt(laborForm.workerId));
      if (!worker) {
        alert('العامل غير موجود');
        return;
      }

      const hours = parseFloat(laborForm.hours) || 0;
      const days = parseFloat(laborForm.days) || 0;

      if (hours <= 0 && days <= 0) {
        alert('يرجى إدخال عدد الساعات أو الأيام');
        return;
      }

      // حساب التكلفة
      const hoursCost = hours * worker.hourlyRate;
      const daysCost = days * worker.dailyRate;
      const totalCost = hoursCost + daysCost;

      // إضافة تكلفة العمالة
      await db.laborCosts.add({
        contractId: selectedContract.id,
        workerId: worker.id,
        date: laborForm.date,
        hoursWorked: hours,
        hourlyRate: worker.hourlyRate,
        totalCost: totalCost,
        description: laborForm.description,
        userId: 1,
        createdAt: new Date()
      });

      // إضافة دفعة للعامل
      await db.workerPayments.add({
        workerId: worker.id,
        contractId: selectedContract.id,
        date: laborForm.date,
        hours: hours,
        days: days,
        amount: totalCost,
        description: laborForm.description,
        isPaid: false,
        userId: 1,
        createdAt: new Date()
      });

      alert('تم تسجيل العمالة بنجاح');
      setLaborForm({
        workerId: '',
        hours: 0,
        days: 0,
        date: new Date().toISOString().split('T')[0],
        description: ''
      });
      loadContractDetails(selectedContract.id);
    } catch (error) {
      console.error('خطأ في تسجيل العمالة:', error);
      alert('حدث خطأ أثناء تسجيل العمالة');
    }
  };

  const handleCompleteContract = async (contract) => {
    if (!confirm('هل أنت متأكد من إكمال العقد وتحويله إلى فاتورة إيراد؟')) return;

    try {
      // تحديث حالة العقد
      await db.contracts.update(contract.id, {
        status: 'completed',
        completedAt: new Date()
      });

      // إنشاء فاتورة إيراد
      const invoiceNumber = await generateInvoiceNumber();
      await db.salesInvoices.add({
        invoiceNumber: invoiceNumber,
        customerId: contract.customerId,
        date: new Date().toISOString().split('T')[0],
        dueDate: new Date().toISOString().split('T')[0],
        items: [{
          itemId: 0,
          description: `إيراد عقد: ${contract.name}`,
          quantity: 1,
          price: contract.totalValue,
          cost: 0,
          total: contract.totalValue
        }],
        subtotal: contract.totalValue,
        discountAmount: 0,
        taxAmount: 0,
        total: contract.totalValue,
        totalCost: 0,
        paidAmount: contract.paidAmount,
        status: 'pending',
        notes: `فاتورة إيراد من العقد: ${contract.contractNumber}`,
        hasTax: false,
        taxRate: 0,
        discountType: 'amount',
        discount: 0,
        userId: 1,
        createdAt: new Date()
      });

      alert('تم إكمال العقد وإنشاء فاتورة الإيراد بنجاح');
      loadContracts();
    } catch (error) {
      console.error('خطأ في إكمال العقد:', error);
      alert('حدث خطأ أثناء إكمال العقد');
    }
  };

  const generateInvoiceNumber = async () => {
    const count = await db.salesInvoices.count();
    return `REV${String(count + 1).padStart(6, '0')}`;
  };

  const resetForm = () => {
    setFormData({
      contractNumber: '',
      customerId: '',
      name: '',
      description: '',
      startDate: '',
      endDate: '',
      totalValue: 0,
      paidAmount: 0,
      status: 'active'
    });
    setSelectedContract(null);
  };

  const getCustomerName = (customerId) => {
    if (!customerId) return 'غير محدد';
    const customer = customers.find(c => c.id === parseInt(customerId));
    if (!customer) {
      console.log('Customer not found for ID:', customerId, 'Available customers:', customers);
    }
    return customer ? customer.name : 'غير محدد';
  };

  const getItemName = (itemId) => {
    const item = items.find(i => i.id === itemId);
    return item ? `${item.code} - ${item.name}` : 'غير محدد';
  };

  const getWorkerName = (workerId) => {
    const worker = workers.find(w => w.id === workerId);
    return worker ? worker.name : 'غير محدد';
  };

  const handleViewExpenses = (contract) => {
    setSelectedContract(contract);
    loadContractDetails(contract.id);
    setCurrentView('expenses');
  };

  const handleViewLabor = (contract) => {
    setSelectedContract(contract);
    loadContractDetails(contract.id);
    setCurrentView('labor');
  };

  const handleOpenVoucher = (contract) => {
    setSelectedContract(contract);
    setShowVoucher(true);
  };

  const handleCloseVoucher = () => {
    setShowVoucher(false);
    setSelectedContract(null);
    setSelectedVoucherId(null);
  };

  const handleVoucherSaved = () => {
    setShowVoucher(false);
    if (selectedContract) {
      loadContractDetails(selectedContract.id);
    }
    loadContracts();
  };

  const handleEditVoucher = (voucherId) => {
    setSelectedVoucherId(voucherId);
    setShowVoucher(true);
  };

  const filteredContracts = contracts.filter(contract =>
    contract.contractNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contract.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    getCustomerName(contract.customerId).toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return '#28a745';
      case 'completed': return '#007bff';
      case 'cancelled': return '#dc3545';
      case 'suspended': return '#ffc107';
      default: return '#6c757d';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'active': return 'نشط';
      case 'completed': return 'مكتمل';
      case 'cancelled': return 'ملغي';
      case 'suspended': return 'معلق';
      default: return 'غير محدد';
    }
  };

  if (loading) {
    return (
      <div className="container">
        <div className="loading">
          <div className="spinner"></div>
        </div>
      </div>
    );
  }

  if (currentView === 'form') {
    return (
      <div className="container">
        <div className="card">
          <div className="card-title">
            {selectedContract ? 'تعديل العقد' : 'إضافة عقد جديد'}
          </div>
          
          <form onSubmit={handleSubmit}>
            <div className="grid grid-2">
              <div className="form-group">
                <label className="form-label">رقم العقد</label>
                <input
                  type="text"
                  className="form-control"
                  value={formData.contractNumber}
                  onChange={(e) => setFormData({...formData, contractNumber: e.target.value})}
                  placeholder="سيتم إنشاؤه تلقائياً"
                />
              </div>

              <div className="form-group">
                <label className="form-label">العميل *</label>
                <select
                  className="form-control"
                  value={formData.customerId}
                  onChange={(e) => setFormData({...formData, customerId: e.target.value})}
                  required
                >
                  <option value="">اختر العميل...</option>
                  {customers.map(customer => (
                    <option key={customer.id} value={customer.id}>
                      {customer.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label className="form-label">اسم المشروع *</label>
                <input
                  type="text"
                  className="form-control"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  required
                />
              </div>

              <div className="form-group">
                <label className="form-label">حالة العقد</label>
                <select
                  className="form-control"
                  value={formData.status}
                  onChange={(e) => setFormData({...formData, status: e.target.value})}
                >
                  <option value="active">نشط</option>
                  <option value="completed">مكتمل</option>
                  <option value="cancelled">ملغي</option>
                  <option value="suspended">معلق</option>
                </select>
              </div>

              <div className="form-group">
                <label className="form-label">تاريخ البداية *</label>
                <input
                  type="date"
                  className="form-control"
                  value={formData.startDate}
                  onChange={(e) => setFormData({...formData, startDate: e.target.value})}
                  required
                />
              </div>

              <div className="form-group">
                <label className="form-label">تاريخ النهاية</label>
                <input
                  type="date"
                  className="form-control"
                  value={formData.endDate}
                  onChange={(e) => setFormData({...formData, endDate: e.target.value})}
                />
              </div>

              <div className="form-group">
                <label className="form-label">قيمة العقد *</label>
                <input
                  type="number"
                  className="form-control"
                  value={formData.totalValue}
                  onChange={(e) => setFormData({...formData, totalValue: e.target.value})}
                  min="0"
                  step="0.01"
                  required
                />
              </div>

              <div className="form-group">
                <label className="form-label">المبلغ المدفوع</label>
                <input
                  type="number"
                  className="form-control"
                  value={formData.paidAmount}
                  onChange={(e) => setFormData({...formData, paidAmount: e.target.value})}
                  min="0"
                  step="0.01"
                />
              </div>
            </div>

            <div className="form-group">
              <label className="form-label">وصف المشروع</label>
              <textarea
                className="form-control"
                rows="4"
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                placeholder="تفاصيل المشروع والمتطلبات..."
              />
            </div>

            <div style={{ display: 'flex', gap: '1rem', marginTop: '2rem' }}>
              <button type="submit" className="btn btn-primary">
                {selectedContract ? 'تحديث العقد' : 'إضافة العقد'}
              </button>
              <button 
                type="button" 
                className="btn btn-secondary"
                onClick={() => {
                  resetForm();
                  setCurrentView('list');
                }}
              >
                إلغاء
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  }

  return (
    <div className="container">
      {/* Header */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h2 style={{ margin: 0, color: '#333' }}>إدارة العقود</h2>
          <button
            className="btn btn-primary"
            onClick={() => setCurrentView('form')}
          >
            ➕ إضافة عقد جديد
          </button>
        </div>
      </div>

      {/* Search */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div className="form-group">
          <label className="form-label">البحث</label>
          <input
            type="text"
            className="form-control"
            placeholder="ابحث برقم العقد أو اسم المشروع أو العميل..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {/* Contracts List */}
      <div className="card">
        <div className="card-title">
          قائمة العقود ({filteredContracts.length})
        </div>
        
        {filteredContracts.length > 0 ? (
          <div style={{ overflow: 'auto' }}>
            <table className="table">
              <thead>
                <tr>
                  <th>رقم العقد</th>
                  <th>العميل</th>
                  <th>اسم المشروع</th>
                  <th>تاريخ البداية</th>
                  <th>تاريخ النهاية</th>
                  <th>قيمة العقد</th>
                  <th>المدفوع</th>
                  <th>المتبقي</th>
                  <th>الحالة</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {filteredContracts.map(contract => {
                  const remaining = contract.totalValue - contract.paidAmount;
                  return (
                    <tr key={contract.id}>
                      <td><strong>{contract.contractNumber}</strong></td>
                      <td>{getCustomerName(contract.customerId)}</td>
                      <td>{contract.name}</td>
                      <td>{new Date(contract.startDate).toLocaleDateString('ar-EG')}</td>
                      <td>
                        {contract.endDate ? 
                          new Date(contract.endDate).toLocaleDateString('ar-EG') : 
                          'غير محدد'
                        }
                      </td>
                      <td style={{ fontWeight: 'bold', color: '#007bff' }}>
                        {contract.totalValue.toLocaleString('ar-EG')} ج.م
                      </td>
                      <td style={{ color: '#28a745' }}>
                        {contract.paidAmount.toLocaleString('ar-EG')} ج.م
                      </td>
                      <td style={{ 
                        color: remaining > 0 ? '#dc3545' : '#28a745',
                        fontWeight: 'bold'
                      }}>
                        {remaining.toLocaleString('ar-EG')} ج.م
                      </td>
                      <td>
                        <span style={{
                          padding: '0.25rem 0.5rem',
                          borderRadius: '4px',
                          fontSize: '0.8rem',
                          background: getStatusColor(contract.status) + '20',
                          color: getStatusColor(contract.status),
                          border: `1px solid ${getStatusColor(contract.status)}`
                        }}>
                          {getStatusText(contract.status)}
                        </span>
                      </td>
                      <td>
                        <div style={{ display: 'flex', gap: '0.5rem' }}>
                          <button
                            className="btn btn-primary"
                            style={{ padding: '0.25rem 0.5rem', fontSize: '0.8rem' }}
                            onClick={() => handleEdit(contract)}
                          >
                            تعديل
                          </button>
                          <button
                            className="btn btn-info"
                            style={{ padding: '0.25rem 0.5rem', fontSize: '0.8rem' }}
                            onClick={() => handleOpenVoucher(contract)}
                            title="إذن صرف"
                          >
                            📋 إذن صرف
                          </button>
                          <button
                            className="btn btn-secondary"
                            style={{ padding: '0.25rem 0.5rem', fontSize: '0.8rem' }}
                            onClick={() => handleViewExpenses(contract)}
                            title="عرض المصروفات"
                          >
                            📊 المصروفات
                          </button>
                          {contract.status === 'active' && (
                            <button
                              className="btn btn-success"
                              style={{ padding: '0.25rem 0.5rem', fontSize: '0.8rem' }}
                              onClick={() => handleCompleteContract(contract)}
                            >
                              إكمال
                            </button>
                          )}
                          <button
                            className="btn btn-danger"
                            style={{ padding: '0.25rem 0.5rem', fontSize: '0.8rem' }}
                            onClick={() => handleDelete(contract.id)}
                          >
                            حذف
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        ) : (
          <div style={{ textAlign: 'center', color: '#666', padding: '2rem' }}>
            لا توجد عقود تطابق معايير البحث
          </div>
        )}
      </div>

      {/* شاشة إدارة المصروفات */}
      {currentView === 'expenses' && selectedContract && (
        <div className="card" style={{ marginTop: '2rem' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1.5rem' }}>
            <h3 style={{ margin: 0 }}>
              مصروفات العقد: {selectedContract.name}
            </h3>
            <button
              className="btn btn-secondary"
              onClick={() => setCurrentView('list')}
            >
              ← العودة
            </button>
          </div>

          {/* أزرار الإجراءات */}
          <div style={{
            display: 'flex',
            gap: '1rem',
            marginBottom: '2rem',
            padding: '1rem',
            background: '#f8f9fa',
            borderRadius: '5px'
          }}>
            <button
              className="btn btn-primary"
              onClick={() => setShowIssueVoucher(true)}
              style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}
            >
              📋 إضافة إذن صرف جديد
            </button>
            <div style={{ fontSize: '0.9rem', color: '#666', alignSelf: 'center' }}>
              استخدم إذن الصرف لإضافة المواد والعمالة معاً في إذن واحد
            </div>
          </div>

          {/* قائمة أذون الصرف والمصروفات */}
          <div className="card-title">أذون الصرف والمصروفات</div>
          {contractExpenses.length > 0 ? (
            <div style={{ overflow: 'auto' }}>
              <table className="table">
                <thead>
                  <tr>
                    <th>التاريخ</th>
                    <th>رقم الإذن</th>
                    <th>النوع</th>
                    <th>الوصف</th>
                    <th>طالب الصرف</th>
                    <th>القسم</th>
                    <th>المبلغ</th>
                    <th>الحالة</th>
                    <th>إجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {contractExpenses.map(expense => (
                    <tr key={expense.id}>
                      <td>{new Date(expense.date).toLocaleDateString('ar-EG')}</td>
                      <td style={{ fontWeight: 'bold', color: '#007bff' }}>
                        {expense.voucherNumber || expense.reference || '-'}
                      </td>
                      <td>
                        <span style={{
                          padding: '0.25rem 0.5rem',
                          borderRadius: '4px',
                          fontSize: '0.8rem',
                          background: expense.type === 'voucher' ? '#e1f5fe' :
                                    expense.type === 'material' ? '#d4edda' :
                                    expense.type === 'labor' ? '#fff3cd' : '#d1ecf1',
                          color: expense.type === 'voucher' ? '#01579b' :
                               expense.type === 'material' ? '#155724' :
                               expense.type === 'labor' ? '#856404' : '#0c5460'
                        }}>
                          {expense.type === 'voucher' ? '📋 إذن صرف' :
                           expense.type === 'material' ? '📦 مواد' :
                           expense.type === 'labor' ? '👷 عمالة' :
                           expense.type === 'equipment' ? '🔧 معدات' :
                           expense.type === 'transport' ? '🚛 نقل' : '📝 أخرى'}
                        </span>
                      </td>
                      <td>{expense.description}</td>
                      <td>{expense.requestedBy || '-'}</td>
                      <td>
                        {expense.department ? (
                          <span style={{
                            padding: '0.2rem 0.4rem',
                            borderRadius: '3px',
                            fontSize: '0.75rem',
                            background: '#f8f9fa',
                            color: '#495057'
                          }}>
                            {expense.department === 'construction' ? 'إنشاءات' :
                             expense.department === 'finishing' ? 'تشطيبات' :
                             expense.department === 'electrical' ? 'كهرباء' :
                             expense.department === 'plumbing' ? 'سباكة' :
                             expense.department === 'maintenance' ? 'صيانة' : 'أخرى'}
                          </span>
                        ) : '-'}
                      </td>
                      <td style={{ fontWeight: 'bold', color: '#dc3545' }}>
                        {expense.amount.toLocaleString('ar-EG')} ج.م
                      </td>
                      <td>
                        <span style={{
                          padding: '0.2rem 0.4rem',
                          borderRadius: '3px',
                          fontSize: '0.75rem',
                          background: expense.status === 'issued' ? '#d4edda' :
                                    expense.status === 'approved' ? '#fff3cd' : '#f8d7da',
                          color: expense.status === 'issued' ? '#155724' :
                               expense.status === 'approved' ? '#856404' : '#721c24'
                        }}>
                          {expense.status === 'issued' ? 'مصروف' :
                           expense.status === 'approved' ? 'موافق عليه' : 'معلق'}
                        </span>
                      </td>
                      <td>
                        {expense.type === 'voucher' && expense.items && (
                          <div style={{ display: 'flex', gap: '0.5rem' }}>
                            <button
                              className="btn btn-info"
                              style={{ padding: '0.2rem 0.4rem', fontSize: '0.7rem' }}
                              onClick={() => {
                                const itemsDetails = expense.items.map(item =>
                                  `${item.type === 'material' ? '📦' : '👷'} ${item.description}: ${item.quantity} × ${item.unitCost} = ${item.totalCost} ج.م`
                                ).join('\n');
                                alert(`تفاصيل الإذن ${expense.voucherNumber}:\n\n${itemsDetails}\n\nإجمالي: ${expense.amount} ج.م`);
                              }}
                            >
                              📋 التفاصيل
                            </button>
                            <button
                              className="btn btn-warning"
                              style={{ padding: '0.2rem 0.4rem', fontSize: '0.7rem' }}
                              onClick={() => handleEditVoucher(expense.id)}
                            >
                              ✏️ تعديل
                            </button>
                          </div>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
                <tfoot>
                  <tr style={{ background: '#f8f9fa', fontWeight: 'bold' }}>
                    <td colSpan="6">إجمالي المصروفات:</td>
                    <td style={{ color: '#dc3545', fontSize: '1.1rem' }}>
                      {contractExpenses.reduce((sum, expense) => sum + expense.amount, 0).toLocaleString('ar-EG')} ج.م
                    </td>
                    <td colSpan="2"></td>
                  </tr>
                </tfoot>
              </table>
            </div>
          ) : (
            <div style={{ textAlign: 'center', color: '#666', padding: '2rem' }}>
              لا توجد أذون صرف أو مصروفات مسجلة لهذا العقد
            </div>
          )}

          {/* ملخص التكاليف */}
          {contractExpenses.length > 0 && (
            <div style={{
              marginTop: '2rem',
              padding: '1.5rem',
              background: '#f8f9fa',
              borderRadius: '8px',
              border: '1px solid #dee2e6'
            }}>
              <h4 style={{ margin: '0 0 1rem 0', color: '#495057' }}>ملخص تكاليف العقد</h4>

              <div className="grid grid-2">
                <div style={{
                  padding: '1rem',
                  background: '#e8f5e8',
                  borderRadius: '5px',
                  textAlign: 'center'
                }}>
                  <div style={{ fontSize: '0.9rem', color: '#155724', marginBottom: '0.5rem' }}>📦 تكلفة المواد</div>
                  <div style={{ fontSize: '1.2rem', fontWeight: 'bold', color: '#155724' }}>
                    {contractExpenses
                      .filter(e => e.type === 'material' || (e.type === 'voucher' && e.items?.some(i => i.type === 'material')))
                      .reduce((sum, expense) => {
                        if (expense.type === 'voucher' && expense.items) {
                          return sum + expense.items.filter(i => i.type === 'material').reduce((s, i) => s + i.totalCost, 0);
                        }
                        return sum + (expense.type === 'material' ? expense.amount : 0);
                      }, 0)
                      .toLocaleString('ar-EG')} ج.م
                  </div>
                </div>

                <div style={{
                  padding: '1rem',
                  background: '#fff3cd',
                  borderRadius: '5px',
                  textAlign: 'center'
                }}>
                  <div style={{ fontSize: '0.9rem', color: '#856404', marginBottom: '0.5rem' }}>👷 تكلفة العمالة</div>
                  <div style={{ fontSize: '1.2rem', fontWeight: 'bold', color: '#856404' }}>
                    {contractExpenses
                      .filter(e => e.type === 'labor' || (e.type === 'voucher' && e.items?.some(i => i.type === 'labor')))
                      .reduce((sum, expense) => {
                        if (expense.type === 'voucher' && expense.items) {
                          return sum + expense.items.filter(i => i.type === 'labor').reduce((s, i) => s + i.totalCost, 0);
                        }
                        return sum + (expense.type === 'labor' ? expense.amount : 0);
                      }, 0)
                      .toLocaleString('ar-EG')} ج.م
                  </div>
                </div>
              </div>

              <div className="grid grid-2" style={{ marginTop: '1rem' }}>
                <div style={{
                  padding: '1rem',
                  background: '#f8d7da',
                  borderRadius: '5px',
                  textAlign: 'center'
                }}>
                  <div style={{ fontSize: '0.9rem', color: '#721c24', marginBottom: '0.5rem' }}>💰 مصاريف من القيود اليومية</div>
                  <div style={{ fontSize: '1.2rem', fontWeight: 'bold', color: '#721c24' }}>
                    {contractExpenses
                      .filter(e => e.type === 'journal')
                      .reduce((sum, expense) => sum + expense.amount, 0)
                      .toLocaleString('ar-EG')} ج.م
                  </div>
                  <div style={{ fontSize: '0.7rem', color: '#721c24', marginTop: '0.25rem' }}>
                    ({contractExpenses.filter(e => e.type === 'journal').length} مصروف)
                  </div>
                </div>

                <div style={{
                  padding: '1rem',
                  background: '#d1ecf1',
                  borderRadius: '5px',
                  textAlign: 'center'
                }}>
                  <div style={{ fontSize: '0.9rem', color: '#0c5460', marginBottom: '0.5rem' }}>📝 مصروفات أخرى</div>
                  <div style={{ fontSize: '1.2rem', fontWeight: 'bold', color: '#0c5460' }}>
                    {contractExpenses
                      .filter(e => !['material', 'labor', 'voucher', 'journal'].includes(e.type))
                      .reduce((sum, expense) => sum + expense.amount, 0)
                      .toLocaleString('ar-EG')} ج.م
                  </div>
                </div>
              </div>

              <div style={{
                marginTop: '1rem',
                padding: '1rem',
                background: 'white',
                borderRadius: '5px',
                textAlign: 'center',
                border: '2px solid #007bff'
              }}>
                <div style={{ fontSize: '1rem', color: '#007bff', marginBottom: '0.5rem' }}>💰 إجمالي تكاليف العقد</div>
                <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#007bff' }}>
                  {contractExpenses.reduce((sum, expense) => sum + expense.amount, 0).toLocaleString('ar-EG')} ج.م
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* شاشة إدارة العمالة */}
      {currentView === 'labor' && selectedContract && (
        <div className="card" style={{ marginTop: '2rem' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1.5rem' }}>
            <h3 style={{ margin: 0 }}>
              عمالة العقد: {selectedContract.name}
            </h3>
            <button
              className="btn btn-secondary"
              onClick={() => setCurrentView('list')}
            >
              ← العودة
            </button>
          </div>

          {/* نموذج إضافة عمالة */}
          <div style={{
            background: '#fff3cd',
            padding: '1.5rem',
            borderRadius: '5px',
            marginBottom: '2rem'
          }}>
            <h4 style={{ margin: '0 0 1rem 0', fontSize: '1rem' }}>تسجيل عمالة جديدة</h4>

            <div className="grid grid-2">
              <div className="form-group">
                <label className="form-label">العامل</label>
                <select
                  className="form-control"
                  value={laborForm.workerId}
                  onChange={(e) => setLaborForm({...laborForm, workerId: e.target.value})}
                >
                  <option value="">اختر عامل...</option>
                  {workers.map(worker => (
                    <option key={worker.id} value={worker.id}>
                      {worker.code} - {worker.name} (يومي: {worker.dailyRate} ج.م، ساعي: {worker.hourlyRate} ج.م)
                    </option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label className="form-label">التاريخ</label>
                <input
                  type="date"
                  className="form-control"
                  value={laborForm.date}
                  onChange={(e) => setLaborForm({...laborForm, date: e.target.value})}
                />
              </div>

              <div className="form-group">
                <label className="form-label">عدد الساعات</label>
                <input
                  type="number"
                  className="form-control"
                  value={laborForm.hours}
                  onChange={(e) => setLaborForm({...laborForm, hours: e.target.value})}
                  min="0"
                  step="0.5"
                />
              </div>

              <div className="form-group">
                <label className="form-label">عدد الأيام</label>
                <input
                  type="number"
                  className="form-control"
                  value={laborForm.days}
                  onChange={(e) => setLaborForm({...laborForm, days: e.target.value})}
                  min="0"
                  step="0.5"
                />
              </div>
            </div>

            <div className="form-group">
              <label className="form-label">وصف العمل</label>
              <textarea
                className="form-control"
                rows="3"
                value={laborForm.description}
                onChange={(e) => setLaborForm({...laborForm, description: e.target.value})}
                placeholder="وصف العمل المنجز..."
              />
            </div>

            {/* معاينة التكلفة */}
            {laborForm.workerId && (
              <div style={{
                marginTop: '1rem',
                padding: '1rem',
                background: '#f8f9fa',
                borderRadius: '5px'
              }}>
                <h5 style={{ margin: '0 0 0.5rem 0', fontSize: '0.9rem' }}>معاينة التكلفة:</h5>
                {(() => {
                  const worker = workers.find(w => w.id === parseInt(laborForm.workerId));
                  if (!worker) return null;

                  const hours = parseFloat(laborForm.hours) || 0;
                  const days = parseFloat(laborForm.days) || 0;
                  const hoursCost = hours * worker.hourlyRate;
                  const daysCost = days * worker.dailyRate;
                  const totalCost = hoursCost + daysCost;

                  return (
                    <div style={{ fontSize: '0.9rem' }}>
                      <div>تكلفة الساعات: {hours} × {worker.hourlyRate} = {hoursCost.toLocaleString('ar-EG')} ج.م</div>
                      <div>تكلفة الأيام: {days} × {worker.dailyRate} = {daysCost.toLocaleString('ar-EG')} ج.م</div>
                      <div style={{ fontWeight: 'bold', marginTop: '0.5rem' }}>
                        إجمالي التكلفة: {totalCost.toLocaleString('ar-EG')} ج.م
                      </div>
                    </div>
                  );
                })()}
              </div>
            )}

            <button
              className="btn btn-warning"
              onClick={handleAddLabor}
              style={{ marginTop: '1rem' }}
            >
              تسجيل العمالة
            </button>
          </div>

          {/* قائمة العمالة */}
          <div className="card-title">قائمة العمالة</div>
          {contractLabor.length > 0 ? (
            <div style={{ overflow: 'auto' }}>
              <table className="table">
                <thead>
                  <tr>
                    <th>التاريخ</th>
                    <th>العامل</th>
                    <th>الساعات</th>
                    <th>التكلفة</th>
                    <th>الوصف</th>
                  </tr>
                </thead>
                <tbody>
                  {contractLabor.map(labor => (
                    <tr key={labor.id}>
                      <td>{new Date(labor.date).toLocaleDateString('ar-EG')}</td>
                      <td>{getWorkerName(labor.workerId)}</td>
                      <td>{labor.hoursWorked}</td>
                      <td style={{ fontWeight: 'bold', color: '#856404' }}>
                        {labor.totalCost.toLocaleString('ar-EG')} ج.م
                      </td>
                      <td>{labor.description || '-'}</td>
                    </tr>
                  ))}
                </tbody>
                <tfoot>
                  <tr style={{ background: '#f8f9fa', fontWeight: 'bold' }}>
                    <td colSpan="4">إجمالي تكلفة العمالة:</td>
                    <td style={{ color: '#856404' }}>
                      {contractLabor.reduce((sum, labor) => sum + labor.totalCost, 0).toLocaleString('ar-EG')} ج.م
                    </td>
                  </tr>
                </tfoot>
              </table>
            </div>
          ) : (
            <div style={{ textAlign: 'center', color: '#666', padding: '2rem' }}>
              لا توجد عمالة مسجلة لهذا العقد
            </div>
          )}
        </div>
      )}

      {/* نافذة إذن الصرف */}
      {showVoucher && selectedContract && (
        <IssueVoucher
          contractId={selectedContract.id}
          contract={selectedContract}
          voucherId={selectedVoucherId}
          onClose={handleCloseVoucher}
          onSave={handleVoucherSaved}
        />
      )}
    </div>
  );
};

export default ContractManagement;
