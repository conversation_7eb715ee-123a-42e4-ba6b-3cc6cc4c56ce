import React, { useState, useEffect } from 'react';
import { db } from '../database/db';

const PeriodManagement = ({ user }) => {
  const [periods, setPeriods] = useState([]);
  const [loading, setLoading] = useState(false);
  const [newPeriod, setNewPeriod] = useState({
    year: new Date().getFullYear(),
    startDate: '',
    endDate: '',
    status: 'open'
  });

  useEffect(() => {
    loadPeriods();
  }, []);

  const loadPeriods = async () => {
    try {
      setLoading(true);
      const allPeriods = await db.accountingPeriods?.toArray() || [];
      setPeriods(allPeriods.sort((a, b) => b.year - a.year));
    } catch (error) {
      console.error('خطأ في تحميل الفترات:', error);
    } finally {
      setLoading(false);
    }
  };

  const createPeriod = async () => {
    if (!newPeriod.startDate || !newPeriod.endDate) {
      alert('يرجى إدخال تاريخ البداية والنهاية');
      return;
    }

    if (new Date(newPeriod.startDate) >= new Date(newPeriod.endDate)) {
      alert('تاريخ البداية يجب أن يكون قبل تاريخ النهاية');
      return;
    }

    try {
      // التحقق من عدم وجود فترة بنفس السنة
      const existingPeriod = periods.find(p => p.year === newPeriod.year);
      if (existingPeriod) {
        alert(`الفترة المحاسبية لسنة ${newPeriod.year} موجودة بالفعل`);
        return;
      }

      const period = {
        ...newPeriod,
        createdBy: user.id,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // إنشاء جدول الفترات إذا لم يكن موجوداً
      if (!db.accountingPeriods) {
        await db.open();
      }

      await db.accountingPeriods?.add(period);
      alert('تم إنشاء الفترة المحاسبية بنجاح');
      
      setNewPeriod({
        year: new Date().getFullYear() + 1,
        startDate: '',
        endDate: '',
        status: 'open'
      });
      
      await loadPeriods();
    } catch (error) {
      console.error('خطأ في إنشاء الفترة:', error);
      alert('حدث خطأ أثناء إنشاء الفترة');
    }
  };

  const closePeriod = async (periodId) => {
    if (!confirm('هل تريد إقفال هذه الفترة؟ لن يمكن إضافة أو تعديل قيود بعد الإقفال.')) {
      return;
    }

    try {
      await db.accountingPeriods?.update(periodId, {
        status: 'closed',
        closedBy: user.id,
        closedAt: new Date(),
        updatedAt: new Date()
      });

      alert('تم إقفال الفترة المحاسبية بنجاح');
      await loadPeriods();
    } catch (error) {
      console.error('خطأ في إقفال الفترة:', error);
      alert('حدث خطأ أثناء إقفال الفترة');
    }
  };

  const reopenPeriod = async (periodId) => {
    if (!confirm('هل تريد إعادة فتح هذه الفترة؟')) {
      return;
    }

    try {
      await db.accountingPeriods?.update(periodId, {
        status: 'open',
        reopenedBy: user.id,
        reopenedAt: new Date(),
        updatedAt: new Date()
      });

      alert('تم إعادة فتح الفترة المحاسبية بنجاح');
      await loadPeriods();
    } catch (error) {
      console.error('خطأ في إعادة فتح الفترة:', error);
      alert('حدث خطأ أثناء إعادة فتح الفترة');
    }
  };

  // التحقق من إمكانية إضافة قيود في فترة معينة
  const isPeriodOpen = (date) => {
    const entryYear = new Date(date).getFullYear();
    const period = periods.find(p => p.year === entryYear);
    return !period || period.status === 'open';
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '3rem' }}>
        <div>جاري التحميل...</div>
      </div>
    );
  }

  return (
    <div>
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        marginBottom: '2rem' 
      }}>
        <h2 style={{ margin: 0, color: '#2c3e50' }}>إدارة الفترات المحاسبية</h2>
      </div>

      {/* إنشاء فترة جديدة */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div className="card-title">إنشاء فترة محاسبية جديدة</div>
        
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem' }}>
          <div className="form-group">
            <label className="form-label">السنة المالية</label>
            <input
              type="number"
              className="form-control"
              value={newPeriod.year}
              onChange={(e) => setNewPeriod({...newPeriod, year: parseInt(e.target.value)})}
              min="2020"
              max="2050"
            />
          </div>

          <div className="form-group">
            <label className="form-label">تاريخ البداية</label>
            <input
              type="date"
              className="form-control"
              value={newPeriod.startDate}
              onChange={(e) => setNewPeriod({...newPeriod, startDate: e.target.value})}
            />
          </div>

          <div className="form-group">
            <label className="form-label">تاريخ النهاية</label>
            <input
              type="date"
              className="form-control"
              value={newPeriod.endDate}
              onChange={(e) => setNewPeriod({...newPeriod, endDate: e.target.value})}
            />
          </div>

          <div className="form-group" style={{ display: 'flex', alignItems: 'end' }}>
            <button
              className="btn btn-primary"
              onClick={createPeriod}
              style={{ width: '100%' }}
            >
              ➕ إنشاء فترة
            </button>
          </div>
        </div>
      </div>

      {/* قائمة الفترات */}
      <div className="card">
        <div className="card-title">الفترات المحاسبية ({periods.length})</div>

        {periods.length === 0 ? (
          <div style={{ textAlign: 'center', padding: '3rem', color: '#6c757d' }}>
            <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📅</div>
            <div style={{ fontSize: '1.2rem', marginBottom: '0.5rem' }}>لا توجد فترات محاسبية</div>
            <div>قم بإنشاء أول فترة محاسبية</div>
          </div>
        ) : (
          <div style={{ overflow: 'auto' }}>
            <table className="table">
              <thead>
                <tr>
                  <th>السنة المالية</th>
                  <th>تاريخ البداية</th>
                  <th>تاريخ النهاية</th>
                  <th>الحالة</th>
                  <th>تاريخ الإنشاء</th>
                  <th>إجراءات</th>
                </tr>
              </thead>
              <tbody>
                {periods.map(period => (
                  <tr key={period.id}>
                    <td style={{ fontWeight: 'bold', fontSize: '1.1rem' }}>
                      {period.year}
                    </td>
                    <td>{new Date(period.startDate).toLocaleDateString('ar-EG')}</td>
                    <td>{new Date(period.endDate).toLocaleDateString('ar-EG')}</td>
                    <td>
                      <span style={{
                        padding: '0.25rem 0.5rem',
                        borderRadius: '3px',
                        fontSize: '0.8rem',
                        background: period.status === 'open' ? '#d4edda' : '#f8d7da',
                        color: period.status === 'open' ? '#155724' : '#721c24'
                      }}>
                        {period.status === 'open' ? '🔓 مفتوحة' : '🔒 مقفلة'}
                      </span>
                    </td>
                    <td>{new Date(period.createdAt).toLocaleDateString('ar-EG')}</td>
                    <td>
                      <div style={{ display: 'flex', gap: '0.5rem' }}>
                        {period.status === 'open' ? (
                          <button
                            className="btn btn-warning btn-sm"
                            onClick={() => closePeriod(period.id)}
                          >
                            🔒 إقفال
                          </button>
                        ) : (
                          <button
                            className="btn btn-success btn-sm"
                            onClick={() => reopenPeriod(period.id)}
                          >
                            🔓 إعادة فتح
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default PeriodManagement;
