import React, { useState, useEffect } from 'react';
import { db } from '../database/db';
import { useAuth } from '../contexts/AuthContext';
import * as XLSX from 'xlsx';

const InvoicesView = () => {
  const { user } = useAuth();
  const [currentView, setCurrentView] = useState('sales');
  const [salesInvoices, setSalesInvoices] = useState([]);
  const [purchaseInvoices, setPurchaseInvoices] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [suppliers, setSuppliers] = useState([]);
  const [items, setItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [dateFilter, setDateFilter] = useState({
    from: '',
    to: ''
  });
  const [selectedInvoice, setSelectedInvoice] = useState(null);
  const [showPrintModal, setShowPrintModal] = useState(false);
  const [editingInvoice, setEditingInvoice] = useState(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editFormData, setEditFormData] = useState({});
  const [editItems, setEditItems] = useState([]);
  const [availableItems, setAvailableItems] = useState([]);
  const [newItemForm, setNewItemForm] = useState({
    itemId: '',
    quantity: 1,
    price: 0,
    cost: 0
  });
  const [showAddItem, setShowAddItem] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadSalesInvoices(),
        loadPurchaseInvoices(),
        loadCustomers(),
        loadSuppliers(),
        loadItems(),
        loadAvailableItems()
      ]);
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadSalesInvoices = async () => {
    try {
      const invoices = await db.salesInvoices.orderBy('createdAt').reverse().toArray();
      setSalesInvoices(invoices);
    } catch (error) {
      console.error('خطأ في تحميل فواتير المبيعات:', error);
    }
  };

  const loadPurchaseInvoices = async () => {
    try {
      const invoices = await db.purchaseInvoices.orderBy('createdAt').reverse().toArray();
      setPurchaseInvoices(invoices);
    } catch (error) {
      console.error('خطأ في تحميل فواتير المشتريات:', error);
    }
  };

  const loadCustomers = async () => {
    try {
      const allCustomers = await db.customers.toArray();
      setCustomers(allCustomers);
    } catch (error) {
      console.error('خطأ في تحميل العملاء:', error);
    }
  };

  const loadSuppliers = async () => {
    try {
      const allSuppliers = await db.suppliers.toArray();
      setSuppliers(allSuppliers);
    } catch (error) {
      console.error('خطأ في تحميل الموردين:', error);
    }
  };

  const loadItems = async () => {
    try {
      const allItems = await db.items.toArray();
      setItems(allItems);
    } catch (error) {
      console.error('خطأ في تحميل الأصناف:', error);
    }
  };

  const loadAvailableItems = async () => {
    try {
      const allItems = await db.items.toArray();
      setAvailableItems(allItems);
    } catch (error) {
      console.error('خطأ في تحميل الأصناف المتاحة:', error);
    }
  };

  const getCustomerName = (customerId) => {
    const customer = customers.find(c => c.id === customerId);
    return customer ? customer.name : 'غير محدد';
  };

  const getSupplierName = (supplierId) => {
    const supplier = suppliers.find(s => s.id === supplierId);
    return supplier ? supplier.name : 'غير محدد';
  };

  const getItemName = (itemId) => {
    const item = items.find(i => i.id === itemId);
    return item ? `${item.code} - ${item.name}` : `صنف ${itemId}`;
  };

  const filterInvoices = (invoices) => {
    return invoices.filter(invoice => {
      const matchesSearch = !searchTerm || 
        invoice.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (currentView === 'sales' ? getCustomerName(invoice.customerId) : getSupplierName(invoice.supplierId))
          .toLowerCase().includes(searchTerm.toLowerCase());

      const invoiceDate = new Date(invoice.date);
      const matchesDateFrom = !dateFilter.from || invoiceDate >= new Date(dateFilter.from);
      const matchesDateTo = !dateFilter.to || invoiceDate <= new Date(dateFilter.to);

      return matchesSearch && matchesDateFrom && matchesDateTo;
    });
  };

  const exportToExcel = () => {
    try {
      const invoices = currentView === 'sales' ? salesInvoices : purchaseInvoices;
      const filteredInvoices = filterInvoices(invoices);
      
      const exportData = filteredInvoices.map(invoice => ({
        'رقم الفاتورة': invoice.invoiceNumber,
        [currentView === 'sales' ? 'العميل' : 'المورد']: currentView === 'sales' 
          ? getCustomerName(invoice.customerId) 
          : getSupplierName(invoice.supplierId),
        'التاريخ': new Date(invoice.date).toLocaleDateString('ar-EG'),
        'المجموع الفرعي': invoice.subtotal || 0,
        'الخصم': invoice.discountAmount || 0,
        'الضريبة': invoice.taxAmount || 0,
        'الإجمالي': invoice.total || 0,
        'المدفوع': invoice.paidAmount || 0,
        'المتبقي': (invoice.total || 0) - (invoice.paidAmount || 0),
        'الحالة': invoice.status === 'pending' ? 'معلقة' :
                  invoice.status === 'posted' ? 'مرحلة' : 'مكتملة',
        'ملاحظات': invoice.notes || ''
      }));

      const ws = XLSX.utils.json_to_sheet(exportData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, currentView === 'sales' ? 'فواتير المبيعات' : 'فواتير المشتريات');
      
      const fileName = `${currentView === 'sales' ? 'فواتير_المبيعات' : 'فواتير_المشتريات'}_${new Date().toISOString().split('T')[0]}.xlsx`;
      XLSX.writeFile(wb, fileName);
      
      alert('تم تصدير الفواتير بنجاح');
    } catch (error) {
      console.error('خطأ في تصدير البيانات:', error);
      alert('حدث خطأ أثناء تصدير البيانات');
    }
  };

  const handlePrint = (invoice) => {
    setSelectedInvoice(invoice);
    setShowPrintModal(true);
  };

  const handlePostInvoice = async (invoice) => {
    if (!confirm(`هل أنت متأكد من ترحيل فاتورة ${invoice.invoiceNumber}؟ لن يمكن تعديلها بعد الترحيل.`)) return;

    try {
      const table = currentView === 'sales' ? db.salesInvoices : db.purchaseInvoices;
      await table.update(invoice.id, {
        status: 'posted',
        postedAt: new Date(),
        updatedAt: new Date()
      });

      alert('تم ترحيل الفاتورة بنجاح');
      loadData();
    } catch (error) {
      console.error('خطأ في ترحيل الفاتورة:', error);
      alert('حدث خطأ أثناء ترحيل الفاتورة');
    }
  };

  // إلغاء ترحيل الفاتورة
  const handleUnpostInvoice = async (invoice) => {
    // التحقق من صلاحية إلغاء الترحيل
    if (!user.permissions?.editPostedEntries && user.role !== 'super-admin') {
      alert('لا يمكن إلغاء ترحيل الفواتير. تحتاج إلى صلاحيات خاصة.');
      return;
    }

    if (invoice.status !== 'posted') {
      alert('هذه الفاتورة غير مرحلة');
      return;
    }

    if (!confirm(`هل تريد إلغاء ترحيل فاتورة ${invoice.invoiceNumber}؟ ستعود إلى حالة معلقة.`)) return;

    try {
      const table = currentView === 'sales' ? db.salesInvoices : db.purchaseInvoices;
      await table.update(invoice.id, {
        status: 'pending',
        unpostedAt: new Date(),
        unpostedBy: user.id,
        updatedAt: new Date()
      });

      alert('تم إلغاء ترحيل الفاتورة بنجاح');
      await loadData();
    } catch (error) {
      console.error('خطأ في إلغاء ترحيل الفاتورة:', error);
      alert('حدث خطأ أثناء إلغاء ترحيل الفاتورة');
    }
  };

  const handleEditInvoice = (invoice) => {
    if (invoice.status === 'posted') {
      // التحقق من صلاحية تعديل الفواتير المرحلة
      if (!user.permissions?.editAllData && user.role !== 'super-admin') {
        alert('لا يمكن تعديل فاتورة مرحلة. تحتاج إلى صلاحيات خاصة.');
        return;
      }

      if (!confirm('هذه الفاتورة مرحلة. هل تريد تعديلها؟ (يتطلب صلاحيات خاصة)')) {
        return;
      }
    }

    setEditingInvoice(invoice);
    setEditFormData({
      invoiceNumber: invoice.invoiceNumber,
      date: invoice.date,
      notes: invoice.notes || '',
      discountAmount: invoice.discountAmount || 0,
      hasTax: invoice.hasTax || false,
      paidAmount: invoice.paidAmount || 0
    });
    setEditItems([...invoice.items]);
    setNewItemForm({
      itemId: '',
      quantity: 1,
      price: 0,
      cost: 0
    });
    setShowAddItem(false);
    setShowEditModal(true);
  };

  const handleSaveEdit = async () => {
    try {
      const table = currentView === 'sales' ? db.salesInvoices : db.purchaseInvoices;

      // حساب الإجماليات الجديدة من الأصناف المحدثة
      const subtotal = editItems.reduce((sum, item) => sum + (item.total || 0), 0);
      const discountAmount = parseFloat(editFormData.discountAmount) || 0;
      const afterDiscount = subtotal - discountAmount;
      const taxAmount = editFormData.hasTax ? afterDiscount * 0.14 : 0;
      const total = afterDiscount + taxAmount;

      await table.update(editingInvoice.id, {
        invoiceNumber: editFormData.invoiceNumber,
        date: editFormData.date,
        notes: editFormData.notes,
        items: editItems,
        subtotal: subtotal,
        discountAmount: discountAmount,
        hasTax: editFormData.hasTax,
        taxAmount: taxAmount,
        total: total,
        paidAmount: parseFloat(editFormData.paidAmount) || 0,
        updatedAt: new Date()
      });

      alert('تم تحديث الفاتورة بنجاح');
      setShowEditModal(false);
      setEditingInvoice(null);
      loadData();
    } catch (error) {
      console.error('خطأ في تحديث الفاتورة:', error);
      alert('حدث خطأ أثناء تحديث الفاتورة');
    }
  };

  const updateItemQuantity = (index, quantity) => {
    const newItems = [...editItems];
    const item = newItems[index];
    const newQuantity = parseFloat(quantity) || 0;
    const price = currentView === 'sales' ? item.price : item.cost;

    newItems[index] = {
      ...item,
      quantity: newQuantity,
      total: newQuantity * price
    };

    setEditItems(newItems);
  };

  const removeItem = (index) => {
    const newItems = editItems.filter((_, i) => i !== index);
    setEditItems(newItems);
  };

  const handleAddNewItem = () => {
    if (!newItemForm.itemId) {
      alert('يرجى اختيار صنف');
      return;
    }

    const selectedItem = availableItems.find(item => item.id === parseInt(newItemForm.itemId));
    if (!selectedItem) {
      alert('الصنف المحدد غير موجود');
      return;
    }

    const quantity = parseFloat(newItemForm.quantity) || 0;
    const price = currentView === 'sales' ?
      (parseFloat(newItemForm.price) || selectedItem.sellingPrice || 0) :
      (parseFloat(newItemForm.cost) || selectedItem.cost || 0);

    const newItem = {
      itemId: selectedItem.id,
      quantity: quantity,
      price: currentView === 'sales' ? price : selectedItem.sellingPrice || 0,
      cost: currentView === 'sales' ? selectedItem.cost || 0 : price,
      total: quantity * price
    };

    setEditItems([...editItems, newItem]);
    setNewItemForm({
      itemId: '',
      quantity: 1,
      price: 0,
      cost: 0
    });
    setShowAddItem(false);
  };

  const handleItemSelect = (itemId) => {
    const selectedItem = availableItems.find(item => item.id === parseInt(itemId));
    if (selectedItem) {
      setNewItemForm({
        ...newItemForm,
        itemId: itemId,
        price: selectedItem.sellingPrice || 0,
        cost: selectedItem.cost || 0
      });
    }
  };

  const handleDeleteInvoice = async (invoice) => {
    if (invoice.status === 'posted') {
      alert('لا يمكن حذف فاتورة مرحلة');
      return;
    }

    if (!confirm(`هل أنت متأكد من حذف فاتورة ${invoice.invoiceNumber}؟`)) return;

    try {
      const table = currentView === 'sales' ? db.salesInvoices : db.purchaseInvoices;
      await table.delete(invoice.id);

      alert('تم حذف الفاتورة بنجاح');
      loadData();
    } catch (error) {
      console.error('خطأ في حذف الفاتورة:', error);
      alert('حدث خطأ أثناء حذف الفاتورة');
    }
  };

  const printInvoice = () => {
    const printWindow = window.open('', '_blank');
    const invoiceHtml = generateInvoiceHTML(selectedInvoice);
    
    printWindow.document.write(invoiceHtml);
    printWindow.document.close();
    printWindow.print();
    
    setShowPrintModal(false);
  };

  const generateInvoiceHTML = (invoice) => {
    const isSales = currentView === 'sales';
    const clientName = isSales ? getCustomerName(invoice.customerId) : getSupplierName(invoice.supplierId);
    
    return `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <title>فاتورة ${invoice.invoiceNumber}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
          .header { text-align: center; margin-bottom: 30px; }
          .invoice-info { display: flex; justify-content: space-between; margin-bottom: 20px; }
          .client-info { margin-bottom: 20px; }
          table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
          th { background-color: #f2f2f2; }
          .totals { margin-top: 20px; }
          .total-row { font-weight: bold; }
          @media print { body { margin: 0; } }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>فاتورة ${isSales ? 'مبيعات' : 'مشتريات'}</h1>
          <h2>${invoice.invoiceNumber}</h2>
        </div>
        
        <div class="invoice-info">
          <div>
            <strong>التاريخ:</strong> ${new Date(invoice.date).toLocaleDateString('ar-EG')}
          </div>
          <div>
            <strong>${isSales ? 'العميل' : 'المورد'}:</strong> ${clientName}
          </div>
        </div>
        
        <table>
          <thead>
            <tr>
              <th>الصنف</th>
              <th>الكمية</th>
              <th>${isSales ? 'السعر' : 'التكلفة'}</th>
              <th>الإجمالي</th>
            </tr>
          </thead>
          <tbody>
            ${invoice.items.map(item => `
              <tr>
                <td>${getItemName(item.itemId)}</td>
                <td>${item.quantity}</td>
                <td>${(isSales ? item.price : item.cost).toLocaleString('ar-EG')}</td>
                <td>${item.total.toLocaleString('ar-EG')}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
        
        <div class="totals">
          <div>المجموع الفرعي: ${invoice.subtotal.toLocaleString('ar-EG')} ج.م</div>
          <div>الخصم: ${(invoice.discountAmount || 0).toLocaleString('ar-EG')} ج.م</div>
          ${invoice.hasTax ? `<div>الضريبة: ${(invoice.taxAmount || 0).toLocaleString('ar-EG')} ج.م</div>` : ''}
          <div class="total-row">الإجمالي: ${invoice.total.toLocaleString('ar-EG')} ج.م</div>
        </div>
        
        ${invoice.notes ? `<div style="margin-top: 20px;"><strong>ملاحظات:</strong> ${invoice.notes}</div>` : ''}
      </body>
      </html>
    `;
  };

  const currentInvoices = currentView === 'sales' ? salesInvoices : purchaseInvoices;
  const filteredInvoices = filterInvoices(currentInvoices);

  if (loading) {
    return (
      <div className="container">
        <div className="loading">
          <div className="spinner"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container">
      {/* Header */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h2 style={{ margin: 0, color: '#333' }}>عرض الفواتير</h2>
          <div style={{ display: 'flex', gap: '1rem' }}>
            <button
              className="btn btn-success"
              onClick={exportToExcel}
            >
              📊 تصدير Excel
            </button>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div style={{ display: 'flex', gap: '1rem', marginBottom: '1rem' }}>
          <button
            className={`btn ${currentView === 'sales' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setCurrentView('sales')}
          >
            💰 فواتير المبيعات
          </button>
          <button
            className={`btn ${currentView === 'purchase' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setCurrentView('purchase')}
          >
            🛒 فواتير المشتريات
          </button>
        </div>

        {/* Filters */}
        <div className="grid grid-3">
          <div className="form-group">
            <label className="form-label">البحث</label>
            <input
              type="text"
              className="form-control"
              placeholder="ابحث برقم الفاتورة أو اسم العميل/المورد..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="form-group">
            <label className="form-label">من تاريخ</label>
            <input
              type="date"
              className="form-control"
              value={dateFilter.from}
              onChange={(e) => setDateFilter({...dateFilter, from: e.target.value})}
            />
          </div>
          <div className="form-group">
            <label className="form-label">إلى تاريخ</label>
            <input
              type="date"
              className="form-control"
              value={dateFilter.to}
              onChange={(e) => setDateFilter({...dateFilter, to: e.target.value})}
            />
          </div>
        </div>
      </div>

      {/* Invoices List */}
      <div className="card">
        <div className="card-title">
          {currentView === 'sales' ? 'فواتير المبيعات' : 'فواتير المشتريات'} ({filteredInvoices.length})
        </div>
        
        {filteredInvoices.length > 0 ? (
          <div style={{ overflow: 'auto' }}>
            <table className="table">
              <thead>
                <tr>
                  <th>رقم الفاتورة</th>
                  <th>{currentView === 'sales' ? 'العميل' : 'المورد'}</th>
                  <th>التاريخ</th>
                  <th>الإجمالي</th>
                  <th>المدفوع</th>
                  <th>المتبقي</th>
                  <th>الحالة</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {filteredInvoices.map(invoice => {
                  const remaining = (invoice.total || 0) - (invoice.paidAmount || 0);
                  return (
                    <tr key={invoice.id}>
                      <td><strong>{invoice.invoiceNumber}</strong></td>
                      <td>
                        {currentView === 'sales' 
                          ? getCustomerName(invoice.customerId) 
                          : getSupplierName(invoice.supplierId)
                        }
                      </td>
                      <td>{new Date(invoice.date).toLocaleDateString('ar-EG')}</td>
                      <td style={{ fontWeight: 'bold', color: '#007bff' }}>
                        {(invoice.total || 0).toLocaleString('ar-EG')} ج.م
                      </td>
                      <td style={{ color: '#28a745' }}>
                        {(invoice.paidAmount || 0).toLocaleString('ar-EG')} ج.م
                      </td>
                      <td style={{ 
                        color: remaining > 0 ? '#dc3545' : '#28a745',
                        fontWeight: 'bold'
                      }}>
                        {remaining.toLocaleString('ar-EG')} ج.م
                      </td>
                      <td>
                        <span style={{
                          padding: '0.25rem 0.5rem',
                          borderRadius: '4px',
                          fontSize: '0.8rem',
                          background: invoice.status === 'pending' ? '#fff3cd' :
                                    invoice.status === 'posted' ? '#d4edda' : '#f8d7da',
                          color: invoice.status === 'pending' ? '#856404' :
                               invoice.status === 'posted' ? '#155724' : '#721c24'
                        }}>
                          {invoice.status === 'pending' ? 'معلقة' :
                           invoice.status === 'posted' ? 'مرحلة' : 'مكتملة'}
                        </span>
                      </td>
                      <td>
                        <div style={{ display: 'flex', gap: '0.25rem', flexWrap: 'wrap' }}>
                          <button
                            className="btn btn-info"
                            style={{ padding: '0.2rem 0.4rem', fontSize: '0.75rem' }}
                            onClick={() => handlePrint(invoice)}
                          >
                            🖨️
                          </button>

                          {invoice.status === 'pending' && (
                            <>
                              <button
                                className="btn btn-success"
                                style={{ padding: '0.2rem 0.4rem', fontSize: '0.75rem' }}
                                onClick={() => handlePostInvoice(invoice)}
                                title="ترحيل الفاتورة"
                              >
                                ✅
                              </button>
                              <button
                                className="btn btn-primary"
                                style={{ padding: '0.2rem 0.4rem', fontSize: '0.75rem' }}
                                onClick={() => handleEditInvoice(invoice)}
                                title="تعديل الفاتورة"
                              >
                                ✏️
                              </button>
                              <button
                                className="btn btn-danger"
                                style={{ padding: '0.2rem 0.4rem', fontSize: '0.75rem' }}
                                onClick={() => handleDeleteInvoice(invoice)}
                                title="حذف الفاتورة"
                              >
                                🗑️
                              </button>
                            </>
                          )}

                          {invoice.status === 'posted' && (
                            <>
                              <span style={{
                                padding: '0.2rem 0.4rem',
                                fontSize: '0.75rem',
                                color: '#28a745',
                                background: '#d4edda',
                                borderRadius: '3px',
                                marginLeft: '0.25rem'
                              }}>
                                مرحلة
                              </span>

                              {/* أزرار خاصة للمدير الأعلى */}
                              {(user.role === 'super-admin' || user.permissions?.editPostedEntries) && (
                                <button
                                  className="btn btn-info btn-sm"
                                  onClick={() => handleUnpostInvoice(invoice)}
                                  title="إلغاء ترحيل الفاتورة (صلاحيات خاصة)"
                                  style={{ fontSize: '0.7rem', padding: '0.2rem 0.4rem' }}
                                >
                                  ↩️ إلغاء ترحيل
                                </button>
                              )}
                            </>
                          )}
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        ) : (
          <div style={{ textAlign: 'center', color: '#666', padding: '2rem' }}>
            لا توجد فواتير تطابق معايير البحث
          </div>
        )}
      </div>

      {/* Print Modal */}
      {showPrintModal && selectedInvoice && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            padding: '2rem',
            borderRadius: '10px',
            width: '90%',
            maxWidth: '500px'
          }}>
            <h3 style={{ marginBottom: '1.5rem' }}>طباعة الفاتورة</h3>
            <p>هل تريد طباعة فاتورة {selectedInvoice.invoiceNumber}؟</p>
            
            <div style={{ display: 'flex', gap: '1rem', marginTop: '1.5rem' }}>
              <button 
                className="btn btn-primary"
                onClick={printInvoice}
              >
                🖨️ طباعة
              </button>
              <button 
                className="btn btn-secondary"
                onClick={() => setShowPrintModal(false)}
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نافذة تعديل الفاتورة */}
      {showEditModal && editingInvoice && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            padding: '2rem',
            borderRadius: '10px',
            width: '90%',
            maxWidth: '600px',
            maxHeight: '90vh',
            overflow: 'auto'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1.5rem' }}>
              <h3 style={{ margin: 0 }}>تعديل فاتورة {editingInvoice.invoiceNumber}</h3>
              <button
                className="btn btn-secondary"
                onClick={() => setShowEditModal(false)}
              >
                ✕ إغلاق
              </button>
            </div>

            <div className="grid grid-2" style={{ gap: '1rem' }}>
              <div className="form-group">
                <label className="form-label">رقم الفاتورة</label>
                <input
                  type="text"
                  className="form-control"
                  value={editFormData.invoiceNumber}
                  onChange={(e) => setEditFormData({...editFormData, invoiceNumber: e.target.value})}
                />
              </div>

              <div className="form-group">
                <label className="form-label">التاريخ</label>
                <input
                  type="date"
                  className="form-control"
                  value={editFormData.date}
                  onChange={(e) => setEditFormData({...editFormData, date: e.target.value})}
                />
              </div>

              <div className="form-group">
                <label className="form-label">مبلغ الخصم</label>
                <input
                  type="number"
                  className="form-control"
                  value={editFormData.discountAmount}
                  onChange={(e) => setEditFormData({...editFormData, discountAmount: e.target.value})}
                  min="0"
                  step="0.01"
                />
              </div>

              <div className="form-group">
                <label className="form-label">المبلغ المدفوع</label>
                <input
                  type="number"
                  className="form-control"
                  value={editFormData.paidAmount}
                  onChange={(e) => setEditFormData({...editFormData, paidAmount: e.target.value})}
                  min="0"
                  step="0.01"
                />
              </div>
            </div>

            <div className="form-group" style={{ marginTop: '1rem' }}>
              <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <input
                  type="checkbox"
                  checked={editFormData.hasTax}
                  onChange={(e) => setEditFormData({...editFormData, hasTax: e.target.checked})}
                />
                <span>إضافة ضريبة القيمة المضافة (14%)</span>
              </label>
            </div>

            <div className="form-group">
              <label className="form-label">ملاحظات</label>
              <textarea
                className="form-control"
                rows="3"
                value={editFormData.notes}
                onChange={(e) => setEditFormData({...editFormData, notes: e.target.value})}
                placeholder="ملاحظات إضافية..."
              />
            </div>

            {/* جدول الأصناف */}
            <div style={{ marginTop: '1.5rem' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
                <h4 style={{ margin: 0, fontSize: '1rem' }}>أصناف الفاتورة:</h4>
                <button
                  className="btn btn-success"
                  style={{ padding: '0.5rem 1rem', fontSize: '0.9rem' }}
                  onClick={() => setShowAddItem(!showAddItem)}
                >
                  {showAddItem ? '✕ إلغاء' : '➕ إضافة صنف'}
                </button>
              </div>

              {/* نموذج إضافة صنف جديد */}
              {showAddItem && (
                <div style={{
                  background: '#f8f9fa',
                  padding: '1rem',
                  borderRadius: '5px',
                  marginBottom: '1rem',
                  border: '1px solid #dee2e6'
                }}>
                  <h5 style={{ margin: '0 0 1rem 0', fontSize: '0.9rem', color: '#495057' }}>إضافة صنف جديد:</h5>
                  <div className="grid grid-4" style={{ gap: '0.5rem', alignItems: 'end' }}>
                    <div className="form-group" style={{ margin: 0 }}>
                      <label className="form-label" style={{ fontSize: '0.8rem' }}>الصنف</label>
                      <select
                        className="form-control"
                        style={{ fontSize: '0.9rem' }}
                        value={newItemForm.itemId}
                        onChange={(e) => handleItemSelect(e.target.value)}
                      >
                        <option value="">اختر صنف...</option>
                        {availableItems
                          .filter(item => !editItems.some(editItem => editItem.itemId === item.id))
                          .map(item => (
                            <option key={item.id} value={item.id}>
                              {item.code} - {item.name}
                            </option>
                          ))
                        }
                      </select>
                    </div>

                    <div className="form-group" style={{ margin: 0 }}>
                      <label className="form-label" style={{ fontSize: '0.8rem' }}>الكمية</label>
                      <input
                        type="number"
                        className="form-control"
                        style={{ fontSize: '0.9rem' }}
                        value={newItemForm.quantity}
                        onChange={(e) => setNewItemForm({...newItemForm, quantity: e.target.value})}
                        min="0"
                        step="0.01"
                      />
                    </div>

                    <div className="form-group" style={{ margin: 0 }}>
                      <label className="form-label" style={{ fontSize: '0.8rem' }}>
                        {currentView === 'sales' ? 'السعر' : 'التكلفة'}
                      </label>
                      <input
                        type="number"
                        className="form-control"
                        style={{ fontSize: '0.9rem' }}
                        value={currentView === 'sales' ? newItemForm.price : newItemForm.cost}
                        onChange={(e) => setNewItemForm({
                          ...newItemForm,
                          [currentView === 'sales' ? 'price' : 'cost']: e.target.value
                        })}
                        min="0"
                        step="0.01"
                      />
                    </div>

                    <div style={{ display: 'flex', gap: '0.5rem' }}>
                      <button
                        className="btn btn-primary"
                        style={{ padding: '0.5rem 1rem', fontSize: '0.8rem' }}
                        onClick={handleAddNewItem}
                      >
                        ✅ إضافة
                      </button>
                    </div>
                  </div>

                  {newItemForm.itemId && (
                    <div style={{
                      marginTop: '0.5rem',
                      padding: '0.5rem',
                      background: '#e9ecef',
                      borderRadius: '3px',
                      fontSize: '0.8rem'
                    }}>
                      <strong>معاينة:</strong> {getItemName(parseInt(newItemForm.itemId))} × {newItemForm.quantity} = {
                        ((parseFloat(newItemForm.quantity) || 0) *
                         (currentView === 'sales' ?
                          (parseFloat(newItemForm.price) || 0) :
                          (parseFloat(newItemForm.cost) || 0)
                         )).toLocaleString('ar-EG')
                      } ج.م
                    </div>
                  )}
                </div>
              )}
              <div style={{ overflow: 'auto', maxHeight: '300px' }}>
                <table className="table" style={{ fontSize: '0.9rem' }}>
                  <thead>
                    <tr>
                      <th>الصنف</th>
                      <th>الكمية</th>
                      <th>{currentView === 'sales' ? 'السعر' : 'التكلفة'}</th>
                      <th>الإجمالي</th>
                      <th>إجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {editItems.map((item, index) => (
                      <tr key={index}>
                        <td>{getItemName(item.itemId)}</td>
                        <td>
                          <input
                            type="number"
                            className="form-control"
                            style={{ width: '80px', padding: '0.25rem' }}
                            value={item.quantity}
                            onChange={(e) => updateItemQuantity(index, e.target.value)}
                            min="0"
                            step="0.01"
                          />
                        </td>
                        <td>
                          {(currentView === 'sales' ? item.price : item.cost).toLocaleString('ar-EG')} ج.م
                        </td>
                        <td style={{ fontWeight: 'bold' }}>
                          {item.total.toLocaleString('ar-EG')} ج.م
                        </td>
                        <td>
                          <button
                            className="btn btn-danger"
                            style={{ padding: '0.2rem 0.4rem', fontSize: '0.75rem' }}
                            onClick={() => removeItem(index)}
                          >
                            🗑️
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* معاينة الإجماليات */}
            <div style={{
              marginTop: '1.5rem',
              padding: '1rem',
              background: '#f8f9fa',
              borderRadius: '5px'
            }}>
              <h4 style={{ margin: '0 0 1rem 0', fontSize: '1rem' }}>معاينة الإجماليات:</h4>
              {(() => {
                const subtotal = editItems.reduce((sum, item) => sum + (item.total || 0), 0);
                const discountAmount = parseFloat(editFormData.discountAmount) || 0;
                const afterDiscount = subtotal - discountAmount;
                const taxAmount = editFormData.hasTax ? afterDiscount * 0.14 : 0;
                const total = afterDiscount + taxAmount;

                return (
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <span>المجموع الفرعي:</span>
                      <span>{subtotal.toLocaleString('ar-EG')} ج.م</span>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <span>الخصم:</span>
                      <span>{discountAmount.toLocaleString('ar-EG')} ج.م</span>
                    </div>
                    {editFormData.hasTax && (
                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <span>الضريبة (14%):</span>
                        <span>{taxAmount.toLocaleString('ar-EG')} ج.م</span>
                      </div>
                    )}
                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      fontWeight: 'bold',
                      borderTop: '1px solid #ddd',
                      paddingTop: '0.5rem',
                      marginTop: '0.5rem'
                    }}>
                      <span>الإجمالي:</span>
                      <span>{total.toLocaleString('ar-EG')} ج.م</span>
                    </div>
                  </div>
                );
              })()}
            </div>

            <div style={{ display: 'flex', gap: '1rem', marginTop: '1.5rem' }}>
              <button
                className="btn btn-primary"
                onClick={handleSaveEdit}
              >
                💾 حفظ التعديلات
              </button>
              <button
                className="btn btn-secondary"
                onClick={() => setShowEditModal(false)}
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default InvoicesView;
