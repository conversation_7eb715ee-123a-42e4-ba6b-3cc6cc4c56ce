import React, { useState, useEffect } from 'react';
import { db, dbHelpers } from '../database/db';
import { useAuth } from '../contexts/AuthContext';
import ItemsList from './ItemsList';
import AddItemForm from './AddItemForm';
import StockMovements from './StockMovements';
// import InventoryReports from './InventoryReports';

const InventoryManagement = () => {
  const { user } = useAuth();
  const [currentView, setCurrentView] = useState('items');
  const [items, setItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalItems: 0,
    totalValue: 0,
    lowStockItems: 0,
    outOfStockItems: 0
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadItems(),
        loadStats()
      ]);
    } catch (error) {
      console.error('خطأ في تحميل بيانات المخزون:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadItems = async () => {
    try {
      const allItems = await db.items.orderBy('name').toArray();
      
      // حساب الرصيد الحالي لكل صنف
      const itemsWithStock = await Promise.all(
        allItems.map(async (item) => {
          const currentStock = await dbHelpers.getItemStock(item.id);
          const currentCost = await dbHelpers.getItemCost(item.id);
          return {
            ...item,
            currentStock,
            currentCost,
            totalValue: currentStock * currentCost
          };
        })
      );
      
      setItems(itemsWithStock);
    } catch (error) {
      console.error('خطأ في تحميل الأصناف:', error);
    }
  };

  const loadStats = async () => {
    try {
      const allItems = await db.items.toArray();
      const totalItems = allItems.length;
      
      let totalValue = 0;
      let lowStockItems = 0;
      let outOfStockItems = 0;
      
      for (const item of allItems) {
        const currentStock = await dbHelpers.getItemStock(item.id);
        const currentCost = await dbHelpers.getItemCost(item.id);
        
        totalValue += currentStock * currentCost;
        
        if (currentStock === 0) {
          outOfStockItems++;
        } else if (currentStock <= item.minStock) {
          lowStockItems++;
        }
      }
      
      setStats({
        totalItems,
        totalValue,
        lowStockItems,
        outOfStockItems
      });
    } catch (error) {
      console.error('خطأ في تحميل إحصائيات المخزون:', error);
    }
  };

  const renderContent = () => {
    switch (currentView) {
      case 'items':
        return <ItemsList items={items} onRefresh={loadData} />;
      case 'add-item':
        return <AddItemForm onSuccess={() => { setCurrentView('items'); loadData(); }} />;
      case 'movements':
        return <StockMovements onRefresh={loadData} />;
      case 'reports':
        return <div className="card"><div className="card-title">تقارير المخزون</div><p>قريباً...</p></div>;
      default:
        return <ItemsList items={items} onRefresh={loadData} />;
    }
  };

  const StatCard = ({ title, value, color, icon, subtitle }) => (
    <div className="card" style={{ borderTop: `4px solid ${color}` }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <h3 style={{ color, fontSize: '2rem', fontWeight: 'bold', margin: 0 }}>
            {typeof value === 'number' && title.includes('قيمة') 
              ? `${value.toLocaleString('ar-EG')} ج.م`
              : value
            }
          </h3>
          <p style={{ color: '#666', margin: '0.5rem 0 0 0' }}>{title}</p>
          {subtitle && (
            <p style={{ color: '#999', fontSize: '0.9rem', margin: '0.25rem 0 0 0' }}>
              {subtitle}
            </p>
          )}
        </div>
        <div style={{ fontSize: '2rem', color, opacity: 0.7 }}>
          {icon}
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="container">
        <div className="loading">
          <div className="spinner"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container">
      {/* إحصائيات المخزون */}
      <div style={{ marginBottom: '2rem' }}>
        <h2 style={{ marginBottom: '1rem', color: '#333' }}>إدارة المخزون</h2>
        <div className="grid grid-4">
          <StatCard
            title="إجمالي الأصناف"
            value={stats.totalItems}
            color="#007bff"
            icon="📦"
          />
          <StatCard
            title="قيمة المخزون"
            value={stats.totalValue}
            color="#28a745"
            icon="💰"
          />
          <StatCard
            title="أصناف منخفضة المخزون"
            value={stats.lowStockItems}
            color="#ffc107"
            icon="⚠️"
            subtitle="أقل من الحد الأدنى"
          />
          <StatCard
            title="أصناف نفدت من المخزون"
            value={stats.outOfStockItems}
            color="#dc3545"
            icon="🚫"
            subtitle="رصيد صفر"
          />
        </div>
      </div>

      {/* شريط التنقل */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ display: 'flex', gap: '1rem' }}>
            <button
              className={`btn ${currentView === 'items' ? 'btn-primary' : 'btn-secondary'}`}
              onClick={() => setCurrentView('items')}
            >
              📦 قائمة الأصناف
            </button>
            <button
              className={`btn ${currentView === 'add-item' ? 'btn-primary' : 'btn-secondary'}`}
              onClick={() => setCurrentView('add-item')}
            >
              ➕ إضافة صنف
            </button>
            <button
              className={`btn ${currentView === 'movements' ? 'btn-primary' : 'btn-secondary'}`}
              onClick={() => setCurrentView('movements')}
            >
              📊 حركات المخزون
            </button>
            <button
              className={`btn ${currentView === 'reports' ? 'btn-primary' : 'btn-secondary'}`}
              onClick={() => setCurrentView('reports')}
            >
              📋 تقارير المخزون
            </button>
          </div>
          
          <button
            className="btn btn-success"
            onClick={loadData}
          >
            🔄 تحديث البيانات
          </button>
        </div>
      </div>

      {/* المحتوى */}
      {renderContent()}
    </div>
  );
};

export default InventoryManagement;
