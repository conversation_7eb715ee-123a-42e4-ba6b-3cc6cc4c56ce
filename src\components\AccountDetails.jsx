import React, { useState, useEffect } from 'react';
import { db, dbHelpers } from '../database/db';

const AccountDetails = ({ accountId, onClose }) => {
  const [account, setAccount] = useState(null);
  const [balance, setBalance] = useState(0);
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (accountId) {
      loadAccountDetails();
    }
  }, [accountId]);

  const loadAccountDetails = async () => {
    try {
      setLoading(true);
      
      // تحميل بيانات الحساب
      const accountData = await db.accounts.get(accountId);
      setAccount(accountData);
      
      // حساب الرصيد
      const accountBalance = await dbHelpers.getAccountBalance(accountId);
      setBalance(accountBalance);
      
      // تحميل آخر المعاملات
      const recentTransactions = await db.journalEntryDetails
        .where('accountId')
        .equals(accountId)
        .reverse()
        .limit(10)
        .toArray();
      
      // إضافة تفاصيل القيد لكل معاملة
      const transactionsWithDetails = await Promise.all(
        recentTransactions.map(async (transaction) => {
          const journalEntry = await db.journalEntries.get(transaction.entryId);
          return {
            ...transaction,
            journalEntry
          };
        })
      );
      
      setTransactions(transactionsWithDetails);
    } catch (error) {
      console.error('خطأ في تحميل تفاصيل الحساب:', error);
    } finally {
      setLoading(false);
    }
  };

  const getTypeColor = (type) => {
    const colors = {
      'أصول': '#007bff',
      'التزامات': '#dc3545',
      'حقوق ملكية': '#28a745',
      'إيرادات': '#17a2b8',
      'مصروفات': '#ffc107'
    };
    return colors[type] || '#6c757d';
  };

  if (loading) {
    return (
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: 'rgba(0,0,0,0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 1000
      }}>
        <div className="loading">
          <div className="spinner"></div>
        </div>
      </div>
    );
  }

  if (!account) {
    return null;
  }

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'rgba(0,0,0,0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{
        background: 'white',
        padding: '2rem',
        borderRadius: '10px',
        width: '90%',
        maxWidth: '800px',
        maxHeight: '90vh',
        overflow: 'auto'
      }}>
        {/* Header */}
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          marginBottom: '2rem',
          borderBottom: '2px solid #dee2e6',
          paddingBottom: '1rem'
        }}>
          <h2 style={{ margin: 0 }}>تفاصيل الحساب</h2>
          <button 
            className="btn btn-secondary"
            onClick={onClose}
          >
            إغلاق
          </button>
        </div>

        {/* معلومات الحساب */}
        <div className="grid grid-2" style={{ marginBottom: '2rem' }}>
          <div className="card">
            <div className="card-title">معلومات أساسية</div>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
              <div>
                <strong>رمز الحساب:</strong> {account.code}
              </div>
              <div>
                <strong>اسم الحساب:</strong> {account.name}
              </div>
              <div>
                <strong>نوع الحساب:</strong>
                <span style={{ 
                  background: getTypeColor(account.type), 
                  color: 'white', 
                  padding: '0.25rem 0.5rem', 
                  borderRadius: '3px',
                  marginRight: '0.5rem'
                }}>
                  {account.type}
                </span>
              </div>
              <div>
                <strong>المستوى:</strong> {account.level}
              </div>
              <div>
                <strong>الحالة:</strong> 
                <span style={{ 
                  color: account.isActive ? '#28a745' : '#dc3545',
                  fontWeight: 'bold',
                  marginRight: '0.5rem'
                }}>
                  {account.isActive ? 'نشط' : 'غير نشط'}
                </span>
              </div>
              <div>
                <strong>تاريخ الإنشاء:</strong> {new Date(account.createdAt).toLocaleDateString('ar-EG')}
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-title">الرصيد الحالي</div>
            <div style={{ textAlign: 'center', padding: '2rem' }}>
              <div style={{ 
                fontSize: '2.5rem', 
                fontWeight: 'bold', 
                color: balance >= 0 ? '#28a745' : '#dc3545',
                marginBottom: '0.5rem'
              }}>
                {balance.toLocaleString('ar-EG')} ج.م
              </div>
              <div style={{ color: '#666' }}>
                {balance >= 0 ? 'رصيد مدين' : 'رصيد دائن'}
              </div>
            </div>
          </div>
        </div>

        {/* آخر المعاملات */}
        <div className="card">
          <div className="card-title">آخر المعاملات</div>
          
          {transactions.length > 0 ? (
            <div style={{ overflow: 'auto' }}>
              <table className="table">
                <thead>
                  <tr>
                    <th>التاريخ</th>
                    <th>رقم القيد</th>
                    <th>البيان</th>
                    <th>مدين</th>
                    <th>دائن</th>
                  </tr>
                </thead>
                <tbody>
                  {transactions.map(transaction => (
                    <tr key={transaction.id}>
                      <td>
                        {transaction.journalEntry ? 
                          new Date(transaction.journalEntry.date).toLocaleDateString('ar-EG') : 
                          '-'
                        }
                      </td>
                      <td>
                        {transaction.journalEntry?.entryNumber || '-'}
                      </td>
                      <td>{transaction.description}</td>
                      <td style={{ color: '#28a745', fontWeight: 'bold' }}>
                        {transaction.debit ? transaction.debit.toLocaleString('ar-EG') : '-'}
                      </td>
                      <td style={{ color: '#dc3545', fontWeight: 'bold' }}>
                        {transaction.credit ? transaction.credit.toLocaleString('ar-EG') : '-'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div style={{ textAlign: 'center', color: '#666', padding: '2rem' }}>
              لا توجد معاملات لهذا الحساب
            </div>
          )}
        </div>

        {/* أزرار الإجراءات */}
        <div style={{ 
          display: 'flex', 
          gap: '1rem', 
          marginTop: '2rem',
          justifyContent: 'center'
        }}>
          <button className="btn btn-primary">
            عرض كشف الحساب
          </button>
          <button className="btn btn-success">
            إضافة قيد يدوي
          </button>
          <button className="btn btn-secondary" onClick={onClose}>
            إغلاق
          </button>
        </div>
      </div>
    </div>
  );
};

export default AccountDetails;
