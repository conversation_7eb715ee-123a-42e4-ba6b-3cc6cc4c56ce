import React, { useState, useEffect } from 'react';
import { db } from '../database/db';
import { useAuth } from '../contexts/AuthContext';

const AddItemForm = ({ onSuccess }) => {
  const { user } = useAuth();
  const [formData, setFormData] = useState({
    code: '',
    name: '',
    unit: '',
    category: '',
    minStock: 0,
    initialStock: 0,
    initialCost: 0,
    accountId: null
  });
  const [accounts, setAccounts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  useEffect(() => {
    loadAccounts();
    generateItemCode();
  }, []);

  const loadAccounts = async () => {
    try {
      // تحميل حسابات المخزون فقط
      const inventoryAccounts = await db.accounts
        .where('name')
        .startsWithIgnoreCase('مخزون')
        .or('name')
        .startsWithIgnoreCase('المخزون')
        .toArray();
      
      setAccounts(inventoryAccounts);
      
      // اختيار حساب المخزون الافتراضي
      const defaultAccount = inventoryAccounts.find(acc => acc.name.includes('المخزون'));
      if (defaultAccount) {
        setFormData(prev => ({ ...prev, accountId: defaultAccount.id }));
      }
    } catch (error) {
      console.error('خطأ في تحميل الحسابات:', error);
    }
  };

  const generateItemCode = async () => {
    try {
      const itemsCount = await db.items.count();
      const newCode = `ITM${String(itemsCount + 1).padStart(4, '0')}`;
      setFormData(prev => ({ ...prev, code: newCode }));
    } catch (error) {
      console.error('خطأ في توليد كود الصنف:', error);
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.code.trim()) {
      newErrors.code = 'كود الصنف مطلوب';
    }

    if (!formData.name.trim()) {
      newErrors.name = 'اسم الصنف مطلوب';
    }

    if (!formData.unit.trim()) {
      newErrors.unit = 'وحدة القياس مطلوبة';
    }

    if (formData.initialStock < 0) {
      newErrors.initialStock = 'الرصيد الابتدائي لا يمكن أن يكون سالب';
    }

    if (formData.initialCost < 0) {
      newErrors.initialCost = 'التكلفة الابتدائية لا يمكن أن تكون سالبة';
    }

    if (formData.initialStock > 0 && formData.initialCost === 0) {
      newErrors.initialCost = 'يجب إدخال التكلفة عند وجود رصيد ابتدائي';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // التحقق من عدم تكرار الكود
      const existingItem = await db.items.where('code').equals(formData.code).first();
      if (existingItem) {
        setErrors({ code: 'كود الصنف موجود مسبقاً' });
        setLoading(false);
        return;
      }

      // إضافة الصنف
      const itemId = await db.items.add({
        code: formData.code,
        name: formData.name,
        unit: formData.unit,
        category: formData.category,
        minStock: formData.minStock,
        currentStock: 0,
        avgCost: 0,
        lastCost: 0,
        isActive: true,
        createdAt: new Date()
      });

      // إضافة حركة مخزون ابتدائية إذا كان هناك رصيد
      if (formData.initialStock > 0) {
        await db.stockMovements.add({
          itemId: itemId,
          type: 'in',
          quantity: formData.initialStock,
          cost: formData.initialCost,
          reference: 'رصيد ابتدائي',
          date: new Date(),
          userId: user.id,
          notes: 'رصيد ابتدائي عند إنشاء الصنف'
        });

        // تحديث تكلفة الصنف
        await db.items.update(itemId, {
          currentStock: formData.initialStock,
          avgCost: formData.initialCost,
          lastCost: formData.initialCost
        });
      }

      alert('تم إضافة الصنف بنجاح');
      onSuccess();
    } catch (error) {
      console.error('خطأ في إضافة الصنف:', error);
      alert('حدث خطأ أثناء إضافة الصنف');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      code: '',
      name: '',
      unit: '',
      category: '',
      minStock: 0,
      initialStock: 0,
      initialCost: 0,
      accountId: accounts.find(acc => acc.name.includes('المخزون'))?.id || null
    });
    setErrors({});
    generateItemCode();
  };

  const commonUnits = [
    'قطعة', 'كيلو', 'جرام', 'لتر', 'متر', 'متر مربع', 'متر مكعب',
    'صندوق', 'كرتونة', 'علبة', 'زجاجة', 'كيس', 'طن', 'دزينة'
  ];

  const commonCategories = [
    'مواد خام', 'منتجات تامة', 'منتجات نصف مصنعة', 'قطع غيار',
    'مستلزمات مكتبية', 'أدوات', 'معدات', 'مواد استهلاكية'
  ];

  return (
    <div className="card">
      <div className="card-title">إضافة صنف جديد</div>
      
      <form onSubmit={handleSubmit}>
        <div className="grid grid-2">
          {/* المعلومات الأساسية */}
          <div>
            <h4 style={{ marginBottom: '1rem', color: '#333' }}>المعلومات الأساسية</h4>
            
            <div className="form-group">
              <label className="form-label">كود الصنف *</label>
              <input
                type="text"
                className="form-control"
                value={formData.code}
                onChange={(e) => setFormData({...formData, code: e.target.value})}
                disabled={loading}
              />
              {errors.code && (
                <div style={{ color: '#dc3545', fontSize: '0.9rem', marginTop: '0.25rem' }}>
                  {errors.code}
                </div>
              )}
            </div>

            <div className="form-group">
              <label className="form-label">اسم الصنف *</label>
              <input
                type="text"
                className="form-control"
                value={formData.name}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
                disabled={loading}
              />
              {errors.name && (
                <div style={{ color: '#dc3545', fontSize: '0.9rem', marginTop: '0.25rem' }}>
                  {errors.name}
                </div>
              )}
            </div>

            <div className="form-group">
              <label className="form-label">وحدة القياس *</label>
              <input
                type="text"
                className="form-control"
                list="units"
                value={formData.unit}
                onChange={(e) => setFormData({...formData, unit: e.target.value})}
                disabled={loading}
              />
              <datalist id="units">
                {commonUnits.map(unit => (
                  <option key={unit} value={unit} />
                ))}
              </datalist>
              {errors.unit && (
                <div style={{ color: '#dc3545', fontSize: '0.9rem', marginTop: '0.25rem' }}>
                  {errors.unit}
                </div>
              )}
            </div>

            <div className="form-group">
              <label className="form-label">الفئة</label>
              <input
                type="text"
                className="form-control"
                list="categories"
                value={formData.category}
                onChange={(e) => setFormData({...formData, category: e.target.value})}
                disabled={loading}
              />
              <datalist id="categories">
                {commonCategories.map(category => (
                  <option key={category} value={category} />
                ))}
              </datalist>
            </div>

            <div className="form-group">
              <label className="form-label">الحد الأدنى للمخزون</label>
              <input
                type="number"
                className="form-control"
                value={formData.minStock}
                onChange={(e) => setFormData({...formData, minStock: parseInt(e.target.value) || 0})}
                min="0"
                disabled={loading}
              />
            </div>
          </div>

          {/* الرصيد الابتدائي */}
          <div>
            <h4 style={{ marginBottom: '1rem', color: '#333' }}>الرصيد الابتدائي</h4>
            
            <div className="form-group">
              <label className="form-label">الكمية الابتدائية</label>
              <input
                type="number"
                className="form-control"
                value={formData.initialStock}
                onChange={(e) => setFormData({...formData, initialStock: parseFloat(e.target.value) || 0})}
                min="0"
                step="0.01"
                disabled={loading}
              />
              {errors.initialStock && (
                <div style={{ color: '#dc3545', fontSize: '0.9rem', marginTop: '0.25rem' }}>
                  {errors.initialStock}
                </div>
              )}
            </div>

            <div className="form-group">
              <label className="form-label">التكلفة الابتدائية (للوحدة)</label>
              <input
                type="number"
                className="form-control"
                value={formData.initialCost}
                onChange={(e) => setFormData({...formData, initialCost: parseFloat(e.target.value) || 0})}
                min="0"
                step="0.01"
                disabled={loading}
              />
              {errors.initialCost && (
                <div style={{ color: '#dc3545', fontSize: '0.9rem', marginTop: '0.25rem' }}>
                  {errors.initialCost}
                </div>
              )}
            </div>

            {formData.initialStock > 0 && formData.initialCost > 0 && (
              <div style={{
                background: '#e7f3ff',
                border: '1px solid #b3d9ff',
                borderRadius: '5px',
                padding: '1rem',
                marginTop: '1rem'
              }}>
                <h5 style={{ margin: '0 0 0.5rem 0', color: '#0066cc' }}>ملخص الرصيد الابتدائي</h5>
                <p style={{ margin: 0 }}>
                  <strong>الكمية:</strong> {formData.initialStock} {formData.unit}<br />
                  <strong>التكلفة للوحدة:</strong> {formData.initialCost.toLocaleString('ar-EG')} ج.م<br />
                  <strong>إجمالي القيمة:</strong> {(formData.initialStock * formData.initialCost).toLocaleString('ar-EG')} ج.م
                </p>
              </div>
            )}

            <div className="form-group">
              <label className="form-label">حساب المخزون</label>
              <select
                className="form-control"
                value={formData.accountId || ''}
                onChange={(e) => setFormData({...formData, accountId: parseInt(e.target.value) || null})}
                disabled={loading}
              >
                <option value="">اختر حساب المخزون</option>
                {accounts.map(account => (
                  <option key={account.id} value={account.id}>
                    {account.code} - {account.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* أزرار الإجراءات */}
        <div style={{ 
          display: 'flex', 
          gap: '1rem', 
          marginTop: '2rem',
          justifyContent: 'center'
        }}>
          <button 
            type="submit" 
            className="btn btn-primary"
            disabled={loading}
          >
            {loading ? 'جاري الحفظ...' : 'حفظ الصنف'}
          </button>
          <button 
            type="button" 
            className="btn btn-secondary"
            onClick={resetForm}
            disabled={loading}
          >
            مسح النموذج
          </button>
        </div>
      </form>
    </div>
  );
};

export default AddItemForm;
