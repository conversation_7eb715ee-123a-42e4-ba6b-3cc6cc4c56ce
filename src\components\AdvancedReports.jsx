import React, { useState, useEffect } from 'react';
import { db } from '../database/db';

const AdvancedReports = () => {
  const [loading, setLoading] = useState(false);
  const [reportData, setReportData] = useState({});
  const [selectedReport, setSelectedReport] = useState('financial-summary');
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');

  const reportTypes = {
    'financial-summary': 'الملخص المالي',
    'inventory-report': 'تقرير المخزون',
    'contracts-analysis': 'تحليل العقود',
    'labor-analysis': 'تحليل العمالة',
    'profit-loss': 'الأرباح والخسائر'
  };

  useEffect(() => {
    // تعيين التواريخ الافتراضية (آخر شهر)
    const today = new Date();
    const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
    setDateFrom(lastMonth.toISOString().split('T')[0]);
    setDateTo(today.toISOString().split('T')[0]);
  }, []);

  useEffect(() => {
    if (dateFrom && dateTo) {
      generateReport();
    }
  }, [selectedReport, dateFrom, dateTo]);

  const generateReport = async () => {
    try {
      setLoading(true);
      
      switch (selectedReport) {
        case 'financial-summary':
          await generateFinancialSummary();
          break;
        case 'inventory-report':
          await generateInventoryReport();
          break;
        case 'contracts-analysis':
          await generateContractsAnalysis();
          break;
        case 'labor-analysis':
          await generateLaborAnalysis();
          break;
        case 'profit-loss':
          await generateProfitLoss();
          break;
      }
    } catch (error) {
      console.error('خطأ في إنشاء التقرير:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateFinancialSummary = async () => {
    const fromDate = new Date(dateFrom);
    const toDate = new Date(dateTo);

    // فواتير البيع
    const salesInvoices = await db.salesInvoices
      .where('date')
      .between(fromDate, toDate, true, true)
      .toArray();

    // فواتير الشراء
    const purchaseInvoices = await db.purchaseInvoices
      .where('date')
      .between(fromDate, toDate, true, true)
      .toArray();

    // مصروفات العقود
    const contractExpenses = await db.contractExpenses
      .where('date')
      .between(fromDate, toDate, true, true)
      .toArray();

    const totalSales = salesInvoices.reduce((sum, inv) => sum + (inv.total || 0), 0);
    const totalPurchases = purchaseInvoices.reduce((sum, inv) => sum + (inv.total || 0), 0);
    const totalExpenses = contractExpenses.reduce((sum, exp) => sum + (exp.amount || 0), 0);

    setReportData({
      type: 'financial-summary',
      totalSales,
      totalPurchases,
      totalExpenses,
      netProfit: totalSales - totalPurchases - totalExpenses,
      salesCount: salesInvoices.length,
      purchaseCount: purchaseInvoices.length,
      expenseCount: contractExpenses.length
    });
  };

  const generateInventoryReport = async () => {
    const items = await db.items.toArray();
    
    const lowStockItems = items.filter(item => 
      item.currentStock <= (item.minStock || 0)
    );

    const totalInventoryValue = items.reduce((sum, item) => 
      sum + (item.currentStock * (item.avgCost || 0)), 0
    );

    const outOfStockItems = items.filter(item => item.currentStock <= 0);

    setReportData({
      type: 'inventory-report',
      totalItems: items.length,
      lowStockItems,
      outOfStockItems,
      totalInventoryValue,
      averageStockValue: totalInventoryValue / items.length
    });
  };

  const generateContractsAnalysis = async () => {
    const contracts = await db.contracts.toArray();
    
    const activeContracts = contracts.filter(c => c.status === 'active');
    const completedContracts = contracts.filter(c => c.status === 'completed');
    const cancelledContracts = contracts.filter(c => c.status === 'cancelled');

    const totalContractValue = contracts.reduce((sum, c) => sum + (c.totalValue || 0), 0);
    const totalPaidAmount = contracts.reduce((sum, c) => sum + (c.paidAmount || 0), 0);
    const totalRemaining = totalContractValue - totalPaidAmount;

    setReportData({
      type: 'contracts-analysis',
      totalContracts: contracts.length,
      activeContracts: activeContracts.length,
      completedContracts: completedContracts.length,
      cancelledContracts: cancelledContracts.length,
      totalContractValue,
      totalPaidAmount,
      totalRemaining,
      completionRate: (completedContracts.length / contracts.length * 100).toFixed(1)
    });
  };

  const generateLaborAnalysis = async () => {
    const fromDate = new Date(dateFrom);
    const toDate = new Date(dateTo);

    // تكاليف العمالة المباشرة
    const laborCosts = await db.laborCosts
      .where('date')
      .between(fromDate, toDate, true, true)
      .toArray();

    // عمالة أذون الصرف
    const voucherExpenses = await db.contractExpenses
      .where('type')
      .equals('voucher')
      .and(expense => {
        const expenseDate = new Date(expense.date);
        return expenseDate >= fromDate && expenseDate <= toDate;
      })
      .toArray();

    let voucherLaborCost = 0;
    voucherExpenses.forEach(expense => {
      if (expense.items) {
        expense.items.forEach(item => {
          if (item.type === 'labor') {
            voucherLaborCost += item.totalCost || 0;
          }
        });
      }
    });

    const directLaborCost = laborCosts.reduce((sum, cost) => sum + (cost.totalCost || 0), 0);
    const totalLaborCost = directLaborCost + voucherLaborCost;

    setReportData({
      type: 'labor-analysis',
      directLaborCost,
      voucherLaborCost,
      totalLaborCost,
      directLaborCount: laborCosts.length,
      voucherLaborCount: voucherExpenses.reduce((count, exp) => 
        count + (exp.items?.filter(item => item.type === 'labor').length || 0), 0
      )
    });
  };

  const generateProfitLoss = async () => {
    const fromDate = new Date(dateFrom);
    const toDate = new Date(dateTo);

    // الإيرادات
    const salesInvoices = await db.salesInvoices
      .where('date')
      .between(fromDate, toDate, true, true)
      .toArray();

    const totalRevenue = salesInvoices.reduce((sum, inv) => sum + (inv.total || 0), 0);

    // التكاليف
    const purchaseInvoices = await db.purchaseInvoices
      .where('date')
      .between(fromDate, toDate, true, true)
      .toArray();

    const contractExpenses = await db.contractExpenses
      .where('date')
      .between(fromDate, toDate, true, true)
      .toArray();

    const laborCosts = await db.laborCosts
      .where('date')
      .between(fromDate, toDate, true, true)
      .toArray();

    const totalPurchases = purchaseInvoices.reduce((sum, inv) => sum + (inv.total || 0), 0);
    const totalExpenses = contractExpenses.reduce((sum, exp) => sum + (exp.amount || 0), 0);
    const totalLaborCosts = laborCosts.reduce((sum, cost) => sum + (cost.totalCost || 0), 0);

    const totalCosts = totalPurchases + totalExpenses + totalLaborCosts;
    const grossProfit = totalRevenue - totalCosts;
    const profitMargin = totalRevenue > 0 ? (grossProfit / totalRevenue * 100).toFixed(2) : 0;

    setReportData({
      type: 'profit-loss',
      totalRevenue,
      totalPurchases,
      totalExpenses,
      totalLaborCosts,
      totalCosts,
      grossProfit,
      profitMargin
    });
  };

  const exportReport = () => {
    try {
      const reportContent = generateReportContent();
      
      const printWindow = window.open('', '_blank');
      printWindow.document.write(reportContent);
      printWindow.document.close();
      printWindow.focus();
      
      setTimeout(() => {
        printWindow.print();
      }, 250);
    } catch (error) {
      console.error('خطأ في تصدير التقرير:', error);
      alert('حدث خطأ أثناء تصدير التقرير');
    }
  };

  const generateReportContent = () => {
    const currentDate = new Date().toLocaleDateString('ar-EG');
    const reportTitle = reportTypes[selectedReport];
    
    return `
      <!DOCTYPE html>
      <html dir="rtl">
      <head>
        <meta charset="UTF-8">
        <title>${reportTitle}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .header { text-align: center; margin-bottom: 30px; }
          .header h1 { color: #333; margin-bottom: 10px; }
          .header p { color: #666; margin: 5px 0; }
          .report-content { margin-bottom: 20px; }
          .metric { background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 10px; }
          .metric h3 { margin-top: 0; color: #495057; }
          .metric .value { font-size: 1.5rem; font-weight: bold; color: #007bff; }
          @media print {
            body { margin: 0; }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>${reportTitle}</h1>
          <p>من ${dateFrom} إلى ${dateTo}</p>
          <p>تاريخ التقرير: ${currentDate}</p>
        </div>
        <div class="report-content">
          ${renderReportContent()}
        </div>
      </body>
      </html>
    `;
  };

  const renderReportContent = () => {
    if (!reportData.type) return '';

    switch (reportData.type) {
      case 'financial-summary':
        return `
          <div class="metric">
            <h3>إجمالي المبيعات</h3>
            <div class="value">${reportData.totalSales?.toLocaleString('ar-EG')} ج.م</div>
          </div>
          <div class="metric">
            <h3>إجمالي المشتريات</h3>
            <div class="value">${reportData.totalPurchases?.toLocaleString('ar-EG')} ج.م</div>
          </div>
          <div class="metric">
            <h3>إجمالي المصروفات</h3>
            <div class="value">${reportData.totalExpenses?.toLocaleString('ar-EG')} ج.م</div>
          </div>
          <div class="metric">
            <h3>صافي الربح</h3>
            <div class="value" style="color: ${reportData.netProfit >= 0 ? '#28a745' : '#dc3545'}">
              ${reportData.netProfit?.toLocaleString('ar-EG')} ج.م
            </div>
          </div>
        `;
      default:
        return '<p>لا توجد بيانات متاحة</p>';
    }
  };

  if (loading) {
    return (
      <div className="container">
        <div className="loading">
          <div className="spinner"></div>
          <div>جاري إنشاء التقرير...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="container">
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h2 style={{ margin: 0, color: '#333' }}>📊 التقارير المتقدمة</h2>
          <button className="btn btn-primary" onClick={exportReport}>
            🖨️ طباعة التقرير
          </button>
        </div>
      </div>

      <div className="card" style={{ marginBottom: '2rem' }}>
        <div className="grid grid-3">
          <div className="form-group">
            <label className="form-label">نوع التقرير</label>
            <select
              className="form-control"
              value={selectedReport}
              onChange={(e) => setSelectedReport(e.target.value)}
            >
              {Object.keys(reportTypes).map(key => (
                <option key={key} value={key}>
                  {reportTypes[key]}
                </option>
              ))}
            </select>
          </div>

          <div className="form-group">
            <label className="form-label">من تاريخ</label>
            <input
              type="date"
              className="form-control"
              value={dateFrom}
              onChange={(e) => setDateFrom(e.target.value)}
            />
          </div>

          <div className="form-group">
            <label className="form-label">إلى تاريخ</label>
            <input
              type="date"
              className="form-control"
              value={dateTo}
              onChange={(e) => setDateTo(e.target.value)}
            />
          </div>
        </div>
      </div>

      {/* Report Content */}
      {reportData.type && (
        <div className="card">
          <div className="card-title">
            {reportTypes[selectedReport]} - من {dateFrom} إلى {dateTo}
          </div>
          
          {reportData.type === 'financial-summary' && (
            <div className="grid grid-2">
              <div className="stat-card">
                <div className="stat-icon">💰</div>
                <div className="stat-content">
                  <div className="stat-number">{reportData.totalSales?.toLocaleString('ar-EG')}</div>
                  <div className="stat-label">إجمالي المبيعات (ج.م)</div>
                </div>
              </div>

              <div className="stat-card">
                <div className="stat-icon">🛒</div>
                <div className="stat-content">
                  <div className="stat-number">{reportData.totalPurchases?.toLocaleString('ar-EG')}</div>
                  <div className="stat-label">إجمالي المشتريات (ج.م)</div>
                </div>
              </div>

              <div className="stat-card">
                <div className="stat-icon">💸</div>
                <div className="stat-content">
                  <div className="stat-number">{reportData.totalExpenses?.toLocaleString('ar-EG')}</div>
                  <div className="stat-label">إجمالي المصروفات (ج.م)</div>
                </div>
              </div>

              <div className="stat-card">
                <div className="stat-icon">📈</div>
                <div className="stat-content">
                  <div className="stat-number" style={{ 
                    color: reportData.netProfit >= 0 ? '#28a745' : '#dc3545' 
                  }}>
                    {reportData.netProfit?.toLocaleString('ar-EG')}
                  </div>
                  <div className="stat-label">صافي الربح (ج.م)</div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default AdvancedReports;
