# 🤝 دليل المساهمة | Contributing Guide

نرحب بمساهماتكم في تطوير نظام المحاسبة الشامل! هذا الدليل سيساعدكم في فهم كيفية المساهمة بفعالية.

We welcome contributions to the Comprehensive Accounting System! This guide will help you understand how to contribute effectively.

## 🚀 كيفية المساهمة | How to Contribute

### 1. إعداد البيئة المحلية | Setting Up Local Environment

```bash
# Fork the repository on GitHub
# Clone your fork
git clone https://github.com/YOUR_USERNAME/accounting-system.git

# Navigate to the project directory
cd accounting-system

# Install dependencies
npm install

# Start development server
npm run dev
```

### 2. إنشاء Branch جديد | Creating a New Branch

```bash
# Create and switch to a new branch
git checkout -b feature/your-feature-name

# Or for bug fixes
git checkout -b fix/bug-description
```

### 3. تطوير الميزة | Developing Your Feature

- اتبع معايير الكود الموجودة | Follow existing code standards
- اكتب تعليقات واضحة باللغة العربية أو الإنجليزية | Write clear comments in Arabic or English
- تأكد من أن الكود يعمل بشكل صحيح | Ensure your code works correctly

### 4. اختبار التغييرات | Testing Your Changes

```bash
# Test the application
npm run dev

# Build to ensure no build errors
npm run build
```

### 5. Commit التغييرات | Committing Changes

```bash
# Add your changes
git add .

# Commit with a descriptive message
git commit -m "Add: new feature description"

# Or for Arabic
git commit -m "إضافة: وصف الميزة الجديدة"
```

### 6. Push والـ Pull Request | Push and Pull Request

```bash
# Push to your fork
git push origin feature/your-feature-name

# Create a Pull Request on GitHub
```

## 📝 معايير الكود | Code Standards

### React Components
- استخدم Functional Components مع Hooks
- اكتب أسماء المكونات بـ PascalCase
- استخدم PropTypes أو TypeScript للتحقق من الأنواع

```jsx
// Good example
const SalesInvoice = ({ invoiceData, onSave }) => {
  // Component logic
  return (
    <div className="sales-invoice">
      {/* Component JSX */}
    </div>
  );
};
```

### CSS Styling
- استخدم class names وصفية
- اتبع منهجية BEM إذا أمكن
- تأكد من الاستجابة للشاشات المختلفة

```css
/* Good example */
.sales-invoice {
  padding: 20px;
  border-radius: 8px;
}

.sales-invoice__header {
  margin-bottom: 16px;
}

.sales-invoice__item {
  padding: 8px 0;
}
```

### Database Operations
- استخدم Dexie.js للتعامل مع IndexedDB
- اكتب دوال async/await للعمليات غير المتزامنة
- تعامل مع الأخطاء بشكل مناسب

```javascript
// Good example
const saveInvoice = async (invoiceData) => {
  try {
    const id = await db.invoices.add(invoiceData);
    return { success: true, id };
  } catch (error) {
    console.error('Error saving invoice:', error);
    return { success: false, error };
  }
};
```

## 🐛 الإبلاغ عن الأخطاء | Reporting Bugs

عند الإبلاغ عن خطأ، يرجى تضمين:

When reporting a bug, please include:

- وصف واضح للمشكلة | Clear description of the issue
- خطوات إعادة إنتاج الخطأ | Steps to reproduce the bug
- السلوك المتوقع | Expected behavior
- السلوك الفعلي | Actual behavior
- لقطات شاشة إذا أمكن | Screenshots if applicable
- معلومات البيئة (المتصفح، نظام التشغيل) | Environment info (browser, OS)

## 💡 اقتراح ميزات جديدة | Suggesting New Features

لاقتراح ميزة جديدة:

To suggest a new feature:

1. تحقق من Issues الموجودة | Check existing issues
2. اكتب وصف مفصل للميزة | Write detailed feature description
3. اشرح الفائدة المتوقعة | Explain expected benefits
4. قدم أمثلة للاستخدام | Provide usage examples

## 🔍 مراجعة الكود | Code Review

جميع Pull Requests تخضع لمراجعة الكود:

All Pull Requests undergo code review:

- تأكد من اتباع معايير الكود | Ensure code standards compliance
- اختبر الوظائف الجديدة | Test new functionality
- تحقق من عدم كسر الوظائف الموجودة | Verify no breaking changes
- راجع الأداء والأمان | Review performance and security

## 📚 الموارد المفيدة | Helpful Resources

- [React Documentation](https://reactjs.org/docs)
- [Dexie.js Documentation](https://dexie.org/)
- [Vite Documentation](https://vitejs.dev/)
- [JavaScript Style Guide](https://github.com/airbnb/javascript)

## 🏷️ أنواع المساهمات | Types of Contributions

### 🐛 إصلاح الأخطاء | Bug Fixes
- إصلاح مشاكل في الوظائف الموجودة
- تحسين معالجة الأخطاء
- إصلاح مشاكل الأداء

### ✨ ميزات جديدة | New Features
- إضافة وظائف محاسبية جديدة
- تحسين واجهة المستخدم
- إضافة تقارير جديدة

### 📖 التوثيق | Documentation
- تحسين README
- إضافة تعليقات للكود
- كتابة أدلة الاستخدام

### 🎨 التصميم | Design
- تحسين واجهة المستخدم
- إضافة رموز جديدة
- تحسين تجربة المستخدم

## 🙏 شكر وتقدير | Acknowledgments

نشكر جميع المساهمين في تطوير هذا النظام:

We thank all contributors to this system:

- المطورين | Developers
- المختبرين | Testers  
- مقدمي التغذية الراجعة | Feedback providers
- مترجمي الوثائق | Documentation translators

---

**شكراً لمساهمتكم في جعل نظام المحاسبة أفضل للجميع!**

**Thank you for contributing to make the accounting system better for everyone!**
