import React, { useState, useEffect } from 'react';
import { db, dbHelpers, cleanupDuplicateAccountLinks } from '../database/db';
import { useAuth } from '../contexts/AuthContext';

const AccountLinks = () => {
  const { user, hasPermission } = useAuth();
  const [accountLinks, setAccountLinks] = useState([]);
  const [accounts, setAccounts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [validationResult, setValidationResult] = useState({ isValid: true, missingLinks: [] });

  // أسماء أنواع الحسابات بالعربية
  const linkTypeNames = {
    inventory: 'حساب المخزون',
    vat: 'حساب ضريبة القيمة المضافة',
    sales: 'حساب المبيعات',
    purchases: 'حساب المشتريات',
    cost_of_goods_sold: 'حساب تكلفة البضاعة المباعة',
    workers_salaries: 'حساب العمال والمرتبات',
    transport_expenses: 'حساب مصروفات النقل',
    sales_discounts: 'حساب خصومات المبيعات',
    purchase_discounts: 'حساب خصومات المشتريات',
    sales_returns: 'حساب مردودات المبيعات',
    purchase_returns: 'حساب مردودات المشتريات'
  };

  useEffect(() => {
    loadData();
  }, []);

  // إعادة تحميل البيانات عند تغيير التبويب
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        loadData();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadAccountLinks(),
        loadAccounts(),
        validateLinks()
      ]);
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadAccountLinks = async () => {
    try {
      const links = await db.accountLinks.toArray();
      console.log('روابط الحسابات المحملة:', links);

      // إزالة المكررات بناءً على linkType
      const uniqueLinks = [];
      const seenTypes = new Set();

      for (const link of links) {
        if (!seenTypes.has(link.linkType)) {
          uniqueLinks.push(link);
          seenTypes.add(link.linkType);
        }
      }

      // إذا كان هناك مكررات، نظف قاعدة البيانات
      if (uniqueLinks.length !== links.length) {
        console.log('تم العثور على روابط مكررة، جاري التنظيف...');
        await cleanupDuplicateLinks(uniqueLinks);
      }

      setAccountLinks(uniqueLinks);
    } catch (error) {
      console.error('خطأ في تحميل روابط الحسابات:', error);
      setAccountLinks([]);
    }
  };

  const cleanupDuplicateLinks = async (uniqueLinks) => {
    try {
      // حذف جميع الروابط
      await db.accountLinks.clear();

      // إعادة إضافة الروابط الفريدة
      for (const link of uniqueLinks) {
        await db.accountLinks.add({
          ...link,
          updatedAt: new Date()
        });
      }

      console.log('تم تنظيف الروابط المكررة');
    } catch (error) {
      console.error('خطأ في تنظيف الروابط المكررة:', error);
    }
  };

  const loadAccounts = async () => {
    try {
      const allAccounts = await db.accounts.toArray();
      const activeAccounts = allAccounts.filter(account => account.isActive !== false);
      console.log('الحسابات المحملة:', activeAccounts);
      setAccounts(activeAccounts);
    } catch (error) {
      console.error('خطأ في تحميل الحسابات:', error);
      setAccounts([]);
    }
  };

  const validateLinks = async () => {
    try {
      const result = await dbHelpers.validateRequiredAccountLinks();
      setValidationResult(result);
    } catch (error) {
      console.error('خطأ في التحقق من الروابط:', error);
    }
  };

  const handleAccountChange = async (linkType, accountCode) => {
    try {
      const account = accounts.find(acc => acc.code === accountCode);
      
      if (account) {
        // تحديث الحالة المحلية
        setAccountLinks(prev => prev.map(link => 
          link.linkType === linkType 
            ? { ...link, accountId: account.id, accountCode: account.code, accountName: account.name }
            : link
        ));

        // حفظ في قاعدة البيانات
        const success = await dbHelpers.updateAccountLink(
          linkType, 
          account.id, 
          account.code, 
          account.name
        );

        if (success) {
          console.log(`تم ربط ${linkTypeNames[linkType]} بالحساب ${account.name}`);

          // التحقق من أن البيانات تم حفظها فعلاً
          const savedLinks = await db.accountLinks.toArray();
          const savedLink = savedLinks.find(l => l.linkType === linkType);

          if (savedLink && savedLink.accountCode === account.code) {
            console.log('تم التأكد من حفظ الربط في قاعدة البيانات');
          } else {
            console.warn('لم يتم حفظ الربط بشكل صحيح');
          }

          await validateLinks(); // إعادة التحقق
        } else {
          alert('حدث خطأ أثناء حفظ الربط');
        }
      } else {
        // إزالة الربط
        setAccountLinks(prev => prev.map(link => 
          link.linkType === linkType 
            ? { ...link, accountId: null, accountCode: '', accountName: '' }
            : link
        ));

        await dbHelpers.updateAccountLink(linkType, null, '', '');
        await validateLinks();
      }
    } catch (error) {
      console.error('خطأ في تحديث ربط الحساب:', error);
      alert('حدث خطأ أثناء تحديث الربط');
    }
  };

  const saveAllLinks = async () => {
    try {
      setSaving(true);
      
      // التحقق من الحسابات المطلوبة
      const validation = await dbHelpers.validateRequiredAccountLinks();
      
      if (!validation.isValid) {
        const missingNames = validation.missingLinks.map(link => linkTypeNames[link.linkType]).join('، ');
        alert(`يجب ربط الحسابات التالية قبل الحفظ:\n${missingNames}`);
        return;
      }

      alert('تم حفظ جميع روابط الحسابات بنجاح!');
      
    } catch (error) {
      console.error('خطأ في حفظ الروابط:', error);
      alert('حدث خطأ أثناء حفظ الروابط');
    } finally {
      setSaving(false);
    }
  };

  const forceCreateDefaults = async () => {
    if (!confirm('هل تريد إنشاء الروابط الافتراضية؟ سيتم إضافة الروابط المفقودة فقط.')) {
      return;
    }

    try {
      setSaving(true);

      // إنشاء الروابط الافتراضية بدون حذف الموجودة
      await createDefaultLinksIfMissing();

      // إعادة تحميل البيانات
      await loadData();

      alert('تم إنشاء الروابط الافتراضية المفقودة بنجاح!');

    } catch (error) {
      console.error('خطأ في إنشاء الروابط الافتراضية:', error);
      alert('حدث خطأ أثناء إنشاء الروابط الافتراضية');
    } finally {
      setSaving(false);
    }
  };

  const createDefaultLinksIfMissing = async () => {
    const defaultLinks = [
      {
        linkType: 'inventory',
        accountId: null,
        accountCode: '',
        accountName: '',
        description: 'حساب المخزون - يستخدم لربط جميع أصناف المخزون',
        isRequired: true
      },
      {
        linkType: 'vat',
        accountId: null,
        accountCode: '',
        accountName: '',
        description: 'حساب ضريبة القيمة المضافة - يستخدم في الفواتير',
        isRequired: true
      },
      {
        linkType: 'sales',
        accountId: null,
        accountCode: '',
        accountName: '',
        description: 'حساب المبيعات - يستخدم في فواتير المبيعات',
        isRequired: true
      },
      {
        linkType: 'purchases',
        accountId: null,
        accountCode: '',
        accountName: '',
        description: 'حساب المشتريات - يستخدم في فواتير المشتريات',
        isRequired: true
      },
      {
        linkType: 'cost_of_goods_sold',
        accountId: null,
        accountCode: '',
        accountName: '',
        description: 'حساب تكلفة البضاعة المباعة - يستخدم عند البيع',
        isRequired: true
      },
      {
        linkType: 'workers_salaries',
        accountId: null,
        accountCode: '',
        accountName: '',
        description: 'حساب العمال والمرتبات - يستخدم في كشوف المرتبات',
        isRequired: true
      },
      {
        linkType: 'transport_expenses',
        accountId: null,
        accountCode: '',
        accountName: '',
        description: 'حساب مصروفات النقل - يستخدم في مصروفات النقل',
        isRequired: false
      },
      {
        linkType: 'sales_discounts',
        accountId: null,
        accountCode: '',
        accountName: '',
        description: 'حساب خصومات المبيعات - يستخدم في خصومات المبيعات',
        isRequired: false
      },
      {
        linkType: 'purchase_discounts',
        accountId: null,
        accountCode: '',
        accountName: '',
        description: 'حساب خصومات المشتريات - يستخدم في خصومات المشتريات',
        isRequired: false
      },
      {
        linkType: 'sales_returns',
        accountId: null,
        accountCode: '',
        accountName: '',
        description: 'حساب مردودات المبيعات - يستخدم في مردودات المبيعات',
        isRequired: false
      },
      {
        linkType: 'purchase_returns',
        accountId: null,
        accountCode: '',
        accountName: '',
        description: 'حساب مردودات المشتريات - يستخدم في مردودات المشتريات',
        isRequired: false
      }
    ];

    // التحقق من الروابط الموجودة
    const existingLinks = await db.accountLinks.toArray();
    const existingTypes = new Set(existingLinks.map(link => link.linkType));

    // إضافة الروابط المفقودة فقط
    for (const link of defaultLinks) {
      if (!existingTypes.has(link.linkType)) {
        await db.accountLinks.add({
          ...link,
          createdAt: new Date(),
          updatedAt: new Date()
        });
        console.log(`تم إنشاء ربط افتراضي: ${link.linkType}`);
      }
    }
  };

  const resetToDefaults = async () => {
    if (!confirm('هل أنت متأكد من إعادة تعيين جميع الروابط؟ سيتم فقدان الإعدادات الحالية.')) {
      return;
    }

    try {
      setSaving(true);

      // حذف جميع الروابط الحالية
      await db.accountLinks.clear();

      // إعادة إنشاء الروابط الافتراضية
      const defaultLinks = [
        {
          linkType: 'inventory',
          accountId: null,
          accountCode: '',
          accountName: '',
          description: 'حساب المخزون - يستخدم لربط جميع أصناف المخزون',
          isRequired: true
        },
        {
          linkType: 'vat',
          accountId: null,
          accountCode: '',
          accountName: '',
          description: 'حساب ضريبة القيمة المضافة - يستخدم في الفواتير',
          isRequired: true
        },
        {
          linkType: 'sales',
          accountId: null,
          accountCode: '',
          accountName: '',
          description: 'حساب المبيعات - يستخدم في فواتير المبيعات',
          isRequired: true
        },
        {
          linkType: 'purchases',
          accountId: null,
          accountCode: '',
          accountName: '',
          description: 'حساب المشتريات - يستخدم في فواتير المشتريات',
          isRequired: true
        },
        {
          linkType: 'cost_of_goods_sold',
          accountId: null,
          accountCode: '',
          accountName: '',
          description: 'حساب تكلفة البضاعة المباعة - يستخدم عند البيع',
          isRequired: true
        },
        {
          linkType: 'workers_salaries',
          accountId: null,
          accountCode: '',
          accountName: '',
          description: 'حساب العمال والمرتبات - يستخدم في كشوف المرتبات',
          isRequired: true
        },
        {
          linkType: 'transport_expenses',
          accountId: null,
          accountCode: '',
          accountName: '',
          description: 'حساب مصروفات النقل - يستخدم في مصروفات النقل',
          isRequired: false
        },
        {
          linkType: 'sales_discounts',
          accountId: null,
          accountCode: '',
          accountName: '',
          description: 'حساب خصومات المبيعات - يستخدم في خصومات المبيعات',
          isRequired: false
        },
        {
          linkType: 'purchase_discounts',
          accountId: null,
          accountCode: '',
          accountName: '',
          description: 'حساب خصومات المشتريات - يستخدم في خصومات المشتريات',
          isRequired: false
        },
        {
          linkType: 'sales_returns',
          accountId: null,
          accountCode: '',
          accountName: '',
          description: 'حساب مردودات المبيعات - يستخدم في مردودات المبيعات',
          isRequired: false
        },
        {
          linkType: 'purchase_returns',
          accountId: null,
          accountCode: '',
          accountName: '',
          description: 'حساب مردودات المشتريات - يستخدم في مردودات المشتريات',
          isRequired: false
        }
      ];

      for (const link of defaultLinks) {
        await db.accountLinks.add({
          ...link,
          createdAt: new Date(),
          updatedAt: new Date()
        });
      }
      
      // إعادة تحميل البيانات
      await loadData();
      
      alert('تم إعادة تعيين الروابط إلى الإعدادات الافتراضية');
      
    } catch (error) {
      console.error('خطأ في إعادة التعيين:', error);
      alert('حدث خطأ أثناء إعادة التعيين');
    } finally {
      setSaving(false);
    }
  };

  const cleanupDuplicates = async () => {
    if (!confirm('هل تريد تنظيف الروابط المكررة؟ هذا سيحذف الروابط المكررة ويحتفظ بالأولى فقط.')) {
      return;
    }

    try {
      setSaving(true);
      const success = await cleanupDuplicateAccountLinks();

      if (success) {
        alert('تم تنظيف الروابط المكررة بنجاح');
        await loadData();
      } else {
        alert('حدث خطأ أثناء تنظيف الروابط المكررة');
      }
    } catch (error) {
      console.error('خطأ في تنظيف الروابط المكررة:', error);
      alert('حدث خطأ أثناء تنظيف الروابط المكررة');
    } finally {
      setSaving(false);
    }
  };

  if (!hasPermission('settings')) {
    return (
      <div className="card">
        <div className="card-title">⚠️ غير مصرح</div>
        <p>ليس لديك صلاحية للوصول إلى إعدادات ربط الحسابات.</p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="card">
        <div className="loading">
          <div className="spinner"></div>
          <p>جاري تحميل روابط الحسابات...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container">
      <div className="card">
        <div className="card-title">🔗 ربط الحسابات مع الجداول</div>
        
        {/* رسائل التحذير */}
        {!validationResult.isValid && (
          <div style={{
            background: '#fff3cd',
            border: '1px solid #ffeaa7',
            borderRadius: '5px',
            padding: '1rem',
            marginBottom: '1rem',
            color: '#856404'
          }}>
            <strong>⚠️ تحذير:</strong> يجب ربط الحسابات التالية قبل استخدام النظام:
            <ul style={{ marginTop: '0.5rem', marginBottom: 0 }}>
              {validationResult.missingLinks.map(link => (
                <li key={link.linkType}>{linkTypeNames[link.linkType]}</li>
              ))}
            </ul>
          </div>
        )}

        <div style={{ marginBottom: '2rem' }}>
          <p style={{ color: '#666', lineHeight: '1.6' }}>
            يتيح لك هذا القسم ربط الحسابات المحاسبية مع الوحدات المختلفة في النظام.
            <br />
            <strong>مطلوب:</strong> الحسابات المميزة بـ (*) مطلوبة لعمل النظام بشكل صحيح.
            <br />
            <strong>اختياري:</strong> الحسابات الأخرى يمكن ربطها حسب الحاجة.
          </p>
        </div>

        {/* جدول روابط الحسابات */}
        {accountLinks.length === 0 ? (
          <div style={{
            background: '#f8f9fa',
            border: '2px dashed #dee2e6',
            borderRadius: '8px',
            padding: '3rem',
            textAlign: 'center',
            color: '#6c757d'
          }}>
            <h4>📋 لا توجد روابط حسابات</h4>
            <p>لم يتم العثور على أي روابط حسابات. انقر على "إنشاء الروابط الافتراضية" لإنشاء الروابط الأساسية.</p>
            <button
              className="btn btn-primary"
              onClick={forceCreateDefaults}
              disabled={saving}
            >
              ➕ إنشاء الروابط الافتراضية
            </button>
          </div>
        ) : (
          <div style={{ overflowX: 'auto' }}>
            <table className="table">
              <thead>
                <tr>
                  <th>نوع الحساب</th>
                  <th>الوصف</th>
                  <th>الحساب المربوط</th>
                  <th>رقم الحساب</th>
                  <th>الحالة</th>
                </tr>
              </thead>
              <tbody>
                {accountLinks.map(link => (
                <tr key={link.linkType}>
                  <td>
                    <strong>
                      {linkTypeNames[link.linkType]}
                      {link.isRequired && <span style={{ color: '#dc3545' }}> *</span>}
                    </strong>
                  </td>
                  <td style={{ fontSize: '0.9rem', color: '#666' }}>
                    {link.description}
                  </td>
                  <td>
                    <select
                      className="form-control"
                      value={link.accountCode || ''}
                      onChange={(e) => handleAccountChange(link.linkType, e.target.value)}
                      style={{ minWidth: '200px' }}
                    >
                      <option value="">-- اختر الحساب --</option>
                      {accounts.map(account => (
                        <option key={account.id} value={account.code}>
                          {account.code} - {account.name}
                        </option>
                      ))}
                    </select>
                  </td>
                  <td>
                    <span style={{ 
                      fontFamily: 'monospace', 
                      background: '#f8f9fa', 
                      padding: '0.25rem 0.5rem',
                      borderRadius: '3px'
                    }}>
                      {link.accountCode || '--'}
                    </span>
                  </td>
                  <td>
                    {link.accountId ? (
                      <span style={{ 
                        color: '#28a745', 
                        background: '#d4edda', 
                        padding: '0.25rem 0.5rem',
                        borderRadius: '3px',
                        fontSize: '0.8rem'
                      }}>
                        ✓ مربوط
                      </span>
                    ) : (
                      <span style={{ 
                        color: link.isRequired ? '#dc3545' : '#6c757d', 
                        background: link.isRequired ? '#f8d7da' : '#e9ecef', 
                        padding: '0.25rem 0.5rem',
                        borderRadius: '3px',
                        fontSize: '0.8rem'
                      }}>
                        {link.isRequired ? '✗ مطلوب' : '○ اختياري'}
                      </span>
                    )}
                  </td>
                </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* أزرار الإجراءات */}
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          marginTop: '2rem',
          borderTop: '1px solid #dee2e6',
          paddingTop: '1rem'
        }}>
          <div>
            <button
              className="btn btn-success"
              onClick={saveAllLinks}
              disabled={saving}
              style={{ marginLeft: '1rem' }}
            >
              {saving ? '⏳ جاري الحفظ...' : '💾 حفظ جميع الروابط'}
            </button>
            
            <button
              className="btn btn-secondary"
              onClick={loadData}
              disabled={saving}
              style={{ marginLeft: '1rem' }}
            >
              🔄 تحديث البيانات
            </button>

            <button
              className="btn btn-warning"
              onClick={cleanupDuplicates}
              disabled={saving}
              style={{ marginLeft: '1rem' }}
            >
              🧹 تنظيف المكررات
            </button>

            <button
              className="btn btn-info"
              onClick={forceCreateDefaults}
              disabled={saving}
            >
              ➕ إنشاء الروابط الافتراضية
            </button>
          </div>

          <button
            className="btn btn-danger"
            onClick={resetToDefaults}
            disabled={saving}
          >
            🔄 إعادة تعيين افتراضي
          </button>
        </div>

        {/* معلومات إضافية */}
        <div style={{
          background: '#e7f3ff',
          border: '1px solid #b3d9ff',
          borderRadius: '5px',
          padding: '1rem',
          marginTop: '1rem',
          fontSize: '0.9rem'
        }}>
          <strong>💡 ملاحظات مهمة:</strong>
          <ul style={{ marginTop: '0.5rem', marginBottom: 0 }}>
            <li>حساب المخزون سيتم ربطه تلقائياً مع جميع أصناف المخزون</li>
            <li>حساب ضريبة القيمة المضافة سيستخدم في جميع الفواتير التي تحتوي على ضريبة</li>
            <li>يمكن تغيير هذه الروابط في أي وقت من خلال هذه الشاشة</li>
            <li>التغييرات تؤثر على العمليات الجديدة فقط وليس على البيانات الموجودة</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default AccountLinks;
