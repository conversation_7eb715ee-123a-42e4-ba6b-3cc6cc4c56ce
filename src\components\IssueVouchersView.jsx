import React, { useState, useEffect } from 'react';
import { db } from '../database/db';
import { useAuth } from '../contexts/AuthContext';
import IssueVoucher from './IssueVoucher';

const IssueVouchersView = () => {
  const { user } = useAuth();
  const [vouchers, setVouchers] = useState([]);
  const [contracts, setContracts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedVoucher, setSelectedVoucher] = useState(null);
  const [showVoucherModal, setShowVoucherModal] = useState(false);
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterContract, setFilterContract] = useState('all');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadVouchers(),
        loadContracts()
      ]);
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadVouchers = async () => {
    try {
      const allVouchers = await db.contractExpenses
        .where('type')
        .equals('voucher')
        .toArray();
      setVouchers(allVouchers);
    } catch (error) {
      console.error('خطأ في تحميل أذون الصرف:', error);
    }
  };

  const loadContracts = async () => {
    try {
      const allContracts = await db.contracts.toArray();
      setContracts(allContracts);
    } catch (error) {
      console.error('خطأ في تحميل العقود:', error);
    }
  };

  const getContractName = (contractId) => {
    const contract = contracts.find(c => c.id === contractId);
    return contract ? `${contract.contractNumber} - ${contract.name}` : 'غير محدد';
  };

  const handleEditVoucher = (voucher) => {
    // التحقق من صلاحية تعديل الأذون المرحلة
    if (voucher.status === 'posted') {
      if (!user.permissions?.editAllData && user.role !== 'super-admin') {
        alert('لا يمكن تعديل إذن صرف مرحل. تحتاج إلى صلاحيات خاصة.');
        return;
      }

      if (!confirm('هذا الإذن مرحل. هل تريد تعديله؟ (يتطلب صلاحيات خاصة)')) {
        return;
      }
    }

    const contract = contracts.find(c => c.id === voucher.contractId);
    setSelectedVoucher({ ...voucher, contract });
    setShowVoucherModal(true);
  };

  const handleDeleteVoucher = async (voucherId) => {
    if (window.confirm('هل أنت متأكد من حذف إذن الصرف؟ لا يمكن التراجع عن هذا الإجراء.')) {
      try {
        await db.contractExpenses.delete(voucherId);
        alert('تم حذف إذن الصرف بنجاح');
        loadVouchers();
      } catch (error) {
        console.error('خطأ في حذف إذن الصرف:', error);
        alert('حدث خطأ أثناء حذف إذن الصرف');
      }
    }
  };

  const handlePostVoucher = async (voucherId) => {
    if (window.confirm('هل أنت متأكد من ترحيل إذن الصرف؟ لن يمكن تعديله بعد الترحيل.')) {
      try {
        await db.contractExpenses.update(voucherId, {
          status: 'posted',
          postedAt: new Date(),
          postedBy: user.id
        });
        alert('تم ترحيل إذن الصرف بنجاح - لن يمكن تعديله الآن');
        loadVouchers();
      } catch (error) {
        console.error('خطأ في ترحيل إذن الصرف:', error);
        alert('حدث خطأ أثناء ترحيل إذن الصرف');
      }
    }
  };

  // إلغاء ترحيل إذن الصرف
  const handleUnpostVoucher = async (voucherId) => {
    // التحقق من صلاحية إلغاء الترحيل
    if (!user.permissions?.editPostedEntries && user.role !== 'super-admin') {
      alert('لا يمكن إلغاء ترحيل أذون الصرف. تحتاج إلى صلاحيات خاصة.');
      return;
    }

    if (window.confirm('هل تريد إلغاء ترحيل إذن الصرف؟ سيعود إلى حالة قابلة للتعديل.')) {
      try {
        await db.contractExpenses.update(voucherId, {
          status: 'pending',
          unpostedAt: new Date(),
          unpostedBy: user.id
        });
        alert('تم إلغاء ترحيل إذن الصرف بنجاح');
        loadVouchers();
      } catch (error) {
        console.error('خطأ في إلغاء ترحيل إذن الصرف:', error);
        alert('حدث خطأ أثناء إلغاء ترحيل إذن الصرف');
      }
    }
  };

  const handleCloseModal = () => {
    setShowVoucherModal(false);
    setSelectedVoucher(null);
  };

  const handleVoucherSaved = () => {
    loadVouchers();
    handleCloseModal();
  };

  const filteredVouchers = vouchers.filter(voucher => {
    const statusMatch = filterStatus === 'all' || voucher.status === filterStatus;
    const contractMatch = filterContract === 'all' || voucher.contractId === parseInt(filterContract);
    return statusMatch && contractMatch;
  });

  const getStatusBadge = (status) => {
    const statusConfig = {
      pending: { bg: '#f8d7da', color: '#721c24', text: 'في الانتظار' },
      issued: { bg: '#fff3cd', color: '#856404', text: 'مُصدر' },
      posted: { bg: '#d4edda', color: '#155724', text: 'مُرحل' },
      cancelled: { bg: '#f8f9fa', color: '#6c757d', text: 'ملغي' }
    };
    
    const config = statusConfig[status] || statusConfig.pending;
    
    return (
      <span style={{
        padding: '0.25rem 0.5rem',
        borderRadius: '4px',
        fontSize: '0.75rem',
        background: config.bg,
        color: config.color
      }}>
        {config.text}
      </span>
    );
  };

  if (loading) {
    return (
      <div style={{ padding: '2rem', textAlign: 'center' }}>
        <div className="spinner"></div>
        <div>جاري التحميل...</div>
      </div>
    );
  }

  return (
    <div>
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        marginBottom: '2rem' 
      }}>
        <h2 style={{ margin: 0, color: '#007bff' }}>📋 إدارة أذون الصرف</h2>
        <button 
          className="btn btn-primary"
          onClick={() => window.location.reload()}
        >
          🔄 تحديث
        </button>
      </div>

      {/* فلاتر */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div className="card-title">فلترة أذون الصرف</div>
        
        <div className="grid grid-2">
          <div className="form-group">
            <label className="form-label">حالة الإذن</label>
            <select
              className="form-control"
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
            >
              <option value="all">جميع الحالات</option>
              <option value="pending">في الانتظار</option>
              <option value="issued">مُصدر</option>
              <option value="posted">مُرحل</option>
              <option value="cancelled">ملغي</option>
            </select>
          </div>

          <div className="form-group">
            <label className="form-label">العقد</label>
            <select
              className="form-control"
              value={filterContract}
              onChange={(e) => setFilterContract(e.target.value)}
            >
              <option value="all">جميع العقود</option>
              {contracts.map(contract => (
                <option key={contract.id} value={contract.id}>
                  {contract.contractNumber} - {contract.name}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* جدول أذون الصرف */}
      <div className="card">
        <div className="card-title">
          قائمة أذون الصرف ({filteredVouchers.length})
        </div>
        
        {filteredVouchers.length > 0 ? (
          <div className="table-responsive">
            <table className="table">
              <thead>
                <tr>
                  <th>رقم الإذن</th>
                  <th>التاريخ</th>
                  <th>العقد</th>
                  <th>طالب الصرف</th>
                  <th>القسم</th>
                  <th>المبلغ</th>
                  <th>الحالة</th>
                  <th>إجراءات</th>
                </tr>
              </thead>
              <tbody>
                {filteredVouchers.map(voucher => (
                  <tr key={voucher.id}>
                    <td style={{ fontWeight: 'bold', color: '#007bff' }}>
                      {voucher.voucherNumber}
                    </td>
                    <td>{new Date(voucher.date).toLocaleDateString('ar-EG')}</td>
                    <td>{getContractName(voucher.contractId)}</td>
                    <td>{voucher.requestedBy || '-'}</td>
                    <td>{voucher.department || '-'}</td>
                    <td style={{ fontWeight: 'bold', color: '#dc3545' }}>
                      {voucher.amount.toLocaleString('ar-EG')} ج.م
                    </td>
                    <td>{getStatusBadge(voucher.status)}</td>
                    <td>
                      <div style={{ display: 'flex', gap: '0.5rem' }}>
                        <button
                          className="btn btn-warning btn-sm"
                          onClick={() => handleEditVoucher(voucher)}
                          title="تعديل"
                        >
                          ✏️
                        </button>
                        
                        {voucher.status !== 'posted' && (
                          <button
                            className="btn btn-success btn-sm"
                            onClick={() => handlePostVoucher(voucher.id)}
                            title="ترحيل"
                          >
                            📤
                          </button>
                        )}

                        {voucher.status === 'posted' && (
                          <>
                            <span style={{
                              padding: '0.2rem 0.4rem',
                              fontSize: '0.75rem',
                              color: '#28a745',
                              background: '#d4edda',
                              borderRadius: '3px',
                              marginLeft: '0.25rem'
                            }}>
                              مرحل
                            </span>

                            {/* أزرار خاصة للمدير الأعلى */}
                            {(user.role === 'super-admin' || user.permissions?.editPostedEntries) && (
                              <button
                                className="btn btn-info btn-sm"
                                onClick={() => handleUnpostVoucher(voucher.id)}
                                title="إلغاء ترحيل إذن الصرف (صلاحيات خاصة)"
                                style={{ fontSize: '0.7rem', padding: '0.2rem 0.4rem' }}
                              >
                                ↩️ إلغاء ترحيل
                              </button>
                            )}
                          </>
                        )}
                        
                        <button
                          className="btn btn-danger btn-sm"
                          onClick={() => handleDeleteVoucher(voucher.id)}
                          title="حذف"
                        >
                          🗑️
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
              <tfoot>
                <tr style={{ background: '#f8f9fa', fontWeight: 'bold' }}>
                  <td colSpan="5">إجمالي أذون الصرف:</td>
                  <td style={{ color: '#dc3545', fontSize: '1.1rem' }}>
                    {filteredVouchers.reduce((sum, voucher) => sum + voucher.amount, 0).toLocaleString('ar-EG')} ج.م
                  </td>
                  <td colSpan="2"></td>
                </tr>
              </tfoot>
            </table>
          </div>
        ) : (
          <div style={{ textAlign: 'center', color: '#666', padding: '2rem' }}>
            لا توجد أذون صرف مطابقة للفلاتر المحددة
          </div>
        )}
      </div>

      {/* نافذة تعديل الإذن */}
      {showVoucherModal && selectedVoucher && (
        <IssueVoucher
          contractId={selectedVoucher.contractId}
          contract={selectedVoucher.contract}
          voucherId={selectedVoucher.id}
          onClose={handleCloseModal}
          onSave={handleVoucherSaved}
        />
      )}
    </div>
  );
};

export default IssueVouchersView;
