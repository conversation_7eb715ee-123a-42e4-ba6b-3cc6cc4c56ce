import React, { useState } from 'react';
import UserManagement from './UserManagement';
import PeriodManagement from './PeriodManagement';
import BackupManagement from './BackupManagement';
import DataExport from './DataExport';
import DatabaseCheck from './DatabaseCheck';
import CompanySettings from './CompanySettings';
import DatabaseViewer from './DatabaseViewer';

const SystemSettings = ({ user }) => {
  const [currentView, setCurrentView] = useState('backup');

  const renderCurrentView = () => {
    switch (currentView) {
      case 'users':
        return <UserManagement currentUser={user} />;
      case 'periods':
        return <PeriodManagement user={user} />;
      case 'backup':
        return <BackupManagement user={user} />;
      case 'export':
        return <DataExport />;
      case 'database-check':
        return <DatabaseCheck />;
      case 'company':
        return <CompanySettings />;
      case 'database-viewer':
        return <DatabaseViewer />;
      default:
        return <BackupManagement user={user} />;
    }
  };

  return (
    <div>
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        marginBottom: '2rem' 
      }}>
        <h2 style={{ margin: 0, color: '#2c3e50' }}>⚙️ إعدادات النظام</h2>
      </div>

      {/* Navigation */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(180px, 1fr))',
          gap: '1rem'
        }}>
          <button
            className={`btn ${currentView === 'backup' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setCurrentView('backup')}
            style={{ padding: '1rem', height: 'auto' }}
          >
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '1.5rem', marginBottom: '0.5rem' }}>💾</div>
              <div>النسخ الاحتياطية</div>
            </div>
          </button>

          <button
            className={`btn ${currentView === 'users' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setCurrentView('users')}
            style={{ padding: '1rem', height: 'auto' }}
          >
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '1.5rem', marginBottom: '0.5rem' }}>👥</div>
              <div>إدارة المستخدمين</div>
            </div>
          </button>

          <button
            className={`btn ${currentView === 'periods' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setCurrentView('periods')}
            style={{ padding: '1rem', height: 'auto' }}
          >
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '1.5rem', marginBottom: '0.5rem' }}>📅</div>
              <div>الفترات المحاسبية</div>
            </div>
          </button>

          <button
            className={`btn ${currentView === 'export' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setCurrentView('export')}
            style={{ padding: '1rem', height: 'auto' }}
          >
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '1.5rem', marginBottom: '0.5rem' }}>📊</div>
              <div>تصدير البيانات</div>
            </div>
          </button>

          <button
            className={`btn ${currentView === 'database-check' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setCurrentView('database-check')}
            style={{ padding: '1rem', height: 'auto' }}
          >
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '1.5rem', marginBottom: '0.5rem' }}>🔍</div>
              <div>فحص قاعدة البيانات</div>
            </div>
          </button>

          <button
            className={`btn ${currentView === 'company' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setCurrentView('company')}
            style={{ padding: '1rem', height: 'auto' }}
          >
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '1.5rem', marginBottom: '0.5rem' }}>🏢</div>
              <div>بيانات الشركة</div>
            </div>
          </button>

          <button
            className={`btn ${currentView === 'database-viewer' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setCurrentView('database-viewer')}
            style={{ padding: '1rem', height: 'auto' }}
          >
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '1.5rem', marginBottom: '0.5rem' }}>📋</div>
              <div>عرض الجداول</div>
            </div>
          </button>
        </div>
      </div>

      {/* Current View */}
      {renderCurrentView()}
    </div>
  );
};

export default SystemSettings;
