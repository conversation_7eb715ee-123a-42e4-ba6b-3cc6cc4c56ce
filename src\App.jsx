import React, { useEffect } from 'react';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { initializeDatabase } from './database/db';
import Login from './components/Login';
import SimpleDashboard from './components/SimpleDashboard';

// معالج الأخطاء العام لتجاهل أخطاء إضافات المتصفح
window.addEventListener('error', (event) => {
  if (event.message && (
    event.message.includes('message channel closed') ||
    event.message.includes('listener indicated an asynchronous response') ||
    event.message.includes('Extension context invalidated')
  )) {
    event.preventDefault();
    return false;
  }
});

// معالج الأخطاء غير المعالجة
window.addEventListener('unhandledrejection', (event) => {
  if (event.reason && event.reason.message && (
    event.reason.message.includes('message channel closed') ||
    event.reason.message.includes('listener indicated an asynchronous response') ||
    event.reason.message.includes('Extension context invalidated')
  )) {
    event.preventDefault();
    return false;
  }
});

const AppContent = () => {
  const { user, loading } = useAuth();

  useEffect(() => {
    // تهيئة قاعدة البيانات عند بدء التطبيق
    initializeDatabase();
  }, []);

  if (loading) {
    return (
      <div className="loading">
        <div className="spinner"></div>
      </div>
    );
  }

  return user ? <SimpleDashboard /> : <Login />;
};

const App = () => {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
};

export default App;
