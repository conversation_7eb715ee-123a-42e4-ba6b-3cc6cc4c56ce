import React, { useEffect } from 'react';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { initializeDatabase } from './database/db';
import Login from './components/Login';
import SimpleDashboard from './components/SimpleDashboard';

const AppContent = () => {
  const { user, loading } = useAuth();

  useEffect(() => {
    // تهيئة قاعدة البيانات عند بدء التطبيق
    initializeDatabase();
  }, []);

  if (loading) {
    return (
      <div className="loading">
        <div className="spinner"></div>
      </div>
    );
  }

  return user ? <SimpleDashboard /> : <Login />;
};

const App = () => {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
};

export default App;
