import React, { useState, useEffect } from 'react';
import { db, dbHelpers, createPurchaseInvoiceJournalEntry } from '../database/db';
import { useAuth } from '../contexts/AuthContext';
import AccountingEntries from './AccountingEntries';

const PurchaseInvoice = () => {
  const { user } = useAuth();
  const [suppliers, setSuppliers] = useState([]);
  const [items, setItems] = useState([]);
  const [accounts, setAccounts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showEntries, setShowEntries] = useState(false);
  const [currentEntries, setCurrentEntries] = useState([]);
  const [showJournalEntry, setShowJournalEntry] = useState(false);
  const [journalEntryPreview, setJournalEntryPreview] = useState(null);

  const [invoiceData, setInvoiceData] = useState({
    invoiceNumber: '',
    supplierId: '',
    date: new Date().toISOString().split('T')[0],
    dueDate: '',
    notes: '',
    hasTax: true,
    taxRate: 14,
    discount: 0,
    discountType: 'amount' // amount or percentage
  });

  const [invoiceItems, setInvoiceItems] = useState([
    {
      itemId: '',
      quantity: 0,
      cost: 0,
      total: 0
    }
  ]);

  const [totals, setTotals] = useState({
    subtotal: 0,
    discount: 0,
    taxAmount: 0,
    total: 0
  });

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    calculateTotals();
  }, [invoiceItems, invoiceData.discount, invoiceData.discountType, invoiceData.hasTax, invoiceData.taxRate]);

  useEffect(() => {
    if (totals.total > 0) {
      generateAccountingEntries();
    }
  }, [totals, invoiceData.supplierId]);

  const loadData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadSuppliers(),
        loadItems(),
        loadAccounts(),
        generateInvoiceNumber()
      ]);
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadSuppliers = async () => {
    try {
      const allSuppliers = await db.suppliers.toArray();
      console.log('الموردين المحملين:', allSuppliers);
      setSuppliers(allSuppliers.filter(supplier => supplier.isActive !== false));
    } catch (error) {
      console.error('خطأ في تحميل الموردين:', error);
    }
  };

  const loadItems = async () => {
    try {
      const allItems = await db.items.toArray();
      console.log('الأصناف المحملة في فاتورة المشتريات:', allItems);
      setItems(allItems.filter(item => item.isActive !== false));
    } catch (error) {
      console.error('خطأ في تحميل الأصناف:', error);
    }
  };

  const loadAccounts = async () => {
    try {
      const allAccounts = await db.accounts.toArray();
      setAccounts(allAccounts);
    } catch (error) {
      console.error('خطأ في تحميل الحسابات:', error);
    }
  };

  const generateInvoiceNumber = async () => {
    try {
      const lastInvoice = await db.purchaseInvoices.orderBy('invoiceNumber').last();
      let newNumber = 'PUR0001';
      
      if (lastInvoice) {
        const lastNumber = parseInt(lastInvoice.invoiceNumber.replace('PUR', ''));
        newNumber = `PUR${String(lastNumber + 1).padStart(4, '0')}`;
      }
      
      setInvoiceData(prev => ({ ...prev, invoiceNumber: newNumber }));
    } catch (error) {
      console.error('خطأ في توليد رقم الفاتورة:', error);
    }
  };

  const calculateTotals = () => {
    const subtotal = invoiceItems.reduce((sum, item) => sum + item.total, 0);
    
    let discount = 0;
    if (invoiceData.discountType === 'percentage') {
      discount = (subtotal * invoiceData.discount) / 100;
    } else {
      discount = invoiceData.discount;
    }
    
    const afterDiscount = subtotal - discount;
    const taxAmount = invoiceData.hasTax ? (afterDiscount * invoiceData.taxRate) / 100 : 0;
    const total = afterDiscount + taxAmount;
    
    setTotals({
      subtotal,
      discount,
      taxAmount,
      total
    });
  };

  const generateAccountingEntries = () => {
    if (!invoiceData.supplierId || totals.total === 0) {
      setCurrentEntries([]);
      return;
    }

    const supplier = suppliers.find(s => s.id === parseInt(invoiceData.supplierId));
    const inventoryAccount = accounts.find(acc => acc.name.includes('المخزون'));
    const suppliersAccount = accounts.find(acc => acc.name.includes('الموردين'));
    const taxAccount = accounts.find(acc => acc.name.includes('ضريبة'));

    const entries = [];

    // قيد المخزون (مدين)
    if (inventoryAccount) {
      entries.push({
        accountId: inventoryAccount.id,
        accountName: inventoryAccount.name,
        accountCode: inventoryAccount.code,
        debit: totals.subtotal - totals.discount,
        credit: 0,
        description: `مشتريات من ${supplier?.name || 'مورد'} - فاتورة ${invoiceData.invoiceNumber}`
      });
    }

    // قيد الضريبة (مدين) إذا كانت موجودة
    if (invoiceData.hasTax && totals.taxAmount > 0 && taxAccount) {
      entries.push({
        accountId: taxAccount.id,
        accountName: taxAccount.name,
        accountCode: taxAccount.code,
        debit: totals.taxAmount,
        credit: 0,
        description: `ضريبة قيمة مضافة ${invoiceData.taxRate}% - فاتورة ${invoiceData.invoiceNumber}`
      });
    }

    // قيد الموردين (دائن)
    if (suppliersAccount) {
      entries.push({
        accountId: suppliersAccount.id,
        accountName: suppliersAccount.name,
        accountCode: suppliersAccount.code,
        debit: 0,
        credit: totals.total,
        description: `مشتريات آجلة من ${supplier?.name || 'مورد'} - فاتورة ${invoiceData.invoiceNumber}`
      });
    }

    setCurrentEntries(entries);
  };

  const handleItemChange = (index, field, value) => {
    const newItems = [...invoiceItems];
    newItems[index][field] = value;
    
    if (field === 'quantity' || field === 'cost') {
      newItems[index].total = newItems[index].quantity * newItems[index].cost;
    }
    
    setInvoiceItems(newItems);
  };

  const addItem = () => {
    setInvoiceItems([...invoiceItems, {
      itemId: '',
      quantity: 0,
      cost: 0,
      total: 0
    }]);
  };

  const removeItem = (index) => {
    if (invoiceItems.length > 1) {
      const newItems = invoiceItems.filter((_, i) => i !== index);
      setInvoiceItems(newItems);
    }
  };

  const handleSave = async () => {
    try {
      // التحقق من روابط الحسابات المطلوبة
      const validation = await dbHelpers.validateRequiredAccountLinks();
      if (!validation.isValid) {
        const linkTypeNames = {
          inventory: 'حساب المخزون',
          vat: 'حساب ضريبة القيمة المضافة',
          purchases: 'حساب المشتريات'
        };

        const missingNames = validation.missingLinks
          .map(link => linkTypeNames[link.linkType])
          .filter(name => name)
          .join('، ');

        alert(`يجب ربط الحسابات التالية قبل إنشاء فاتورة المشتريات:\n${missingNames}\n\nيرجى الذهاب إلى دليل الحسابات > ربط الحسابات`);
        return;
      }

      // التحقق من البيانات
      if (!invoiceData.supplierId) {
        alert('يرجى اختيار المورد');
        return;
      }

      if (invoiceItems.some(item => !item.itemId || item.quantity <= 0 || item.cost <= 0)) {
        alert('يرجى ملء جميع بيانات الأصناف');
        return;
      }

      // إنشاء القيد المحاسبي للمراجعة
      const journalEntry = await createPurchaseInvoiceJournalEntry(
        {
          ...invoiceData,
          supplierId: parseInt(invoiceData.supplierId),
          userId: user.id
        },
        invoiceItems.map(item => ({
          ...item,
          itemId: parseInt(item.itemId)
        }))
      );

      // عرض القيد المحاسبي للمراجعة
      setJournalEntryPreview(journalEntry);
      setShowJournalEntry(true);

    } catch (error) {
      console.error('خطأ في إنشاء القيد المحاسبي:', error);
      alert('حدث خطأ أثناء إنشاء القيد المحاسبي: ' + error.message);
    }
  };

  // دالة الحفظ النهائي بعد مراجعة القيد
  const handleFinalSave = async () => {
    try {

      // حفظ الفاتورة
      const invoiceId = await db.purchaseInvoices.add({
        ...invoiceData,
        supplierId: parseInt(invoiceData.supplierId),
        items: invoiceItems.map(item => ({
          ...item,
          itemId: parseInt(item.itemId)
        })),
        subtotal: totals.subtotal,
        discountAmount: totals.discount,
        taxAmount: totals.taxAmount,
        total: totals.total,
        paidAmount: 0,
        status: 'pending',
        userId: user.id,
        createdAt: new Date()
      });

      // تحديث المخزون
      for (const item of invoiceItems) {
        if (item.itemId && item.quantity > 0) {
          // إضافة حركة مخزون
          await db.stockMovements.add({
            itemId: parseInt(item.itemId),
            type: 'in',
            quantity: item.quantity,
            cost: item.cost,
            reference: invoiceData.invoiceNumber,
            date: new Date(invoiceData.date),
            userId: user.id,
            notes: `مشتريات - فاتورة ${invoiceData.invoiceNumber}`
          });

          // تحديث رصيد الصنف
          const currentItem = await db.items.get(parseInt(item.itemId));
          const newStock = currentItem.currentStock + item.quantity;
          
          await db.items.update(parseInt(item.itemId), {
            currentStock: newStock,
            lastCost: item.cost,
            avgCost: item.cost // يمكن تحسين حساب المتوسط لاحقاً
          });
        }
      }

      // حفظ القيد المحاسبي
      if (journalEntryPreview) {
        await db.journalEntries.add({
          ...journalEntryPreview,
          invoiceId: invoiceId,
          status: 'posted' // ترحيل القيد مباشرة
        });
      }

      alert('تم حفظ فاتورة المشتريات والقيد المحاسبي بنجاح');
      setShowJournalEntry(false);
      setJournalEntryPreview(null);
      resetForm();
    } catch (error) {
      console.error('خطأ في حفظ الفاتورة:', error);
      alert('حدث خطأ أثناء حفظ الفاتورة');
    }
  };

  const resetForm = () => {
    setInvoiceData({
      invoiceNumber: '',
      supplierId: '',
      date: new Date().toISOString().split('T')[0],
      dueDate: '',
      notes: '',
      hasTax: true,
      taxRate: 14,
      discount: 0,
      discountType: 'amount'
    });
    
    setInvoiceItems([{
      itemId: '',
      quantity: 0,
      cost: 0,
      total: 0
    }]);
    
    generateInvoiceNumber();
  };

  if (loading) {
    return (
      <div className="container">
        <div className="loading">
          <div className="spinner"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container">
      {showEntries ? (
        /* شاشة القيود المحاسبية في المقدمة */
        <div style={{ position: 'relative' }}>
          <div style={{ marginBottom: '1rem' }}>
            <button
              className="btn btn-secondary"
              onClick={() => setShowEntries(false)}
            >
              ← العودة للفاتورة
            </button>
          </div>
          <AccountingEntries
            entries={currentEntries}
            title={`القيود المحاسبية - فاتورة مشتريات ${invoiceData.invoiceNumber}`}
          />
        </div>
      ) : (
        /* فاتورة المشتريات */
        <div>
          {/* Header */}
          <div className="card" style={{ marginBottom: '2rem' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <h2 style={{ margin: 0, color: '#333' }}>فاتورة مشتريات</h2>
              <div style={{ display: 'flex', gap: '1rem' }}>
                <button
                  className="btn btn-info"
                  onClick={() => setShowEntries(true)}
                >
                  📋 عرض القيود المحاسبية
                </button>
                <button className="btn btn-success" onClick={handleSave}>
                  💾 حفظ الفاتورة
                </button>
                <button className="btn btn-secondary" onClick={resetForm}>
                  🔄 فاتورة جديدة
                </button>
              </div>
            </div>
          </div>

          {/* معلومات الفاتورة */}
          <div className="card" style={{ marginBottom: '2rem' }}>
            <div className="card-title">معلومات الفاتورة</div>
            <div className="grid grid-3">
              <div className="form-group">
                <label className="form-label">رقم الفاتورة</label>
                <input
                  type="text"
                  className="form-control"
                  value={invoiceData.invoiceNumber}
                  readOnly
                  style={{ background: '#f8f9fa' }}
                />
              </div>

              <div className="form-group">
                <label className="form-label">المورد *</label>
                <select
                  className="form-control"
                  value={invoiceData.supplierId}
                  onChange={(e) => setInvoiceData({...invoiceData, supplierId: e.target.value})}
                  required
                >
                  <option value="">اختر المورد</option>
                  {suppliers.map(supplier => (
                    <option key={supplier.id} value={supplier.id}>
                      {supplier.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label className="form-label">تاريخ الفاتورة</label>
                <input
                  type="date"
                  className="form-control"
                  value={invoiceData.date}
                  onChange={(e) => setInvoiceData({...invoiceData, date: e.target.value})}
                />
              </div>

              <div className="form-group">
                <label className="form-label">تاريخ الاستحقاق</label>
                <input
                  type="date"
                  className="form-control"
                  value={invoiceData.dueDate}
                  onChange={(e) => setInvoiceData({...invoiceData, dueDate: e.target.value})}
                />
              </div>

              <div className="form-group">
                <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                  <input
                    type="checkbox"
                    checked={invoiceData.hasTax}
                    onChange={(e) => setInvoiceData({...invoiceData, hasTax: e.target.checked})}
                  />
                  تشمل ضريبة قيمة مضافة
                </label>
              </div>

              {invoiceData.hasTax && (
                <div className="form-group">
                  <label className="form-label">معدل الضريبة (%)</label>
                  <input
                    type="number"
                    className="form-control"
                    value={invoiceData.taxRate}
                    onChange={(e) => setInvoiceData({...invoiceData, taxRate: parseFloat(e.target.value) || 0})}
                    min="0"
                    max="100"
                    step="0.1"
                  />
                </div>
              )}
            </div>

            <div className="form-group">
              <label className="form-label">ملاحظات</label>
              <textarea
                className="form-control"
                value={invoiceData.notes}
                onChange={(e) => setInvoiceData({...invoiceData, notes: e.target.value})}
                rows="2"
                placeholder="ملاحظات إضافية..."
              />
            </div>
          </div>

          {/* أصناف الفاتورة */}
          <div className="card" style={{ marginBottom: '2rem' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
              <div className="card-title" style={{ margin: 0 }}>أصناف الفاتورة</div>
              <button className="btn btn-primary" onClick={addItem}>
                ➕ إضافة صنف
              </button>
            </div>

            <div style={{ overflow: 'auto' }}>
              <table className="table">
                <thead>
                  <tr>
                    <th style={{ width: '30%' }}>الصنف</th>
                    <th style={{ width: '15%' }}>الكمية</th>
                    <th style={{ width: '20%' }}>التكلفة</th>
                    <th style={{ width: '20%' }}>الإجمالي</th>
                    <th style={{ width: '15%' }}>إجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {invoiceItems.map((item, index) => (
                    <tr key={index}>
                      <td>
                        <select
                          className="form-control"
                          value={item.itemId}
                          onChange={(e) => handleItemChange(index, 'itemId', e.target.value)}
                        >
                          <option value="">اختر الصنف</option>
                          {items.map(itm => (
                            <option key={itm.id} value={itm.id}>
                              {itm.code} - {itm.name}
                            </option>
                          ))}
                        </select>
                      </td>
                      <td>
                        <input
                          type="number"
                          className="form-control"
                          value={item.quantity}
                          onChange={(e) => handleItemChange(index, 'quantity', parseFloat(e.target.value) || 0)}
                          min="0"
                          step="0.01"
                        />
                      </td>
                      <td>
                        <input
                          type="number"
                          className="form-control"
                          value={item.cost}
                          onChange={(e) => handleItemChange(index, 'cost', parseFloat(e.target.value) || 0)}
                          min="0"
                          step="0.01"
                        />
                      </td>
                      <td>
                        <input
                          type="text"
                          className="form-control"
                          value={item.total.toLocaleString('ar-EG')}
                          readOnly
                          style={{ background: '#f8f9fa' }}
                        />
                      </td>
                      <td>
                        <button
                          className="btn btn-danger"
                          onClick={() => removeItem(index)}
                          disabled={invoiceItems.length === 1}
                          style={{ padding: '0.25rem 0.5rem', fontSize: '0.8rem' }}
                        >
                          حذف
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* الإجماليات */}
          <div className="card">
            <div className="card-title">إجماليات الفاتورة</div>
            <div className="grid grid-2">
              <div>
                <div className="form-group">
                  <label className="form-label">نوع الخصم</label>
                  <select
                    className="form-control"
                    value={invoiceData.discountType}
                    onChange={(e) => setInvoiceData({...invoiceData, discountType: e.target.value})}
                  >
                    <option value="amount">مبلغ ثابت</option>
                    <option value="percentage">نسبة مئوية</option>
                  </select>
                </div>

                <div className="form-group">
                  <label className="form-label">
                    الخصم {invoiceData.discountType === 'percentage' ? '(%)' : '(ج.م)'}
                  </label>
                  <input
                    type="number"
                    className="form-control"
                    value={invoiceData.discount}
                    onChange={(e) => setInvoiceData({...invoiceData, discount: parseFloat(e.target.value) || 0})}
                    min="0"
                    step="0.01"
                  />
                </div>
              </div>

              <div style={{ background: '#f8f9fa', padding: '1rem', borderRadius: '5px' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '0.5rem' }}>
                  <span>المجموع الفرعي:</span>
                  <strong>{totals.subtotal.toLocaleString('ar-EG')} ج.م</strong>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '0.5rem' }}>
                  <span>الخصم:</span>
                  <strong>{totals.discount.toLocaleString('ar-EG')} ج.م</strong>
                </div>
                {invoiceData.hasTax && (
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '0.5rem' }}>
                    <span>الضريبة ({invoiceData.taxRate}%):</span>
                    <strong>{totals.taxAmount.toLocaleString('ar-EG')} ج.م</strong>
                  </div>
                )}
                <hr />
                <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '1.2rem' }}>
                  <span><strong>الإجمالي:</strong></span>
                  <strong style={{ color: '#007bff' }}>{totals.total.toLocaleString('ar-EG')} ج.م</strong>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* نافذة عرض القيد المحاسبي */}
      {showJournalEntry && journalEntryPreview && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            padding: '2rem',
            borderRadius: '10px',
            width: '90%',
            maxWidth: '800px',
            maxHeight: '90vh',
            overflow: 'auto'
          }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '1.5rem',
              borderBottom: '2px solid #28a745',
              paddingBottom: '1rem'
            }}>
              <h3 style={{ margin: 0, color: '#28a745' }}>
                📋 مراجعة القيد المحاسبي - فاتورة مشتريات
              </h3>
              <button
                className="btn btn-secondary"
                onClick={() => setShowJournalEntry(false)}
              >
                ✕ إغلاق
              </button>
            </div>

            {/* معلومات القيد */}
            <div style={{
              background: '#f8f9fa',
              padding: '1rem',
              borderRadius: '5px',
              marginBottom: '1.5rem'
            }}>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem' }}>
                <div>
                  <strong>رقم القيد:</strong> {journalEntryPreview.entryNumber}
                </div>
                <div>
                  <strong>التاريخ:</strong> {new Date(journalEntryPreview.date).toLocaleDateString('ar-EG')}
                </div>
                <div>
                  <strong>المرجع:</strong> {journalEntryPreview.reference}
                </div>
                <div>
                  <strong>النوع:</strong> فاتورة مشتريات
                </div>
              </div>
              <div style={{ marginTop: '1rem' }}>
                <strong>الوصف:</strong> {journalEntryPreview.description}
              </div>
            </div>

            {/* جدول القيد */}
            <div style={{ overflow: 'auto', marginBottom: '1.5rem' }}>
              <table className="table">
                <thead>
                  <tr style={{ background: '#28a745', color: 'white' }}>
                    <th>رقم الحساب</th>
                    <th>اسم الحساب</th>
                    <th>البيان</th>
                    <th>مدين</th>
                    <th>دائن</th>
                  </tr>
                </thead>
                <tbody>
                  {journalEntryPreview.entries.map((entry, index) => (
                    <tr key={index} style={{
                      background: index % 2 === 0 ? '#f8f9fa' : 'white'
                    }}>
                      <td style={{
                        fontFamily: 'monospace',
                        fontWeight: 'bold',
                        color: '#28a745'
                      }}>
                        {entry.accountCode}
                      </td>
                      <td style={{ fontWeight: 'bold' }}>
                        {entry.accountName}
                      </td>
                      <td>{entry.description}</td>
                      <td style={{
                        color: entry.debit > 0 ? '#dc3545' : '#6c757d',
                        fontWeight: entry.debit > 0 ? 'bold' : 'normal'
                      }}>
                        {entry.debit > 0 ? entry.debit.toLocaleString('ar-EG') : '-'}
                      </td>
                      <td style={{
                        color: entry.credit > 0 ? '#28a745' : '#6c757d',
                        fontWeight: entry.credit > 0 ? 'bold' : 'normal'
                      }}>
                        {entry.credit > 0 ? entry.credit.toLocaleString('ar-EG') : '-'}
                      </td>
                    </tr>
                  ))}
                </tbody>
                <tfoot>
                  <tr style={{ background: '#e9ecef', fontWeight: 'bold' }}>
                    <td colSpan="3" style={{ textAlign: 'center' }}>الإجمالي</td>
                    <td style={{ color: '#dc3545' }}>
                      {journalEntryPreview.entries.reduce((sum, entry) => sum + entry.debit, 0).toLocaleString('ar-EG')} ج.م
                    </td>
                    <td style={{ color: '#28a745' }}>
                      {journalEntryPreview.entries.reduce((sum, entry) => sum + entry.credit, 0).toLocaleString('ar-EG')} ج.م
                    </td>
                  </tr>
                </tfoot>
              </table>
            </div>

            {/* التحقق من توازن القيد */}
            <div style={{
              background: '#d4edda',
              border: '1px solid #c3e6cb',
              padding: '1rem',
              borderRadius: '5px',
              marginBottom: '1.5rem',
              textAlign: 'center'
            }}>
              <div style={{ color: '#155724', fontWeight: 'bold' }}>
                ✅ القيد متوازن - يمكن المتابعة
              </div>
              <div style={{ fontSize: '0.9rem', color: '#155724', marginTop: '0.5rem' }}>
                إجمالي المدين = إجمالي الدائن = {journalEntryPreview.entries.reduce((sum, entry) => sum + entry.debit, 0).toLocaleString('ar-EG')} ج.م
              </div>
            </div>

            {/* أزرار الإجراءات */}
            <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center' }}>
              <button
                className="btn btn-success"
                onClick={handleFinalSave}
                style={{ padding: '0.75rem 2rem' }}
              >
                ✅ موافق - حفظ الفاتورة والقيد
              </button>
              <button
                className="btn btn-secondary"
                onClick={() => setShowJournalEntry(false)}
                style={{ padding: '0.75rem 2rem' }}
              >
                ❌ إلغاء
              </button>
            </div>

            {/* ملاحظة مهمة */}
            <div style={{
              marginTop: '1.5rem',
              padding: '1rem',
              background: '#fff3cd',
              border: '1px solid #ffeaa7',
              borderRadius: '5px',
              fontSize: '0.9rem',
              color: '#856404'
            }}>
              <strong>ملاحظة:</strong> بعد الموافقة سيتم حفظ الفاتورة وترحيل القيد المحاسبي تلقائياً وتحديث أرصدة المخزون.
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PurchaseInvoice;
