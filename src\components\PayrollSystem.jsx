import React, { useState, useEffect } from 'react';
import { db } from '../database/db';
import * as XLSX from 'xlsx';

const PayrollSystem = () => {
  const [currentView, setCurrentView] = useState('list');
  const [payrolls, setPayrolls] = useState([]);
  const [workers, setWorkers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedPayroll, setSelectedPayroll] = useState(null);
  const [payrollDetails, setPayrollDetails] = useState([]);
  
  const [payrollForm, setPayrollForm] = useState({
    month: new Date().getMonth() + 1,
    year: new Date().getFullYear(),
    payrollNumber: '',
    isPosted: false
  });

  const [workerPayrollData, setWorkerPayrollData] = useState([]);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadPayrolls(),
        loadWorkers()
      ]);
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadPayrolls = async () => {
    try {
      const allPayrolls = await db.payrolls.orderBy('createdAt').reverse().toArray();
      setPayrolls(allPayrolls);
    } catch (error) {
      console.error('خطأ في تحميل كشوف المرتبات:', error);
    }
  };

  const loadWorkers = async () => {
    try {
      const allWorkers = await db.workers.where('isActive').equals(true).toArray();
      setWorkers(allWorkers);
    } catch (error) {
      console.error('خطأ في تحميل العمال:', error);
    }
  };

  const generatePayrollNumber = async () => {
    const count = await db.payrolls.count();
    return `PAY${String(count + 1).padStart(6, '0')}`;
  };

  const calculateWorkerPayroll = async (workerId, month, year) => {
    try {
      // الحصول على جميع مدفوعات العامل في الشهر المحدد
      const startDate = new Date(year, month - 1, 1);
      const endDate = new Date(year, month, 0);
      
      const payments = await db.workerPayments
        .where('workerId')
        .equals(workerId)
        .and(payment => {
          const paymentDate = new Date(payment.date);
          return paymentDate >= startDate && paymentDate <= endDate;
        })
        .toArray();

      const totalAmount = payments.reduce((sum, payment) => sum + payment.amount, 0);
      const paidAmount = payments.filter(p => p.isPaid).reduce((sum, payment) => sum + payment.amount, 0);
      const unpaidAmount = totalAmount - paidAmount;

      return {
        workerId,
        totalAmount,
        paidAmount,
        unpaidAmount,
        paymentsCount: payments.length
      };
    } catch (error) {
      console.error('خطأ في حساب راتب العامل:', error);
      return {
        workerId,
        totalAmount: 0,
        paidAmount: 0,
        unpaidAmount: 0,
        paymentsCount: 0
      };
    }
  };

  const handleCreatePayroll = async () => {
    try {
      if (!payrollForm.payrollNumber) {
        payrollForm.payrollNumber = await generatePayrollNumber();
      }

      // حساب رواتب جميع العمال للشهر المحدد
      const workerPayrolls = [];
      let totalAmount = 0;

      for (const worker of workers) {
        const payrollData = await calculateWorkerPayroll(worker.id, payrollForm.month, payrollForm.year);
        
        if (payrollData.totalAmount > 0) {
          workerPayrolls.push({
            workerId: worker.id,
            workerName: worker.name,
            workerCode: worker.code,
            basicSalary: payrollData.totalAmount,
            overtime: 0,
            deductions: 0,
            netSalary: payrollData.totalAmount,
            paidAmount: payrollData.paidAmount,
            unpaidAmount: payrollData.unpaidAmount
          });
          
          totalAmount += payrollData.totalAmount;
        }
      }

      if (workerPayrolls.length === 0) {
        alert('لا توجد بيانات رواتب للشهر المحدد');
        return;
      }

      // إنشاء كشف المرتبات
      const payrollId = await db.payrolls.add({
        payrollNumber: payrollForm.payrollNumber,
        month: payrollForm.month,
        year: payrollForm.year,
        totalAmount: totalAmount,
        userId: 1,
        createdAt: new Date(),
        isPosted: false
      });

      // إضافة تفاصيل كشف المرتبات
      for (const workerPayroll of workerPayrolls) {
        await db.payrollDetails.add({
          payrollId: payrollId,
          workerId: workerPayroll.workerId,
          basicSalary: workerPayroll.basicSalary,
          overtime: workerPayroll.overtime,
          deductions: workerPayroll.deductions,
          netSalary: workerPayroll.netSalary
        });
      }

      alert('تم إنشاء كشف المرتبات بنجاح');
      loadPayrolls();
      setCurrentView('list');
      resetForm();
    } catch (error) {
      console.error('خطأ في إنشاء كشف المرتبات:', error);
      alert('حدث خطأ أثناء إنشاء كشف المرتبات');
    }
  };

  const handleViewPayroll = async (payroll) => {
    try {
      setSelectedPayroll(payroll);
      const details = await db.payrollDetails.where('payrollId').equals(payroll.id).toArray();
      
      // إضافة أسماء العمال
      const detailsWithNames = details.map(detail => {
        const worker = workers.find(w => w.id === detail.workerId);
        return {
          ...detail,
          workerName: worker ? worker.name : 'غير محدد',
          workerCode: worker ? worker.code : 'غير محدد'
        };
      });
      
      setPayrollDetails(detailsWithNames);
      setCurrentView('details');
    } catch (error) {
      console.error('خطأ في تحميل تفاصيل كشف المرتبات:', error);
      alert('حدث خطأ أثناء تحميل تفاصيل كشف المرتبات');
    }
  };

  const handlePostPayroll = async (payrollId) => {
    if (!confirm('هل أنت متأكد من ترحيل كشف المرتبات؟ لن يمكن تعديله بعد الترحيل.')) return;
    
    try {
      await db.payrolls.update(payrollId, {
        isPosted: true,
        postedAt: new Date()
      });
      
      alert('تم ترحيل كشف المرتبات بنجاح');
      loadPayrolls();
    } catch (error) {
      console.error('خطأ في ترحيل كشف المرتبات:', error);
      alert('حدث خطأ أثناء ترحيل كشف المرتبات');
    }
  };

  const handleDeletePayroll = async (payrollId) => {
    if (!confirm('هل أنت متأكد من حذف كشف المرتبات؟')) return;
    
    try {
      // حذف التفاصيل أولاً
      await db.payrollDetails.where('payrollId').equals(payrollId).delete();
      // ثم حذف كشف المرتبات
      await db.payrolls.delete(payrollId);
      
      alert('تم حذف كشف المرتبات بنجاح');
      loadPayrolls();
    } catch (error) {
      console.error('خطأ في حذف كشف المرتبات:', error);
      alert('حدث خطأ أثناء حذف كشف المرتبات');
    }
  };

  const exportPayrollToExcel = (payroll, details) => {
    try {
      const exportData = details.map(detail => ({
        'كود العامل': detail.workerCode,
        'اسم العامل': detail.workerName,
        'الراتب الأساسي': detail.basicSalary,
        'الإضافي': detail.overtime,
        'الخصومات': detail.deductions,
        'صافي الراتب': detail.netSalary
      }));

      const ws = XLSX.utils.json_to_sheet(exportData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'كشف المرتبات');
      
      const fileName = `كشف_مرتبات_${payroll.month}_${payroll.year}_${payroll.payrollNumber}.xlsx`;
      XLSX.writeFile(wb, fileName);
      
      alert('تم تصدير كشف المرتبات بنجاح');
    } catch (error) {
      console.error('خطأ في تصدير البيانات:', error);
      alert('حدث خطأ أثناء تصدير البيانات');
    }
  };

  const resetForm = () => {
    setPayrollForm({
      month: new Date().getMonth() + 1,
      year: new Date().getFullYear(),
      payrollNumber: '',
      isPosted: false
    });
  };

  const getMonthName = (month) => {
    const months = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    return months[month - 1];
  };

  if (loading) {
    return (
      <div className="container">
        <div className="loading">
          <div className="spinner"></div>
        </div>
      </div>
    );
  }

  if (currentView === 'details' && selectedPayroll) {
    return (
      <div className="container">
        <div className="card" style={{ marginBottom: '2rem' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <h2 style={{ margin: 0, color: '#333' }}>
              تفاصيل كشف المرتبات - {selectedPayroll.payrollNumber}
            </h2>
            <div style={{ display: 'flex', gap: '1rem' }}>
              <button
                className="btn btn-success"
                onClick={() => exportPayrollToExcel(selectedPayroll, payrollDetails)}
              >
                📊 تصدير Excel
              </button>
              <button
                className="btn btn-secondary"
                onClick={() => setCurrentView('list')}
              >
                ← العودة
              </button>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-title">
            {getMonthName(selectedPayroll.month)} {selectedPayroll.year}
          </div>

          {payrollDetails.length > 0 ? (
            <div style={{ overflow: 'auto' }}>
              <table className="table">
                <thead>
                  <tr>
                    <th>كود العامل</th>
                    <th>اسم العامل</th>
                    <th>الراتب الأساسي</th>
                    <th>الإضافي</th>
                    <th>الخصومات</th>
                    <th>صافي الراتب</th>
                  </tr>
                </thead>
                <tbody>
                  {payrollDetails.map(detail => (
                    <tr key={detail.id}>
                      <td><strong>{detail.workerCode}</strong></td>
                      <td>{detail.workerName}</td>
                      <td>{detail.basicSalary.toLocaleString('ar-EG')} ج.م</td>
                      <td>{detail.overtime.toLocaleString('ar-EG')} ج.م</td>
                      <td style={{ color: '#dc3545' }}>
                        {detail.deductions.toLocaleString('ar-EG')} ج.م
                      </td>
                      <td style={{ fontWeight: 'bold', color: '#007bff' }}>
                        {detail.netSalary.toLocaleString('ar-EG')} ج.م
                      </td>
                    </tr>
                  ))}
                </tbody>
                <tfoot>
                  <tr style={{ background: '#f8f9fa', fontWeight: 'bold' }}>
                    <td colSpan="5">الإجمالي:</td>
                    <td style={{ color: '#007bff' }}>
                      {payrollDetails.reduce((sum, detail) => sum + detail.netSalary, 0).toLocaleString('ar-EG')} ج.م
                    </td>
                  </tr>
                </tfoot>
              </table>
            </div>
          ) : (
            <div style={{ textAlign: 'center', color: '#666', padding: '2rem' }}>
              لا توجد تفاصيل لكشف المرتبات
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="container">
      {/* Header */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h2 style={{ margin: 0, color: '#333' }}>نظام الرواتب</h2>
          <button
            className="btn btn-primary"
            onClick={() => setCurrentView('create')}
          >
            ➕ إنشاء كشف مرتبات جديد
          </button>
        </div>
      </div>

      {/* Create Payroll Form */}
      {currentView === 'create' && (
        <div className="card" style={{ marginBottom: '2rem' }}>
          <div className="card-title">إنشاء كشف مرتبات جديد</div>
          
          <div className="grid grid-3">
            <div className="form-group">
              <label className="form-label">الشهر</label>
              <select
                className="form-control"
                value={payrollForm.month}
                onChange={(e) => setPayrollForm({...payrollForm, month: parseInt(e.target.value)})}
              >
                {Array.from({length: 12}, (_, i) => (
                  <option key={i + 1} value={i + 1}>
                    {getMonthName(i + 1)}
                  </option>
                ))}
              </select>
            </div>

            <div className="form-group">
              <label className="form-label">السنة</label>
              <input
                type="number"
                className="form-control"
                value={payrollForm.year}
                onChange={(e) => setPayrollForm({...payrollForm, year: parseInt(e.target.value)})}
                min="2020"
                max="2030"
              />
            </div>

            <div className="form-group">
              <label className="form-label">رقم كشف المرتبات</label>
              <input
                type="text"
                className="form-control"
                value={payrollForm.payrollNumber}
                onChange={(e) => setPayrollForm({...payrollForm, payrollNumber: e.target.value})}
                placeholder="سيتم إنشاؤه تلقائياً"
              />
            </div>
          </div>

          <div style={{ display: 'flex', gap: '1rem', marginTop: '2rem' }}>
            <button
              className="btn btn-primary"
              onClick={handleCreatePayroll}
            >
              إنشاء كشف المرتبات
            </button>
            <button
              className="btn btn-secondary"
              onClick={() => setCurrentView('list')}
            >
              إلغاء
            </button>
          </div>
        </div>
      )}

      {/* Payrolls List */}
      <div className="card">
        <div className="card-title">
          كشوف المرتبات ({payrolls.length})
        </div>
        
        {payrolls.length > 0 ? (
          <div style={{ overflow: 'auto' }}>
            <table className="table">
              <thead>
                <tr>
                  <th>رقم الكشف</th>
                  <th>الشهر/السنة</th>
                  <th>إجمالي المبلغ</th>
                  <th>تاريخ الإنشاء</th>
                  <th>الحالة</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {payrolls.map(payroll => (
                  <tr key={payroll.id}>
                    <td><strong>{payroll.payrollNumber}</strong></td>
                    <td>{getMonthName(payroll.month)} {payroll.year}</td>
                    <td style={{ fontWeight: 'bold', color: '#007bff' }}>
                      {payroll.totalAmount.toLocaleString('ar-EG')} ج.م
                    </td>
                    <td>{new Date(payroll.createdAt).toLocaleDateString('ar-EG')}</td>
                    <td>
                      <span style={{
                        padding: '0.25rem 0.5rem',
                        borderRadius: '4px',
                        fontSize: '0.8rem',
                        background: payroll.isPosted ? '#d4edda' : '#fff3cd',
                        color: payroll.isPosted ? '#155724' : '#856404'
                      }}>
                        {payroll.isPosted ? 'مرحل' : 'مسودة'}
                      </span>
                    </td>
                    <td>
                      <div style={{ display: 'flex', gap: '0.5rem', flexWrap: 'wrap' }}>
                        <button
                          className="btn btn-info"
                          style={{ padding: '0.25rem 0.5rem', fontSize: '0.8rem' }}
                          onClick={() => handleViewPayroll(payroll)}
                        >
                          👁️ عرض
                        </button>
                        
                        {!payroll.isPosted && (
                          <>
                            <button
                              className="btn btn-success"
                              style={{ padding: '0.25rem 0.5rem', fontSize: '0.8rem' }}
                              onClick={() => handlePostPayroll(payroll.id)}
                            >
                              ✅ ترحيل
                            </button>
                            <button
                              className="btn btn-danger"
                              style={{ padding: '0.25rem 0.5rem', fontSize: '0.8rem' }}
                              onClick={() => handleDeletePayroll(payroll.id)}
                            >
                              🗑️ حذف
                            </button>
                          </>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div style={{ textAlign: 'center', color: '#666', padding: '2rem' }}>
            لا توجد كشوف مرتبات
          </div>
        )}
      </div>
    </div>
  );
};

export default PayrollSystem;
