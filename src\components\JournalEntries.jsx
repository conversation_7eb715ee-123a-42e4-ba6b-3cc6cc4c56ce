import React, { useState, useEffect } from 'react';
import { db } from '../database/db';
import { useAuth } from '../contexts/AuthContext';

const JournalEntries = () => {
  const { user } = useAuth();
  const [currentView, setCurrentView] = useState('list'); // 'list', 'create', 'edit', 'templates'
  const [journalEntries, setJournalEntries] = useState([]);
  const [accounts, setAccounts] = useState([]);
  const [contracts, setContracts] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [suppliers, setSuppliers] = useState([]);
  const [templates, setTemplates] = useState([]);
  const [purchaseInvoices, setPurchaseInvoices] = useState([]);
  const [salesInvoices, setSalesInvoices] = useState([]);
  const [workerVouchers, setWorkerVouchers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [editingEntry, setEditingEntry] = useState(null);

  // فلاتر البحث
  const [searchFilters, setSearchFilters] = useState({
    entryNumber: '',
    description: '',
    type: '',
    dateFrom: '',
    dateTo: '',
    accountId: ''
  });

  // بيانات القيد الجديد
  const [entryData, setEntryData] = useState({
    entryNumber: '',
    date: new Date().toISOString().split('T')[0],
    description: '',
    reference: '',
    type: 'manual',
    contractId: '',
    customerId: '',
    supplierId: '',
    amount: 0,
    paymentMethod: 'cash', // cash, bank, check
    accountCode: '', // رقم الحساب المدخل يدوياً
    accountName: '', // اسم الحساب المعروض تلقائياً
    selectedInvoices: [], // الفواتير المختارة للسداد/التحصيل
    selectedVouchers: [] // أذون الصرف المختارة للسداد
  });

  // بنود القيد (المدين والدائن)
  const [entryDetails, setEntryDetails] = useState([]);

  // بيانات بند القيد الحالي (للإدخال اليدوي المحسن)
  const [currentEntry, setCurrentEntry] = useState({
    accountCode: '',
    accountName: '',
    description: '',
    debit: '',
    credit: ''
  });

  // بحث الحسابات
  const [accountSearchTerm, setAccountSearchTerm] = useState('');
  const [showAccountSuggestions, setShowAccountSuggestions] = useState(false);

  useEffect(() => {
    loadJournalEntries();
    loadAccounts();
    loadContracts();
    loadCustomers();
    loadSuppliers();
    loadContracts();
    loadTemplates();
    loadPurchaseInvoices();
    loadSalesInvoices();
    loadWorkerVouchers();
  }, []);

  const loadJournalEntries = async () => {
    try {
      setLoading(true);
      const entries = await db.journalEntries.orderBy('createdAt').reverse().toArray();
      console.log('القيود المحملة من قاعدة البيانات:', entries);

      // إضافة أسماء الحسابات للقيود
      const entriesWithAccountNames = await Promise.all(
        entries.map(async (entry) => {
          const entriesWithNames = await Promise.all(
            entry.entries.map(async (detail) => {
              let accountName = detail.accountName;

              // إذا كان هناك accountId، جلب اسم الحساب من قاعدة البيانات
              if (detail.accountId) {
                const account = await db.accounts.get(detail.accountId);
                accountName = account ? account.name : 'حساب محذوف';
              }
              // إذا لم يكن هناك accountId ولكن هناك accountCode، البحث بالكود
              else if (detail.accountCode && !accountName) {
                const account = accounts.find(acc => acc.code === detail.accountCode);
                accountName = account ? account.name : detail.accountCode;
              }

              return {
                ...detail,
                accountName: accountName || detail.accountName || 'حساب غير محدد'
              };
            })
          );
          return {
            ...entry,
            entries: entriesWithNames
          };
        })
      );

      console.log('القيود بعد إضافة أسماء الحسابات:', entriesWithAccountNames);
      setJournalEntries(entriesWithAccountNames);
    } catch (error) {
      console.error('خطأ في تحميل القيود:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadAccounts = async () => {
    try {
      const allAccounts = await db.accounts.toArray();
      console.log('جميع الحسابات المحملة:', allAccounts);

      // فلترة الحسابات النشطة (التي ليس لها isActive = false)
      const activeAccounts = allAccounts.filter(account => account.isActive !== false);
      console.log('الحسابات النشطة:', activeAccounts);

      setAccounts(activeAccounts);
    } catch (error) {
      console.error('خطأ في تحميل الحسابات:', error);
      setAccounts([]);
    }
  };

  const loadContracts = async () => {
    try {
      const allContracts = await db.contracts?.toArray() || [];
      console.log('العقود المحملة:', allContracts);
      setContracts(allContracts.filter(contract => contract.isActive !== false));
    } catch (error) {
      console.error('خطأ في تحميل العقود:', error);
      setContracts([]);
    }
  };

  const loadCustomers = async () => {
    try {
      const allCustomers = await db.customers.toArray();
      console.log('العملاء المحملين:', allCustomers);
      setCustomers(allCustomers.filter(customer => customer.isActive !== false));
    } catch (error) {
      console.error('خطأ في تحميل العملاء:', error);
      setCustomers([]);
    }
  };

  const loadSuppliers = async () => {
    try {
      const allSuppliers = await db.suppliers.toArray();
      console.log('الموردين المحملين:', allSuppliers);
      setSuppliers(allSuppliers.filter(supplier => supplier.isActive !== false));
    } catch (error) {
      console.error('خطأ في تحميل الموردين:', error);
      setSuppliers([]);
    }
  };

  const loadTemplates = async () => {
    try {
      if (db.journalTemplates) {
        const allTemplates = await db.journalTemplates.toArray();
        setTemplates(allTemplates);
      } else {
        console.log('جدول القوالب غير متوفر');
        setTemplates([]);
      }
    } catch (error) {
      console.log('خطأ في تحميل القوالب:', error);
      setTemplates([]);
    }
  };

  const loadPurchaseInvoices = async () => {
    try {
      const allInvoices = await db.purchaseInvoices.toArray();
      console.log('فواتير المشتريات المحملة:', allInvoices);
      setPurchaseInvoices(allInvoices);
    } catch (error) {
      console.error('خطأ في تحميل فواتير المشتريات:', error);
      setPurchaseInvoices([]);
    }
  };

  const loadSalesInvoices = async () => {
    try {
      const allInvoices = await db.salesInvoices.toArray();
      console.log('فواتير المبيعات المحملة:', allInvoices);
      setSalesInvoices(allInvoices);
    } catch (error) {
      console.error('خطأ في تحميل فواتير المبيعات:', error);
      setSalesInvoices([]);
    }
  };

  const loadWorkerVouchers = async () => {
    try {
      console.log('بدء تحميل أذون العمال...');

      // تحميل أذون العمال من الجدول الصحيح
      let allVouchers = [];

      try {
        allVouchers = await db.workerVouchers.orderBy('createdAt').reverse().toArray();
        console.log('أذون العمال المحملة من workerVouchers:', allVouchers);

        // إضافة بيانات العامل لكل إذن
        const vouchersWithWorkers = await Promise.all(
          allVouchers.map(async (voucher) => {
            try {
              const worker = await db.workers.get(voucher.workerId);
              return {
                ...voucher,
                workerName: worker?.name || 'غير معروف',
                amount: voucher.totalAmount || voucher.amount || 0,
                totalAmount: voucher.totalAmount || voucher.amount || 0
              };
            } catch (error) {
              console.warn(`خطأ في تحميل بيانات العامل ${voucher.workerId}:`, error);
              return {
                ...voucher,
                workerName: 'غير معروف',
                amount: voucher.totalAmount || voucher.amount || 0,
                totalAmount: voucher.totalAmount || voucher.amount || 0
              };
            }
          })
        );

        allVouchers = vouchersWithWorkers;
      } catch (error) {
        console.warn('خطأ في تحميل من workerVouchers:', error);

        // محاولة تحميل من جداول أخرى كبديل
        try {
          if (db.issueVouchers) {
            allVouchers = await db.issueVouchers.toArray();
            console.log('تم التحميل من issueVouchers:', allVouchers);
          } else if (db.laborVouchers) {
            allVouchers = await db.laborVouchers.toArray();
            console.log('تم التحميل من laborVouchers:', allVouchers);
          }
        } catch (fallbackError) {
          console.error('خطأ في التحميل من الجداول البديلة:', fallbackError);
        }
      }

      console.log('إجمالي أذون الصرف المحملة:', allVouchers.length);
      setWorkerVouchers(allVouchers);
    } catch (error) {
      console.error('خطأ في تحميل أذون الصرف:', error);
      setWorkerVouchers([]);
    }
  };



  // دالة للحصول على اسم الحساب تلقائياً من دليل الحسابات
  const getAccountName = (accountCode) => {
    const account = accounts.find(acc => acc.code === accountCode);
    return account ? account.name : '';
  };

  // دالة البحث في الحسابات
  const getFilteredAccounts = () => {
    if (!accountSearchTerm.trim()) return accounts;

    const searchTerm = accountSearchTerm.toLowerCase().trim();
    return accounts.filter(account =>
      account.code.toLowerCase().includes(searchTerm) ||
      account.name.toLowerCase().includes(searchTerm)
    );
  };

  // دالة اختيار الحساب من القائمة المنسدلة
  const selectAccount = (account) => {
    setCurrentEntry({
      ...currentEntry,
      accountCode: account.code,
      accountName: account.name
    });
    setAccountSearchTerm('');
    setShowAccountSuggestions(false);
  };

  const generateEntryNumber = async () => {
    try {
      const lastEntry = await db.journalEntries.orderBy('id').last();
      const nextNumber = lastEntry ? (parseInt(lastEntry.entryNumber.split('-')[1]) + 1) : 1;
      return `JE-${nextNumber.toString().padStart(6, '0')}`;
    } catch (error) {
      return `JE-${Date.now()}`;
    }
  };

  // دوال إدارة القيود اليومية المحسنة
  const addEntryLine = () => {
    if (!currentEntry.accountCode.trim()) {
      alert('يرجى إدخال رقم الحساب');
      return;
    }

    if (!currentEntry.debit && !currentEntry.credit) {
      alert('يرجى إدخال مبلغ في المدين أو الدائن');
      return;
    }

    if (currentEntry.debit && currentEntry.credit) {
      alert('لا يمكن أن يكون البند مدين ودائن في نفس الوقت');
      return;
    }

    const debitAmount = parseFloat(currentEntry.debit) || 0;
    const creditAmount = parseFloat(currentEntry.credit) || 0;

    if (debitAmount <= 0 && creditAmount <= 0) {
      alert('يرجى إدخال مبلغ صحيح أكبر من صفر');
      return;
    }

    const newEntry = {
      accountCode: currentEntry.accountCode.trim(),
      accountName: currentEntry.accountName.trim() || getAccountName(currentEntry.accountCode.trim()),
      description: currentEntry.description.trim(),
      debit: debitAmount,
      credit: creditAmount
    };

    setEntryDetails(prev => [...prev, newEntry]);

    // مسح البيانات الحالية
    setCurrentEntry({
      accountCode: '',
      accountName: '',
      description: '',
      debit: '',
      credit: ''
    });
  };

  const removeEntryLine = (index) => {
    setEntryDetails(prev => prev.filter((_, i) => i !== index));
  };

  const getTotalDebit = () => {
    return entryDetails.reduce((sum, entry) => sum + (entry.debit || 0), 0);
  };

  const getTotalCredit = () => {
    return entryDetails.reduce((sum, entry) => sum + (entry.credit || 0), 0);
  };

  const isBalanced = () => {
    return getTotalDebit() === getTotalCredit() && getTotalDebit() > 0;
  };

  const resetManualEntryForm = () => {
    setEntryData({
      entryNumber: '',
      date: new Date().toISOString().split('T')[0],
      description: '',
      reference: '',
      type: 'manual',
      contractId: '',
      customerId: '',
      supplierId: '',
      amount: 0,
      paymentMethod: 'cash',
      accountCode: '',
      accountName: '',
      selectedInvoices: [],
      selectedVouchers: []
    });
    setEntryDetails([]);
    setCurrentEntry({
      accountCode: '',
      accountName: '',
      description: '',
      debit: '',
      credit: ''
    });
    generateEntryNumber().then(number => {
      setEntryData(prev => ({ ...prev, entryNumber: number }));
    });
  };

  // إنشاء رقم مرجعي تلقائي حسب نوع القيد
  const generateReferenceNumber = async (entryType) => {
    try {
      const today = new Date();
      const year = today.getFullYear();
      const month = String(today.getMonth() + 1).padStart(2, '0');

      const prefixes = {
        'purchase-payment': 'PP',
        'sales-collection': 'SC',
        'contract-expense': 'CE',
        'worker-payment': 'WP',
        'manual': 'MN'
      };

      const prefix = prefixes[entryType] || 'GN';

      // البحث عن آخر رقم مرجعي من نفس النوع في نفس الشهر
      const existingRefs = await db.journalEntries
        .where('reference')
        .startsWith(`${prefix}-${year}${month}`)
        .toArray();

      const nextNumber = existingRefs.length + 1;
      return `${prefix}-${year}${month}-${String(nextNumber).padStart(3, '0')}`;
    } catch (error) {
      console.error('خطأ في إنشاء الرقم المرجعي:', error);
      return `REF-${Date.now()}`;
    }
  };

  // التحقق من صحة رقم الحساب
  const validateAccountCode = (code) => {
    console.log('التحقق من رقم الحساب:', code);
    console.log('عدد الحسابات المتاحة:', accounts.length);
    console.log('أول 5 حسابات:', accounts.slice(0, 5));

    if (!code || !code.trim()) {
      return { valid: false, account: null };
    }

    // البحث بطرق مختلفة للتأكد
    const trimmedCode = code.trim();
    let account = accounts.find(acc => acc.code === trimmedCode);

    // إذا لم نجد، جرب البحث بدون مسافات إضافية
    if (!account) {
      account = accounts.find(acc => acc.code && acc.code.toString().trim() === trimmedCode);
    }

    // إذا لم نجد، جرب البحث الجزئي
    if (!account) {
      account = accounts.find(acc => acc.code && acc.code.toString().includes(trimmedCode));
    }

    console.log('الحساب الموجود:', account);

    if (account) {
      return { valid: true, account };
    } else {
      console.log('لم يتم العثور على الحساب. أرقام الحسابات المتاحة:', accounts.map(acc => acc.code));
      return { valid: false, account: null };
    }
  };

  // الحصول على الفواتير غير المسددة للمورد
  const getUnpaidPurchaseInvoices = (supplierId) => {
    console.log('البحث عن فواتير المورد:', supplierId);
    console.log('جميع فواتير المشتريات:', purchaseInvoices);

    const filtered = purchaseInvoices.filter(invoice => {
      const matchesSupplier = invoice.supplierId === parseInt(supplierId);
      const remainingAmount = invoice.totalAmount - (invoice.paidAmount || 0);
      const isUnpaid = invoice.paymentStatus !== 'paid' && remainingAmount > 0;

      console.log(`فاتورة ${invoice.invoiceNumber}: مورد=${matchesSupplier}, متبقي=${remainingAmount}, غير مسددة=${isUnpaid}`);

      return matchesSupplier && (invoice.paymentStatus !== 'paid' || remainingAmount > 0);
    });

    console.log('الفواتير المفلترة:', filtered);
    return filtered;
  };

  // الحصول على الفواتير غير المحصلة للعميل
  const getUnpaidSalesInvoices = (customerId) => {
    console.log('البحث عن فواتير العميل:', customerId);
    console.log('جميع فواتير المبيعات:', salesInvoices);

    const filtered = salesInvoices.filter(invoice => {
      const matchesCustomer = invoice.customerId === parseInt(customerId);
      // التعامل مع أسماء الحقول المختلفة
      const totalAmount = invoice.total || invoice.totalAmount || 0;
      const remainingAmount = totalAmount - (invoice.paidAmount || 0);
      const isUnpaid = (invoice.paymentStatus !== 'paid' && invoice.status !== 'paid') && remainingAmount > 0;

      console.log(`فاتورة ${invoice.invoiceNumber}: عميل=${matchesCustomer}, إجمالي=${totalAmount}, مدفوع=${invoice.paidAmount}, متبقي=${remainingAmount}, غير مسددة=${isUnpaid}`);

      return matchesCustomer && isUnpaid;
    });

    console.log('الفواتير المفلترة:', filtered);
    return filtered;
  };

  // الحصول على أذون الصرف غير المسددة
  const getUnpaidWorkerVouchers = () => {
    console.log('جميع أذون الصرف:', workerVouchers);

    const filtered = workerVouchers.filter(voucher => {
      // التحقق من حالة الإذن - يجب أن يكون معتمد أو مدفوع جزئياً
      const isApprovedOrPartial = voucher.status === 'approved' || voucher.status === 'partial';

      // حساب المبلغ المتبقي
      const totalAmount = voucher.totalAmount || voucher.amount || 0;
      const paidAmount = voucher.paidAmount || 0;
      const remainingAmount = totalAmount - paidAmount;

      // التحقق من أن هناك مبلغ متبقي للسداد
      const hasRemainingAmount = remainingAmount > 0;

      // التحقق من حالة الدفع
      const isNotFullyPaid = voucher.status !== 'paid' && voucher.paymentStatus !== 'paid';

      console.log(`إذن ${voucher.voucherNumber}: حالة=${voucher.status}, إجمالي=${totalAmount}, مدفوع=${paidAmount}, متبقي=${remainingAmount}, معتمد=${isApprovedOrPartial}, غير مسدد=${isNotFullyPaid}`);

      return isApprovedOrPartial && hasRemainingAmount && isNotFullyPaid;
    });

    console.log('أذون الصرف المفلترة للسداد:', filtered);
    return filtered;
  };

  // إنشاء قيد سداد فاتورة مشتريات محسن
  const createPurchasePaymentEntry = async () => {
    if (!entryData.supplierId || entryData.selectedInvoices.length === 0) {
      alert('يرجى اختيار المورد والفواتير المراد سدادها');
      return;
    }

    if (!entryData.accountCode) {
      alert('يرجى إدخال رقم حساب الدفع');
      return;
    }

    const validation = validateAccountCode(entryData.accountCode);
    if (!validation.valid) {
      alert('رقم الحساب المدخل غير صحيح');
      return;
    }

    const supplier = suppliers.find(s => s.id === parseInt(entryData.supplierId));
    const suppliersAccount = accounts.find(acc => acc.code === '211'); // الموردين
    const paymentAccount = validation.account;

    const totalPayment = entryData.selectedInvoices.reduce((sum, inv) => sum + parseFloat(inv.paymentAmount || 0), 0);

    const entryDetailsArray = [
      {
        accountId: suppliersAccount?.id || '',
        accountCode: suppliersAccount?.code || '211',
        accountName: suppliersAccount?.name || 'الموردين',
        debit: totalPayment,
        credit: 0,
        description: `سداد فواتير للمورد: ${supplier?.name}`
      },
      {
        accountId: paymentAccount.id,
        accountCode: paymentAccount.code,
        accountName: paymentAccount.name,
        debit: 0,
        credit: totalPayment,
        description: `سداد من حساب: ${paymentAccount.name}`
      }
    ];

    setEntryDetails(entryDetailsArray);

    const invoiceNumbers = entryData.selectedInvoices.map(inv => inv.invoiceNumber).join(', ');
    const updatedEntryData = {
      ...entryData,
      description: `سداد فواتير مشتريات أرقام: ${invoiceNumbers} للمورد: ${supplier?.name}`,
      type: 'purchase-payment',
      amount: totalPayment
    };

    setEntryData(updatedEntryData);

    // عرض تفاصيل القيد للمستخدم قبل الحفظ
    const confirmMessage = `تأكيد إنشاء قيد سداد المشتريات:\n\n` +
      `الوصف: ${updatedEntryData.description}\n` +
      `المبلغ: ${totalPayment.toLocaleString('ar-EG')} ج.م\n\n` +
      `تفاصيل القيد:\n` +
      `مدين: ${suppliersAccount?.name || 'الموردين'} - ${totalPayment.toLocaleString('ar-EG')} ج.م\n` +
      `دائن: ${paymentAccount.name} - ${totalPayment.toLocaleString('ar-EG')} ج.م\n\n` +
      `هل تريد حفظ هذا القيد؟`;

    if (confirm(confirmMessage)) {
      // حفظ القيد تلقائياً
      setTimeout(async () => {
        await saveJournalEntryAuto(updatedEntryData, entryDetailsArray);
      }, 100);
    }
  };

  // إنشاء قيد تحصيل فاتورة مبيعات محسن
  const createSalesCollectionEntry = async () => {
    if (!entryData.customerId || entryData.selectedInvoices.length === 0) {
      alert('يرجى اختيار العميل والفواتير المراد تحصيلها');
      return;
    }

    if (!entryData.accountCode) {
      alert('يرجى إدخال رقم حساب التحصيل');
      return;
    }

    const validation = validateAccountCode(entryData.accountCode);
    if (!validation.valid) {
      alert('رقم الحساب المدخل غير صحيح');
      return;
    }

    const customer = customers.find(c => c.id === parseInt(entryData.customerId));
    const customersAccount = accounts.find(acc => acc.code === '121'); // العملاء
    const collectionAccount = validation.account;

    const totalCollection = entryData.selectedInvoices.reduce((sum, inv) => sum + parseFloat(inv.paymentAmount || 0), 0);

    const entryDetailsArray = [
      {
        accountId: collectionAccount.id,
        accountCode: collectionAccount.code,
        accountName: collectionAccount.name,
        debit: totalCollection,
        credit: 0,
        description: `تحصيل في حساب: ${collectionAccount.name}`
      },
      {
        accountId: customersAccount?.id || '',
        accountCode: customersAccount?.code || '121',
        accountName: customersAccount?.name || 'العملاء',
        debit: 0,
        credit: totalCollection,
        description: `تحصيل فواتير من العميل: ${customer?.name}`
      }
    ];

    setEntryDetails(entryDetailsArray);

    const invoiceNumbers = entryData.selectedInvoices.map(inv => inv.invoiceNumber).join(', ');
    const updatedEntryData = {
      ...entryData,
      description: `تحصيل فواتير مبيعات أرقام: ${invoiceNumbers} من العميل: ${customer?.name}`,
      type: 'sales-collection',
      amount: totalCollection
    };

    setEntryData(updatedEntryData);

    // عرض تفاصيل القيد للمستخدم قبل الحفظ
    const confirmMessage = `تأكيد إنشاء قيد تحصيل المبيعات:\n\n` +
      `الوصف: ${updatedEntryData.description}\n` +
      `المبلغ: ${totalCollection.toLocaleString('ar-EG')} ج.م\n\n` +
      `تفاصيل القيد:\n` +
      `مدين: ${collectionAccount.name} - ${totalCollection.toLocaleString('ar-EG')} ج.م\n` +
      `دائن: ${customersAccount?.name || 'العملاء'} - ${totalCollection.toLocaleString('ar-EG')} ج.م\n\n` +
      `هل تريد حفظ هذا القيد؟`;

    if (confirm(confirmMessage)) {
      // حفظ القيد تلقائياً
      setTimeout(async () => {
        await saveJournalEntryAuto(updatedEntryData, entryDetailsArray);
      }, 100);
    }
  };

  // إنشاء قيد مصاريف عقد محسن
  const createContractExpenseEntry = async () => {
    if (!entryData.contractId || !entryData.amount) {
      alert('يرجى اختيار العقد وإدخال المبلغ');
      return;
    }

    if (!entryData.accountCode) {
      alert('يرجى إدخال رقم حساب الدفع');
      return;
    }

    if (!entryData.description.trim()) {
      alert('يرجى إدخال وصف المصروف');
      return;
    }

    const validation = validateAccountCode(entryData.accountCode);
    if (!validation.valid) {
      alert('رقم الحساب المدخل غير صحيح');
      return;
    }

    const contract = contracts.find(c => c.id === parseInt(entryData.contractId));
    const contractExpensesAccount = accounts.find(acc => acc.code === '521'); // مصاريف العقود
    const paymentAccount = validation.account;

    const entryDetailsArray = [
      {
        accountId: contractExpensesAccount?.id || '',
        accountCode: contractExpensesAccount?.code || '521',
        accountName: contractExpensesAccount?.name || 'مصاريف العقود',
        debit: parseFloat(entryData.amount),
        credit: 0,
        description: `مصاريف العقد: ${contract?.name}`
      },
      {
        accountId: paymentAccount.id,
        accountCode: paymentAccount.code,
        accountName: paymentAccount.name,
        debit: 0,
        credit: parseFloat(entryData.amount),
        description: `دفع من حساب: ${paymentAccount.name}`
      }
    ];

    setEntryDetails(entryDetailsArray);

    const updatedEntryData = {
      ...entryData,
      description: `مصاريف العقد: ${contract?.name}`,
      type: 'contract-expense'
    };

    setEntryData(updatedEntryData);

    // عرض تفاصيل القيد للمستخدم قبل الحفظ
    const confirmMessage = `تأكيد إنشاء قيد مصاريف العقد:\n\n` +
      `الوصف: ${updatedEntryData.description}\n` +
      `المبلغ: ${parseFloat(entryData.amount).toLocaleString('ar-EG')} ج.م\n\n` +
      `تفاصيل القيد:\n` +
      `مدين: ${contractExpensesAccount?.name || 'مصاريف العقود'} - ${parseFloat(entryData.amount).toLocaleString('ar-EG')} ج.م\n` +
      `دائن: ${paymentAccount.name} - ${parseFloat(entryData.amount).toLocaleString('ar-EG')} ج.م\n\n` +
      `هل تريد حفظ هذا القيد؟`;

    if (confirm(confirmMessage)) {
      // حفظ القيد تلقائياً
      setTimeout(async () => {
        const journalEntryId = await saveJournalEntryAuto(updatedEntryData, entryDetailsArray);

        // إضافة المصروف إلى جدول مصروفات العقود
        if (journalEntryId) {
          await addContractExpenseFromJournal(contract, entryData, journalEntryId);
        }
      }, 100);
    }
  };

  // إضافة مصروف العقد إلى جدول مصروفات العقود
  const addContractExpenseFromJournal = async (contract, entryData, journalEntryId) => {
    try {
      // إنشاء رقم إذن صرف تلقائي
      const voucherCount = await db.contractExpenses.where('type').equals('journal').count();
      const voucherNumber = `JOU${String(voucherCount + 1).padStart(6, '0')}`;

      const contractExpense = {
        contractId: parseInt(entryData.contractId),
        type: 'journal', // نوع جديد للمصروفات من القيود اليومية
        voucherNumber: voucherNumber,
        description: entryData.description || `مصروف من القيود اليومية`,
        amount: parseFloat(entryData.amount),
        date: entryData.date,
        reference: `قيد يومي - ${journalEntryId}`,
        requestedBy: user.name || 'النظام',
        department: 'القيود اليومية',
        notes: `تم إنشاؤه تلقائياً من القيود اليومية`,
        status: 'posted', // مرحل مباشرة
        journalEntryId: journalEntryId, // ربط بالقيد اليومي
        userId: user.id,
        createdAt: new Date()
      };

      await db.contractExpenses.add(contractExpense);
      console.log('تم إضافة مصروف العقد:', contractExpense);

    } catch (error) {
      console.error('خطأ في إضافة مصروف العقد:', error);
      alert('تم حفظ القيد بنجاح ولكن حدث خطأ في إضافة المصروف لقسم العقود');
    }
  };

  // إنشاء قيد دفع أذون العمالة
  const createWorkerPaymentEntry = async () => {
    if (entryData.selectedVouchers.length === 0) {
      alert('يرجى اختيار أذون الصرف المراد سدادها');
      return;
    }

    if (!entryData.accountCode) {
      alert('يرجى إدخال رقم حساب الدفع');
      return;
    }

    const validation = validateAccountCode(entryData.accountCode);
    if (!validation.valid) {
      alert('رقم الحساب المدخل غير صحيح');
      return;
    }

    const laborExpensesAccount = accounts.find(acc => acc.code === '531'); // مصاريف العمالة
    const paymentAccount = validation.account;

    const totalPayment = entryData.selectedVouchers.reduce((sum, voucher) => sum + parseFloat(voucher.paymentAmount || 0), 0);

    const entryDetailsArray = [
      {
        accountId: laborExpensesAccount?.id || '',
        accountCode: laborExpensesAccount?.code || '531',
        accountName: laborExpensesAccount?.name || 'مصاريف العمالة',
        debit: totalPayment,
        credit: 0,
        description: `دفع أذون صرف العمالة`
      },
      {
        accountId: paymentAccount.id,
        accountCode: paymentAccount.code,
        accountName: paymentAccount.name,
        debit: 0,
        credit: totalPayment,
        description: `دفع من حساب: ${paymentAccount.name}`
      }
    ];

    setEntryDetails(entryDetailsArray);

    const voucherNumbers = entryData.selectedVouchers.map(voucher => voucher.voucherNumber).join(', ');
    const updatedEntryData = {
      ...entryData,
      description: `دفع أذون صرف أرقام: ${voucherNumbers}`,
      type: 'worker-payment',
      amount: totalPayment
    };

    setEntryData(updatedEntryData);

    // عرض تفاصيل القيد للمستخدم قبل الحفظ
    const confirmMessage = `تأكيد إنشاء قيد دفع العمالة:\n\n` +
      `الوصف: ${updatedEntryData.description}\n` +
      `المبلغ: ${totalPayment.toLocaleString('ar-EG')} ج.م\n\n` +
      `تفاصيل القيد:\n` +
      `مدين: ${laborExpensesAccount?.name || 'مصاريف العمالة'} - ${totalPayment.toLocaleString('ar-EG')} ج.م\n` +
      `دائن: ${paymentAccount.name} - ${totalPayment.toLocaleString('ar-EG')} ج.م\n\n` +
      `هل تريد حفظ هذا القيد؟`;

    if (confirm(confirmMessage)) {
      // حفظ القيد تلقائياً
      setTimeout(async () => {
        await saveJournalEntryAuto(updatedEntryData, entryDetailsArray);
      }, 100);
    }
  };

  // حفظ القيد كقالب
  const saveAsTemplate = async () => {
    if (!entryData.description.trim()) {
      alert('يرجى إدخال وصف للقالب');
      return;
    }

    try {
      const templateName = prompt('أدخل اسم القالب:');
      if (!templateName) return;

      const template = {
        name: templateName,
        description: entryData.description,
        type: entryData.type,
        entries: entryDetails.filter(detail => detail.accountId),
        createdAt: new Date(),
        userId: user.id
      };

      // إنشاء جدول القوالب إذا لم يكن موجوداً
      try {
        if (db.journalTemplates) {
          await db.journalTemplates.add(template);
        } else {
          console.log('جدول القوالب غير متوفر');
          alert('ميزة القوالب غير متوفرة حالياً');
          return;
        }
      } catch (templateError) {
        console.log('خطأ في الوصول لجدول القوالب:', templateError);
        alert('ميزة القوالب غير متوفرة حالياً');
        return;
      }
      alert('تم حفظ القالب بنجاح');
      await loadTemplates();
    } catch (error) {
      console.error('خطأ في حفظ القالب:', error);
      alert('حدث خطأ أثناء حفظ القالب');
    }
  };

  // تطبيق قالب
  const applyTemplate = async (template) => {
    setEntryData({
      ...entryData,
      description: template.description,
      type: template.type
    });

    const templatedEntries = await Promise.all(
      template.entries.map(async (entry) => {
        const account = await db.accounts.get(entry.accountId);
        return {
          ...entry,
          accountName: account ? account.name : 'حساب محذوف'
        };
      })
    );

    setEntryDetails(templatedEntries);
    setCurrentView('create');
  };

  const addEntryDetail = () => {
    setEntryDetails([
      ...entryDetails,
      { accountId: '', accountName: '', accountCode: '', debit: 0, credit: 0, description: '' }
    ]);
  };

  const removeEntryDetail = (index) => {
    if (entryDetails.length > 1) {
      setEntryDetails(entryDetails.filter((_, i) => i !== index));
    }
  };

  const updateEntryDetail = (index, field, value) => {
    const updated = [...entryDetails];
    updated[index][field] = value;

    // إذا تم تغيير الحساب، إضافة اسم الحساب
    if (field === 'accountId') {
      const account = accounts.find(acc => acc.id === parseInt(value));
      updated[index].accountName = account ? account.name : '';
      updated[index].accountCode = account ? account.code : '';
    }

    setEntryDetails(updated);
  };



  // فلترة القيود
  const getFilteredEntries = () => {
    return journalEntries.filter(entry => {
      const entryNumberMatch = !searchFilters.entryNumber ||
        entry.entryNumber.toLowerCase().includes(searchFilters.entryNumber.toLowerCase());

      const descriptionMatch = !searchFilters.description ||
        entry.description.toLowerCase().includes(searchFilters.description.toLowerCase());

      const typeMatch = !searchFilters.type || entry.type === searchFilters.type;

      const dateFromMatch = !searchFilters.dateFrom ||
        new Date(entry.date) >= new Date(searchFilters.dateFrom);

      const dateToMatch = !searchFilters.dateTo ||
        new Date(entry.date) <= new Date(searchFilters.dateTo);

      const accountMatch = !searchFilters.accountId ||
        entry.entries.some(detail => detail.accountId === parseInt(searchFilters.accountId));

      return entryNumberMatch && descriptionMatch && typeMatch && dateFromMatch && dateToMatch && accountMatch;
    });
  };

  // اقتراحات الحسابات حسب نوع القيد
  const getSuggestedAccounts = (entryType) => {
    const suggestions = {
      'purchase-payment': accounts.filter(acc => ['111', '112', '211'].includes(acc.code)),
      'sales-collection': accounts.filter(acc => ['111', '112', '121'].includes(acc.code)),
      'contract-expense': accounts.filter(acc => ['111', '521', '522'].includes(acc.code)),
      'worker-payment': accounts.filter(acc => ['111', '531'].includes(acc.code)),
      'manual': accounts
    };

    return suggestions[entryType] || accounts;
  };



  // تحديث حالة الفواتير بعد السداد/التحصيل
  const updateInvoicePaymentStatus = async (invoices, type) => {
    try {
      for (const invoice of invoices) {
        const currentPaidAmount = invoice.paidAmount || 0;
        const newPaidAmount = currentPaidAmount + parseFloat(invoice.paymentAmount || 0);
        // التعامل مع أسماء الحقول المختلفة
        const totalAmount = invoice.total || invoice.totalAmount || 0;
        const remainingAmount = totalAmount - newPaidAmount;

        const paymentStatus = remainingAmount <= 0 ? 'paid' : 'partial';

        const updateData = {
          paidAmount: newPaidAmount,
          paymentStatus: paymentStatus,
          lastPaymentDate: new Date()
        };

        if (type === 'purchase') {
          await db.purchaseInvoices.update(invoice.id, updateData);
        } else if (type === 'sales') {
          await db.salesInvoices.update(invoice.id, updateData);
        }
      }
    } catch (error) {
      console.error('خطأ في تحديث حالة الفواتير:', error);
    }
  };

  // تحديث حالة أذون الصرف بعد السداد
  const updateVoucherPaymentStatus = async (vouchers) => {
    try {
      for (const voucher of vouchers) {
        const currentPaidAmount = voucher.paidAmount || 0;
        const paymentAmount = parseFloat(voucher.paymentAmount || 0);
        const newPaidAmount = currentPaidAmount + paymentAmount;
        const totalAmount = voucher.totalAmount || voucher.amount || 0;
        const remainingAmount = totalAmount - newPaidAmount;

        // تحديد حالة الدفع والإذن
        let newStatus = voucher.status;
        let paymentStatus = 'partial';

        if (remainingAmount <= 0) {
          newStatus = 'paid';
          paymentStatus = 'paid';
        }

        const updateData = {
          paidAmount: newPaidAmount,
          paymentStatus: paymentStatus,
          status: newStatus,
          lastPaymentDate: new Date(),
          updatedAt: new Date()
        };

        console.log(`تحديث إذن ${voucher.id}:`, updateData);

        // تحديث الإذن في قاعدة البيانات
        await db.workerVouchers.update(voucher.id, updateData);

        // إذا تم السداد بالكامل، تحديث حالة التكاليف المرتبطة
        if (newStatus === 'paid') {
          await updateLinkedCostsPaymentStatus(voucher.id);
        }
      }
    } catch (error) {
      console.error('خطأ في تحديث حالة أذون الصرف:', error);
    }
  };

  // دالة لتحديث حالة الدفع للتكاليف المرتبطة بالإذن
  const updateLinkedCostsPaymentStatus = async (voucherId) => {
    try {
      // تحديث workSessions
      const workSessions = await db.workSessions.toArray();
      const linkedWorkSessions = workSessions.filter(s => s.voucherId === voucherId);

      for (const session of linkedWorkSessions) {
        await db.workSessions.update(session.id, {
          isPaid: true,
          paidAt: new Date()
        });
      }

      // تحديث laborCosts
      const laborCosts = await db.laborCosts.toArray();
      const linkedLaborCosts = laborCosts.filter(c => c.voucherId === voucherId);

      for (const cost of linkedLaborCosts) {
        await db.laborCosts.update(cost.id, {
          isPaid: true,
          paidAt: new Date()
        });
      }

      // تحديث contractExpenses
      const contractExpenses = await db.contractExpenses.toArray();

      for (const expense of contractExpenses) {
        if (expense.items && Array.isArray(expense.items)) {
          let hasUpdates = false;
          const updatedItems = expense.items.map(item => {
            if (item.type === 'labor' && item.voucherId === voucherId) {
              hasUpdates = true;
              return {
                ...item,
                isPaid: true,
                paidAt: new Date()
              };
            }
            return item;
          });

          if (hasUpdates) {
            await db.contractExpenses.update(expense.id, {
              items: updatedItems
            });
          }
        }
      }

      console.log(`تم تحديث حالة الدفع للتكاليف المرتبطة بالإذن ${voucherId}`);
    } catch (error) {
      console.error('خطأ في تحديث حالة التكاليف المرتبطة:', error);
    }
  };

  // حفظ القيد تلقائياً
  const saveJournalEntryAuto = async (entryDataToSave, entryDetailsToSave) => {
    try {
      // فحص الفترة المحاسبية (إذا كان الجدول موجوداً)
      try {
        const entryYear = new Date(entryDataToSave.date).getFullYear();
        if (db.accountingPeriods) {
          const period = await db.accountingPeriods.where('year').equals(entryYear).first();

          if (period && period.status === 'closed') {
            alert(`لا يمكن إضافة قيود في سنة ${entryYear} - الفترة المحاسبية مقفلة`);
            return;
          }
        }
      } catch (error) {
        console.log('جدول الفترات المحاسبية غير متوفر، سيتم تجاهل فحص الفترة');
      }

      // التحقق من توازن القيد
      const totalDebit = entryDetailsToSave.reduce((sum, detail) => sum + (parseFloat(detail.debit) || 0), 0);
      const totalCredit = entryDetailsToSave.reduce((sum, detail) => sum + (parseFloat(detail.credit) || 0), 0);

      console.log('فحص توازن القيد:', { totalDebit, totalCredit, difference: Math.abs(totalDebit - totalCredit) });

      if (Math.abs(totalDebit - totalCredit) > 0.01) {
        alert(`خطأ: القيد غير متوازن!\nإجمالي المدين: ${totalDebit.toLocaleString('ar-EG')} ج.م\nإجمالي الدائن: ${totalCredit.toLocaleString('ar-EG')} ج.م\nالفرق: ${Math.abs(totalDebit - totalCredit).toLocaleString('ar-EG')} ج.م`);
        return;
      }

      if (totalDebit === 0 || totalCredit === 0) {
        alert('خطأ: القيد يجب أن يحتوي على مبالغ مدينة ودائنة');
        return;
      }

      const entryNumber = await generateEntryNumber();

      // إنشاء رقم مرجعي تلقائي
      const referenceNumber = await generateReferenceNumber(entryDataToSave.type);

      // تحضير بنود القيد مع التأكد من وجود accountId
      const processedEntries = entryDetailsToSave
        .filter(detail => (detail.debit > 0 || detail.credit > 0))
        .map(detail => {
          // إذا لم يكن هناك accountId، البحث عنه باستخدام accountCode
          let accountId = detail.accountId;
          if (!accountId && detail.accountCode) {
            const account = accounts.find(acc => acc.code === detail.accountCode);
            accountId = account ? account.id : null;
          }

          return {
            accountId: accountId,
            accountCode: detail.accountCode,
            accountName: detail.accountName,
            description: detail.description,
            debit: parseFloat(detail.debit) || 0,
            credit: parseFloat(detail.credit) || 0
          };
        })
        .filter(detail => detail.accountId); // فلترة البنود التي لها accountId صحيح

      console.log('بنود القيد المعالجة:', processedEntries);

      const journalEntry = {
        entryNumber,
        date: new Date(entryDataToSave.date),
        description: entryDataToSave.description,
        reference: entryDataToSave.reference || referenceNumber,
        type: entryDataToSave.type,
        status: 'draft', // مسودة، مراجعة، مرحل
        entries: processedEntries,
        totalDebit,
        totalCredit,
        relatedInvoices: entryDataToSave.selectedInvoices || [],
        relatedVouchers: entryDataToSave.selectedVouchers || [],
        userId: user.id,
        reviewedBy: null,
        reviewedAt: null,
        postedBy: null,
        postedAt: null,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const journalEntryId = await db.journalEntries.add(journalEntry);

      // حفظ تفاصيل القيد في جدول منفصل لمتابعة الحسابات
      for (const entry of processedEntries) {
        await db.journalEntryDetails.add({
          entryId: journalEntryId,
          accountId: entry.accountId,
          description: entry.description,
          debit: entry.debit,
          credit: entry.credit
        });
      }

      // تحديث حالة الفواتير وأذون الصرف
      if (entryDataToSave.type === 'purchase-payment' && entryDataToSave.selectedInvoices?.length > 0) {
        await updateInvoicePaymentStatus(entryDataToSave.selectedInvoices, 'purchase');
      } else if (entryDataToSave.type === 'sales-collection' && entryDataToSave.selectedInvoices?.length > 0) {
        await updateInvoicePaymentStatus(entryDataToSave.selectedInvoices, 'sales');
      } else if (entryDataToSave.type === 'worker-payment' && entryDataToSave.selectedVouchers?.length > 0) {
        await updateVoucherPaymentStatus(entryDataToSave.selectedVouchers);
      }

      // تحديث أرصدة الحسابات
      await updateAccountBalances(journalEntry.entries);

      alert('تم إنشاء وحفظ القيد بنجاح');

      // إعادة تحميل البيانات
      await loadJournalEntries();
      await loadPurchaseInvoices();
      await loadSalesInvoices();
      await loadWorkerVouchers();

      // إعادة تعيين النموذج
      resetForm();

      return journalEntryId;

    } catch (error) {
      console.error('خطأ في حفظ القيد:', error);
      alert('حدث خطأ أثناء حفظ القيد');
      return null;
    }
  };

  // حفظ القيد اليدوي المحسن
  const saveManualJournalEntry = async () => {
    try {
      if (!entryData.description.trim()) {
        alert('يرجى إدخال وصف القيد');
        return;
      }

      if (entryDetails.length === 0) {
        alert('يرجى إضافة بنود للقيد');
        return;
      }

      if (!isBalanced()) {
        alert('القيد غير متوازن - يجب أن يكون إجمالي المدين = إجمالي الدائن');
        return;
      }

      // إنشاء رقم قيد إذا لم يكن موجوداً
      if (!entryData.entryNumber) {
        const newEntryNumber = await generateEntryNumber();
        setEntryData(prev => ({ ...prev, entryNumber: newEntryNumber }));
      }

      const journalEntry = {
        entryNumber: entryData.entryNumber,
        date: entryData.date,
        description: entryData.description,
        reference: entryData.reference || '',
        type: entryData.type || 'manual',
        entries: entryDetails.map(entry => {
          // البحث عن الحساب للحصول على accountId
          const account = accounts.find(acc => acc.code === entry.accountCode);
          return {
            accountId: account ? account.id : null,
            accountCode: entry.accountCode,
            accountName: entry.accountName,
            description: entry.description,
            debit: entry.debit || 0,
            credit: entry.credit || 0
          };
        }),
        totalDebit: getTotalDebit(),
        totalCredit: getTotalCredit(),
        status: 'posted',
        userId: user.id,
        createdAt: new Date()
      };

      console.log('حفظ القيد:', journalEntry);
      const journalEntryId = await db.journalEntries.add(journalEntry);

      // حفظ تفاصيل القيد في جدول منفصل لمتابعة الحسابات
      for (const entry of journalEntry.entries) {
        if (entry.accountId) {
          await db.journalEntryDetails.add({
            entryId: journalEntryId,
            accountId: entry.accountId,
            description: entry.description,
            debit: entry.debit || 0,
            credit: entry.credit || 0
          });
        }
      }

      alert('تم حفظ القيد بنجاح');
      resetManualEntryForm();
      await loadJournalEntries();
      setCurrentView('list');
    } catch (error) {
      console.error('خطأ في حفظ القيد:', error);
      alert('حدث خطأ أثناء حفظ القيد');
    }
  };

  const saveJournalEntry = async () => {
    const totalDebit = getTotalDebit();
    const totalCredit = getTotalCredit();

    if (!isBalanced()) {
      alert(`القيد غير متوازن!\nإجمالي المدين: ${totalDebit.toLocaleString('ar-EG')} ج.م\nإجمالي الدائن: ${totalCredit.toLocaleString('ar-EG')} ج.م\nالفرق: ${Math.abs(totalDebit - totalCredit).toLocaleString('ar-EG')} ج.م\n\nيجب أن يكون إجمالي المدين مساوياً لإجمالي الدائن`);
      return;
    }

    if (!entryData.description.trim()) {
      alert('يرجى إدخال وصف للقيد');
      return;
    }

    if (entryDetails.length === 0 || entryDetails.every(detail => (!detail.accountId && !detail.accountCode))) {
      alert('يرجى إضافة بنود للقيد');
      return;
    }

    // التحقق من صحة الحسابات
    const invalidEntries = entryDetails.filter(detail =>
      (detail.debit > 0 || detail.credit > 0) && !detail.accountCode && !detail.accountId
    );

    if (invalidEntries.length > 0) {
      alert('يوجد بنود بدون حسابات صحيحة. يرجى التأكد من جميع البنود.');
      return;
    }

    // تأكيد قبل الحفظ/التحديث
    const confirmMessage = editingEntry ?
      `هل تريد تحديث القيد ${editingEntry.entryNumber}؟` :
      'هل تريد حفظ هذا القيد؟';

    if (!confirm(confirmMessage)) {
      return;
    }

    try {
      setLoading(true);

      const entryNumber = entryData.entryNumber || await generateEntryNumber();

      // تحضير بنود القيد مع التأكد من وجود accountId
      const processedEntries = entryDetails
        .filter(detail => (detail.debit > 0 || detail.credit > 0))
        .map(detail => {
          // إذا لم يكن هناك accountId، البحث عنه باستخدام accountCode
          let accountId = detail.accountId;
          if (!accountId && detail.accountCode) {
            const account = accounts.find(acc => acc.code === detail.accountCode);
            accountId = account ? account.id : null;
          }

          return {
            accountId: accountId,
            accountCode: detail.accountCode,
            accountName: detail.accountName,
            description: detail.description,
            debit: parseFloat(detail.debit) || 0,
            credit: parseFloat(detail.credit) || 0
          };
        });

      const journalEntry = {
        entryNumber,
        date: new Date(entryData.date),
        description: entryData.description,
        reference: entryData.reference || '',
        type: entryData.type,
        entries: processedEntries,
        totalDebit: getTotalDebit(),
        totalCredit: getTotalCredit(),
        relatedInvoices: entryData.selectedInvoices || [],
        relatedVouchers: entryData.selectedVouchers || [],
        status: editingEntry && editingEntry.status === 'posted' ? 'posted' : 'draft', // إعادة تعيين الحالة للمسودة عند التعديل (إلا إذا كان مرحل)
        userId: user.id,
        createdAt: editingEntry ? editingEntry.createdAt : new Date(),
        updatedAt: new Date()
      };

      if (editingEntry) {
        // إضافة معلومات التعديل
        journalEntry.editedBy = user.id;
        journalEntry.editedAt = new Date();

        await db.journalEntries.update(editingEntry.id, journalEntry);

        // حذف التفاصيل القديمة وإضافة الجديدة
        await db.journalEntryDetails.where('entryId').equals(editingEntry.id).delete();
        for (const entry of processedEntries) {
          if (entry.accountId) {
            await db.journalEntryDetails.add({
              entryId: editingEntry.id,
              accountId: entry.accountId,
              description: entry.description,
              debit: entry.debit || 0,
              credit: entry.credit || 0
            });
          }
        }

        alert(`تم تحديث القيد ${entryNumber} بنجاح`);
      } else {
        const journalEntryId = await db.journalEntries.add(journalEntry);

        // حفظ تفاصيل القيد في جدول منفصل لمتابعة الحسابات
        for (const entry of processedEntries) {
          if (entry.accountId) {
            await db.journalEntryDetails.add({
              entryId: journalEntryId,
              accountId: entry.accountId,
              description: entry.description,
              debit: entry.debit || 0,
              credit: entry.credit || 0
            });
          }
        }

        alert(`تم حفظ القيد ${entryNumber} بنجاح`);
      }

      // تحديث حالة الفواتير وأذون الصرف
      if (entryData.type === 'purchase-payment' && entryData.selectedInvoices.length > 0) {
        await updateInvoicePaymentStatus(entryData.selectedInvoices, 'purchase');
      } else if (entryData.type === 'sales-collection' && entryData.selectedInvoices.length > 0) {
        await updateInvoicePaymentStatus(entryData.selectedInvoices, 'sales');
      } else if (entryData.type === 'worker-payment' && entryData.selectedVouchers.length > 0) {
        await updateVoucherPaymentStatus(entryData.selectedVouchers);
      }

      // تحديث أرصدة الحسابات
      await updateAccountBalances(journalEntry.entries);

      resetForm();
      setCurrentView('list');
      await loadJournalEntries();
      await loadPurchaseInvoices();
      await loadSalesInvoices();
      await loadWorkerVouchers();

    } catch (error) {
      console.error('خطأ في حفظ القيد:', error);
      alert('حدث خطأ أثناء حفظ القيد');
    } finally {
      setLoading(false);
    }
  };

  const updateAccountBalances = async (entries) => {
    try {
      for (const entry of entries) {
        const account = await db.accounts.get(entry.accountId);
        if (account) {
          const debitAmount = parseFloat(entry.debit) || 0;
          const creditAmount = parseFloat(entry.credit) || 0;
          
          // حساب الرصيد الجديد حسب نوع الحساب
          let newBalance = account.balance || 0;
          
          if (['أصول', 'مصروفات'].includes(account.type)) {
            // الأصول والمصروفات تزيد بالمدين وتقل بالدائن
            newBalance += debitAmount - creditAmount;
          } else {
            // الالتزامات وحقوق الملكية والإيرادات تزيد بالدائن وتقل بالمدين
            newBalance += creditAmount - debitAmount;
          }
          
          await db.accounts.update(entry.accountId, { balance: newBalance });
        }
      }
    } catch (error) {
      console.error('خطأ في تحديث أرصدة الحسابات:', error);
    }
  };

  const editJournalEntry = (entry) => {
    // التحقق من إمكانية التعديل
    if (entry.status === 'posted') {
      // التحقق من صلاحية تعديل القيود المرحلة
      if (!user.permissions?.editPostedEntries && user.role !== 'super-admin') {
        alert('لا يمكن تعديل القيود المرحلة. تحتاج إلى صلاحيات خاصة.');
        return;
      }

      if (!confirm('هذا القيد مرحل. هل تريد تعديله؟ (يتطلب صلاحيات خاصة)')) {
        return;
      }
    }

    setEditingEntry(entry);
    setEntryData({
      entryNumber: entry.entryNumber,
      date: entry.date instanceof Date ? entry.date.toISOString().split('T')[0] : new Date(entry.date).toISOString().split('T')[0],
      description: entry.description,
      reference: entry.reference || '',
      type: entry.type || 'manual',
      contractId: entry.contractId || '',
      customerId: entry.customerId || '',
      supplierId: entry.supplierId || '',
      amount: entry.amount || 0,
      paymentMethod: entry.paymentMethod || 'cash',
      accountCode: entry.accountCode || '',
      accountName: entry.accountName || '',
      selectedInvoices: entry.relatedInvoices || [],
      selectedVouchers: entry.relatedVouchers || []
    });

    // تحويل بيانات البنود للتعديل
    const editableEntries = entry.entries.map(entryDetail => ({
      accountId: entryDetail.accountId || '',
      accountCode: entryDetail.accountCode || '',
      accountName: entryDetail.accountName || '',
      description: entryDetail.description || '',
      debit: parseFloat(entryDetail.debit) || 0,
      credit: parseFloat(entryDetail.credit) || 0
    }));

    setEntryDetails(editableEntries);
    setCurrentView('create');

    // إشعار المستخدم بوضع التعديل
    console.log('تم تحميل القيد للتعديل:', entry);
  };

  const deleteJournalEntry = async (entryId) => {
    try {
      const entry = await db.journalEntries.get(entryId);
      if (!entry) {
        alert('القيد غير موجود');
        return;
      }

      // التحقق من صلاحية حذف القيود المرحلة
      if (entry.status === 'posted') {
        if (!user.permissions?.editAllData && user.role !== 'super-admin') {
          alert('لا يمكن حذف القيود المرحلة. تحتاج إلى صلاحيات خاصة.');
          return;
        }

        if (!confirm('هذا القيد مرحل. هل تريد حذفه؟ (يتطلب صلاحيات خاصة)')) {
          return;
        }
      } else {
        if (!confirm('هل أنت متأكد من حذف هذا القيد؟')) return;
      }

      // حذف تفاصيل القيد أولاً
      await db.journalEntryDetails.where('entryId').equals(entryId).delete();

      // ثم حذف القيد نفسه
      await db.journalEntries.delete(entryId);

      alert('تم حذف القيد بنجاح');
      await loadJournalEntries();
    } catch (error) {
      console.error('خطأ في حذف القيد:', error);
      alert('حدث خطأ أثناء حذف القيد');
    }
  };

  // مراجعة القيد
  const reviewJournalEntry = async (entryId) => {
    if (!confirm('هل تريد مراجعة هذا القيد؟')) return;

    try {
      await db.journalEntries.update(entryId, {
        status: 'reviewed',
        reviewedBy: user.id,
        reviewedAt: new Date(),
        updatedAt: new Date()
      });
      alert('تم مراجعة القيد بنجاح');
      await loadJournalEntries();
    } catch (error) {
      console.error('خطأ في مراجعة القيد:', error);
      alert('حدث خطأ أثناء مراجعة القيد');
    }
  };

  // ترحيل القيد
  const postJournalEntry = async (entryId) => {
    if (!confirm('هل تريد ترحيل هذا القيد؟ لن يمكن تعديله بعد الترحيل.')) return;

    try {
      const entry = await db.journalEntries.get(entryId);
      if (!entry) {
        alert('القيد غير موجود');
        return;
      }

      if (entry.status !== 'reviewed') {
        alert('يجب مراجعة القيد أولاً قبل الترحيل');
        return;
      }

      // تحديث أرصدة الحسابات
      await updateAccountBalances(entry.entries);

      // التأكد من وجود تفاصيل القيد في جدول منفصل
      const existingDetails = await db.journalEntryDetails.where('entryId').equals(entryId).count();
      if (existingDetails === 0) {
        // إضافة تفاصيل القيد إذا لم تكن موجودة
        for (const entryDetail of entry.entries) {
          if (entryDetail.accountId) {
            await db.journalEntryDetails.add({
              entryId: entryId,
              accountId: entryDetail.accountId,
              description: entryDetail.description,
              debit: entryDetail.debit || 0,
              credit: entryDetail.credit || 0
            });
          }
        }
      }

      await db.journalEntries.update(entryId, {
        status: 'posted',
        postedBy: user.id,
        postedAt: new Date(),
        updatedAt: new Date()
      });

      alert('تم ترحيل القيد بنجاح');
      await loadJournalEntries();
    } catch (error) {
      console.error('خطأ في ترحيل القيد:', error);
      alert('حدث خطأ أثناء ترحيل القيد');
    }
  };

  // إلغاء ترحيل القيد
  const unpostJournalEntry = async (entryId) => {
    try {
      const entry = await db.journalEntries.get(entryId);
      if (!entry) {
        alert('القيد غير موجود');
        return;
      }

      // التحقق من صلاحية إلغاء الترحيل
      if (!user.permissions?.editPostedEntries && user.role !== 'super-admin') {
        alert('لا يمكن إلغاء ترحيل القيود. تحتاج إلى صلاحيات خاصة.');
        return;
      }

      if (entry.status !== 'posted') {
        alert('هذا القيد غير مرحل');
        return;
      }

      if (!confirm('هل تريد إلغاء ترحيل هذا القيد؟ سيتم إعادته إلى حالة المراجعة.')) {
        return;
      }

      // إعادة تحديث أرصدة الحسابات (عكس العملية)
      const reversedEntries = entry.entries.map(entryDetail => ({
        ...entryDetail,
        debit: entryDetail.credit || 0,  // عكس المدين والدائن
        credit: entryDetail.debit || 0
      }));

      await updateAccountBalances(reversedEntries);

      // تحديث حالة القيد إلى مراجع
      await db.journalEntries.update(entryId, {
        status: 'reviewed',
        unpostedBy: user.id,
        unpostedAt: new Date(),
        updatedAt: new Date()
      });

      alert('تم إلغاء ترحيل القيد بنجاح');
      await loadJournalEntries();
    } catch (error) {
      console.error('خطأ في إلغاء ترحيل القيد:', error);
      alert('حدث خطأ أثناء إلغاء ترحيل القيد');
    }
  };

  const resetForm = () => {
    setEntryData({
      entryNumber: '',
      date: new Date().toISOString().split('T')[0],
      description: '',
      reference: '',
      type: 'manual',
      contractId: '',
      customerId: '',
      supplierId: '',
      amount: 0,
      paymentMethod: 'cash',
      accountCode: '',
      accountName: '',
      selectedInvoices: [],
      selectedVouchers: []
    });
    setEntryDetails([
      { accountId: '', accountName: '', accountCode: '', debit: 0, credit: 0, description: '' }
    ]);
    setEditingEntry(null);
  };

  // إلغاء التعديل
  const cancelEdit = () => {
    if (editingEntry) {
      const hasChanges =
        entryData.description !== editingEntry.description ||
        entryData.reference !== (editingEntry.reference || '') ||
        JSON.stringify(entryDetails) !== JSON.stringify(editingEntry.entries);

      if (hasChanges) {
        if (!confirm('لديك تغييرات غير محفوظة. هل تريد إلغاء التعديل والعودة للقائمة؟')) {
          return;
        }
      }
    }

    resetForm();
    setCurrentView('list');
  };

  const printJournalEntry = (entry) => {
    const printContent = generateJournalEntryPrintContent(entry);
    
    const printWindow = window.open('', '_blank');
    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.focus();
    
    setTimeout(() => {
      printWindow.print();
    }, 250);
  };

  const generateJournalEntryPrintContent = (entry) => {
    const entryRows = entry.entries.map(detail => `
      <tr>
        <td>${detail.accountName}</td>
        <td>${detail.description || '-'}</td>
        <td style="text-align: center;">${(detail.debit || 0) > 0 ? (detail.debit || 0).toLocaleString('ar-EG') : '-'}</td>
        <td style="text-align: center;">${(detail.credit || 0) > 0 ? (detail.credit || 0).toLocaleString('ar-EG') : '-'}</td>
      </tr>
    `).join('');

    return `
      <!DOCTYPE html>
      <html dir="rtl">
      <head>
        <meta charset="UTF-8">
        <title>قيد يومية - ${entry.entryNumber}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
          .header h1 { color: #333; margin-bottom: 10px; }
          .entry-info { display: flex; justify-content: space-between; margin-bottom: 20px; }
          .entry-info div { background: #f8f9fa; padding: 10px; border-radius: 5px; }
          .entries-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
          .entries-table th, .entries-table td { border: 1px solid #ddd; padding: 8px; text-align: right; }
          .entries-table th { background-color: #f8f9fa; font-weight: bold; }
          .totals { margin-top: 20px; text-align: center; font-weight: bold; }
          @media print { body { margin: 0; } }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>قيد يومية</h1>
          <p>رقم القيد: ${entry.entryNumber}</p>
        </div>
        
        <div class="entry-info">
          <div>
            <strong>التاريخ:</strong> ${new Date(entry.date).toLocaleDateString('ar-EG')}<br>
            <strong>المرجع:</strong> ${entry.reference || '-'}
          </div>
          <div>
            <strong>النوع:</strong> ${entry.type === 'manual' ? 'يدوي' : entry.type}<br>
            <strong>تاريخ الإنشاء:</strong> ${new Date(entry.createdAt).toLocaleDateString('ar-EG')}
          </div>
        </div>

        <div style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 5px;">
          <strong>الوصف:</strong> ${entry.description}
        </div>

        <table class="entries-table">
          <thead>
            <tr>
              <th>الحساب</th>
              <th>البيان</th>
              <th>مدين</th>
              <th>دائن</th>
            </tr>
          </thead>
          <tbody>
            ${entryRows}
          </tbody>
        </table>

        <div class="totals">
          <div>إجمالي المدين: ${(entry.totalDebit || 0).toLocaleString('ar-EG')} ج.م</div>
          <div>إجمالي الدائن: ${(entry.totalCredit || 0).toLocaleString('ar-EG')} ج.م</div>
        </div>
      </body>
      </html>
    `;
  };

  if (loading) {
    return (
      <div style={{ padding: '2rem', textAlign: 'center' }}>
        <div className="spinner"></div>
        <div>جاري التحميل...</div>
      </div>
    );
  }

  return (
    <div className="container">
      {/* شريط التنقل */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div className="card-title">
          {currentView === 'create' ?
            (editingEntry ? `تعديل القيد - ${editingEntry.entryNumber}` : 'إنشاء قيد يومية جديد') :
            currentView === 'templates' ? 'قوالب القيود' :
            'القيود اليومية'
          }
        </div>
        <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>
          {currentView === 'create' ? (
            <>
              <button
                className="btn btn-secondary"
                onClick={cancelEdit}
              >
                ← {editingEntry ? 'إلغاء التعديل' : 'العودة للقائمة'}
              </button>
              <button
                className="btn btn-info"
                onClick={saveAsTemplate}
                disabled={entryDetails.length === 0}
              >
                💾 حفظ كقالب
              </button>
              <button
                className="btn btn-success"
                onClick={saveJournalEntry}
                disabled={!isBalanced() || loading}
              >
                {loading ? 'جاري الحفظ...' : (editingEntry ? 'تحديث القيد' : 'حفظ القيد')}
              </button>
            </>
          ) : currentView === 'templates' ? (
            <button
              className="btn btn-secondary"
              onClick={() => setCurrentView('list')}
            >
              ← العودة للقائمة
            </button>
          ) : (
            <>
              <button
                className="btn btn-primary"
                onClick={() => {
                  resetForm();
                  setCurrentView('create');
                }}
              >
                ➕ إنشاء قيد جديد
              </button>
              <button
                className="btn btn-info"
                onClick={() => setCurrentView('templates')}
              >
                📋 القوالب
              </button>

              <button
                className="btn btn-info"
                onClick={() => {
                  console.log('الحسابات المحملة حالياً:', accounts);
                  alert(`عدد الحسابات المحملة: ${accounts.length}\nأرقام الحسابات: ${accounts.map(acc => acc.code).join(', ')}`);
                }}
              >
                🔍 عرض الحسابات
              </button>
            </>
          )}
        </div>
      </div>

      {/* نموذج إنشاء/تعديل القيد */}
      {currentView === 'create' && (
        <div>
          {/* تنبيه وضع التعديل */}
          {editingEntry && (
            <div className="alert alert-info" style={{ marginBottom: '2rem' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                <div style={{ fontSize: '1.5rem' }}>✏️</div>
                <div>
                  <strong>وضع التعديل</strong>
                  <div style={{ fontSize: '0.9rem', marginTop: '0.25rem' }}>
                    تقوم بتعديل القيد رقم: <strong>{editingEntry.entryNumber}</strong>
                    <br />
                    تاريخ الإنشاء: {new Date(editingEntry.createdAt).toLocaleDateString('ar-EG')}
                    {editingEntry.editedAt && (
                      <span> | آخر تعديل: {new Date(editingEntry.editedAt).toLocaleDateString('ar-EG')}</span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* اختيار نوع القيد */}
          <div className="card" style={{ marginBottom: '2rem' }}>
            <div className="card-title">
              نوع القيد
              {editingEntry && (
                <span style={{ fontSize: '0.8rem', color: '#666', marginRight: '1rem' }}>
                  (لا يمكن تغيير نوع القيد عند التعديل)
                </span>
              )}
            </div>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem' }}>
              <button
                className={`btn ${entryData.type === 'manual' ? 'btn-primary' : 'btn-secondary'}`}
                onClick={() => !editingEntry && setEntryData({...entryData, type: 'manual'})}
                disabled={editingEntry}
                style={{ opacity: editingEntry ? 0.6 : 1 }}
              >
                📝 قيد يدوي
              </button>
              <button
                className={`btn ${entryData.type === 'purchase-payment' ? 'btn-primary' : 'btn-secondary'}`}
                onClick={() => !editingEntry && setEntryData({...entryData, type: 'purchase-payment'})}
                disabled={editingEntry}
                style={{ opacity: editingEntry ? 0.6 : 1 }}
              >
                💳 سداد مشتريات
              </button>
              <button
                className={`btn ${entryData.type === 'sales-collection' ? 'btn-primary' : 'btn-secondary'}`}
                onClick={() => !editingEntry && setEntryData({...entryData, type: 'sales-collection'})}
                disabled={editingEntry}
                style={{ opacity: editingEntry ? 0.6 : 1 }}
              >
                💰 تحصيل مبيعات
              </button>
              <button
                className={`btn ${entryData.type === 'contract-expense' ? 'btn-primary' : 'btn-secondary'}`}
                onClick={() => !editingEntry && setEntryData({...entryData, type: 'contract-expense'})}
                disabled={editingEntry}
                style={{ opacity: editingEntry ? 0.6 : 1 }}
              >
                🏗️ مصاريف عقد
              </button>
              <button
                className={`btn ${entryData.type === 'worker-payment' ? 'btn-primary' : 'btn-secondary'}`}
                onClick={() => !editingEntry && setEntryData({...entryData, type: 'worker-payment'})}
                disabled={editingEntry}
                style={{ opacity: editingEntry ? 0.6 : 1 }}
              >
                👷 دفع عمالة
              </button>
            </div>
          </div>

          {/* نموذج سداد المشتريات */}
          {entryData.type === 'purchase-payment' && (
            <div className="card" style={{ marginBottom: '2rem' }}>
              <div className="card-title">سداد فواتير المشتريات</div>

              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem', marginBottom: '1rem' }}>
                <div className="form-group">
                  <label className="form-label">المورد</label>
                  <select
                    className="form-control"
                    value={entryData.supplierId}
                    onChange={(e) => setEntryData({...entryData, supplierId: e.target.value, selectedInvoices: []})}
                  >
                    <option value="">اختر المورد</option>
                    {suppliers.map(supplier => (
                      <option key={supplier.id} value={supplier.id}>
                        {supplier.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label className="form-label">رقم حساب الدفع</label>
                  <input
                    type="text"
                    className="form-control"
                    value={entryData.accountCode}
                    onChange={(e) => {
                      const validation = validateAccountCode(e.target.value);
                      setEntryData({...entryData, accountCode: e.target.value, accountName: validation.account?.name || ''});
                    }}
                    placeholder="أدخل رقم الحساب (مثل: 111)"
                    list="payment-accounts-list"
                  />
                  <datalist id="payment-accounts-list">
                    {accounts.map(account => (
                      <option key={account.id} value={account.code}>
                        {account.code} - {account.name}
                      </option>
                    ))}
                  </datalist>
                  {entryData.accountCode && entryData.accountName && (
                    <small style={{ color: '#28a745' }}>✓ {entryData.accountName}</small>
                  )}
                  {entryData.accountCode && !entryData.accountName && (
                    <small style={{ color: '#dc3545' }}>✗ رقم الحساب غير صحيح</small>
                  )}
                </div>
              </div>

              {entryData.supplierId && (
                <div>
                  <h6>الفواتير غير المسددة:</h6>
                  <div style={{ maxHeight: '300px', overflow: 'auto' }}>
                    <table className="table table-sm">
                      <thead>
                        <tr>
                          <th>اختيار</th>
                          <th>رقم الفاتورة</th>
                          <th>التاريخ</th>
                          <th>المبلغ الإجمالي</th>
                          <th>المسدد</th>
                          <th>المتبقي</th>
                          <th>مبلغ السداد</th>
                        </tr>
                      </thead>
                      <tbody>
                        {getUnpaidPurchaseInvoices(entryData.supplierId).map(invoice => {
                          const remainingAmount = (invoice.totalAmount || 0) - (invoice.paidAmount || 0);
                          const isSelected = entryData.selectedInvoices.some(inv => inv.id === invoice.id);

                          return (
                            <tr key={invoice.id}>
                              <td>
                                <input
                                  type="checkbox"
                                  checked={isSelected}
                                  onChange={(e) => {
                                    if (e.target.checked) {
                                      setEntryData({
                                        ...entryData,
                                        selectedInvoices: [...entryData.selectedInvoices, {...invoice, paymentAmount: remainingAmount}]
                                      });
                                    } else {
                                      setEntryData({
                                        ...entryData,
                                        selectedInvoices: entryData.selectedInvoices.filter(inv => inv.id !== invoice.id)
                                      });
                                    }
                                  }}
                                />
                              </td>
                              <td>{invoice.invoiceNumber}</td>
                              <td>{new Date(invoice.date).toLocaleDateString('ar-EG')}</td>
                              <td>{(invoice.totalAmount || 0).toLocaleString('ar-EG')}</td>
                              <td>{(invoice.paidAmount || 0).toLocaleString('ar-EG')}</td>
                              <td>{(remainingAmount || 0).toLocaleString('ar-EG')}</td>
                              <td>
                                {isSelected && (
                                  <input
                                    type="number"
                                    className="form-control form-control-sm"
                                    value={entryData.selectedInvoices.find(inv => inv.id === invoice.id)?.paymentAmount || 0}
                                    onChange={(e) => {
                                      const newAmount = parseFloat(e.target.value) || 0;
                                      setEntryData({
                                        ...entryData,
                                        selectedInvoices: entryData.selectedInvoices.map(inv =>
                                          inv.id === invoice.id ? {...inv, paymentAmount: Math.min(newAmount, remainingAmount)} : inv
                                        )
                                      });
                                    }}
                                    max={remainingAmount}
                                    min="0"
                                    step="0.01"
                                  />
                                )}
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>

                  {entryData.selectedInvoices.length > 0 && (
                    <div style={{ marginTop: '1rem', padding: '1rem', background: '#f8f9fa', borderRadius: '5px' }}>
                      <strong>إجمالي المبلغ المطلوب سداده: {entryData.selectedInvoices.reduce((sum, inv) => sum + parseFloat(inv.paymentAmount || 0), 0).toLocaleString('ar-EG')} ج.م</strong>
                    </div>
                  )}
                </div>
              )}

              <div style={{ marginTop: '1rem' }}>
                <button
                  className="btn btn-primary"
                  onClick={createPurchasePaymentEntry}
                  disabled={!entryData.supplierId || entryData.selectedInvoices.length === 0 || !entryData.accountCode}
                >
                  🔄 إنشاء قيد السداد
                </button>
              </div>
            </div>
          )}

          {/* نموذج تحصيل المبيعات */}
          {entryData.type === 'sales-collection' && (
            <div className="card" style={{ marginBottom: '2rem' }}>
              <div className="card-title">تحصيل فواتير المبيعات</div>

              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem', marginBottom: '1rem' }}>
                <div className="form-group">
                  <label className="form-label">العميل</label>
                  <select
                    className="form-control"
                    value={entryData.customerId}
                    onChange={(e) => setEntryData({...entryData, customerId: e.target.value, selectedInvoices: []})}
                  >
                    <option value="">اختر العميل</option>
                    {customers.map(customer => (
                      <option key={customer.id} value={customer.id}>
                        {customer.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label className="form-label">رقم حساب التحصيل</label>
                  <input
                    type="text"
                    className="form-control"
                    value={entryData.accountCode}
                    onChange={(e) => {
                      const validation = validateAccountCode(e.target.value);
                      setEntryData({...entryData, accountCode: e.target.value, accountName: validation.account?.name || ''});
                    }}
                    placeholder="أدخل رقم الحساب"
                  />
                  {entryData.accountCode && entryData.accountName && (
                    <small style={{ color: '#28a745' }}>✓ {entryData.accountName}</small>
                  )}
                  {entryData.accountCode && !entryData.accountName && (
                    <small style={{ color: '#dc3545' }}>✗ رقم الحساب غير صحيح</small>
                  )}
                </div>
              </div>

              {entryData.customerId && (
                <div>
                  <h6>الفواتير غير المحصلة:</h6>
                  <div style={{ maxHeight: '300px', overflow: 'auto' }}>
                    <table className="table table-sm">
                      <thead>
                        <tr>
                          <th>اختيار</th>
                          <th>رقم الفاتورة</th>
                          <th>التاريخ</th>
                          <th>المبلغ الإجمالي</th>
                          <th>المحصل</th>
                          <th>المتبقي</th>
                          <th>مبلغ التحصيل</th>
                        </tr>
                      </thead>
                      <tbody>
                        {getUnpaidSalesInvoices(entryData.customerId).map(invoice => {
                          // التعامل مع أسماء الحقول المختلفة
                          const totalAmount = invoice.total || invoice.totalAmount || 0;
                          const remainingAmount = totalAmount - (invoice.paidAmount || 0);
                          const isSelected = entryData.selectedInvoices.some(inv => inv.id === invoice.id);

                          return (
                            <tr key={invoice.id}>
                              <td>
                                <input
                                  type="checkbox"
                                  checked={isSelected}
                                  onChange={(e) => {
                                    if (e.target.checked) {
                                      setEntryData({
                                        ...entryData,
                                        selectedInvoices: [...entryData.selectedInvoices, {...invoice, paymentAmount: remainingAmount, totalAmount: totalAmount}]
                                      });
                                    } else {
                                      setEntryData({
                                        ...entryData,
                                        selectedInvoices: entryData.selectedInvoices.filter(inv => inv.id !== invoice.id)
                                      });
                                    }
                                  }}
                                />
                              </td>
                              <td>{invoice.invoiceNumber || 'غير محدد'}</td>
                              <td>{new Date(invoice.date).toLocaleDateString('ar-EG')}</td>
                              <td>{totalAmount.toLocaleString('ar-EG')}</td>
                              <td>{(invoice.paidAmount || 0).toLocaleString('ar-EG')}</td>
                              <td>{remainingAmount.toLocaleString('ar-EG')}</td>
                              <td>
                                {isSelected && (
                                  <input
                                    type="number"
                                    className="form-control form-control-sm"
                                    value={entryData.selectedInvoices.find(inv => inv.id === invoice.id)?.paymentAmount || 0}
                                    onChange={(e) => {
                                      const newAmount = parseFloat(e.target.value) || 0;
                                      setEntryData({
                                        ...entryData,
                                        selectedInvoices: entryData.selectedInvoices.map(inv =>
                                          inv.id === invoice.id ? {...inv, paymentAmount: Math.min(newAmount, remainingAmount)} : inv
                                        )
                                      });
                                    }}
                                    max={remainingAmount}
                                    min="0"
                                    step="0.01"
                                  />
                                )}
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>

                  {entryData.selectedInvoices.length > 0 && (
                    <div style={{ marginTop: '1rem', padding: '1rem', background: '#f8f9fa', borderRadius: '5px' }}>
                      <strong>إجمالي المبلغ المطلوب تحصيله: {entryData.selectedInvoices.reduce((sum, inv) => sum + parseFloat(inv.paymentAmount || 0), 0).toLocaleString('ar-EG')} ج.م</strong>
                    </div>
                  )}
                </div>
              )}

              <div style={{ marginTop: '1rem' }}>
                <button
                  className="btn btn-primary"
                  onClick={createSalesCollectionEntry}
                  disabled={!entryData.customerId || entryData.selectedInvoices.length === 0 || !entryData.accountCode}
                >
                  🔄 إنشاء قيد التحصيل
                </button>
              </div>
            </div>
          )}

          {/* نموذج مصاريف العقود */}
          {entryData.type === 'contract-expense' && (
            <div className="card" style={{ marginBottom: '2rem' }}>
              <div className="card-title">مصاريف العقود</div>

              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem' }}>
                <div className="form-group">
                  <label className="form-label">العقد</label>
                  <select
                    className="form-control"
                    value={entryData.contractId}
                    onChange={(e) => setEntryData({...entryData, contractId: e.target.value})}
                  >
                    <option value="">اختر العقد</option>
                    {contracts.map(contract => (
                      <option key={contract.id} value={contract.id}>
                        {contract.contractNumber} - {contract.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label className="form-label">المبلغ</label>
                  <input
                    type="number"
                    className="form-control"
                    value={entryData.amount}
                    onChange={(e) => setEntryData({...entryData, amount: e.target.value})}
                    min="0"
                    step="0.01"
                  />
                </div>

                <div className="form-group">
                  <label className="form-label">وصف المصروف</label>
                  <input
                    type="text"
                    className="form-control"
                    value={entryData.description}
                    onChange={(e) => setEntryData({...entryData, description: e.target.value})}
                    placeholder="وصف تفصيلي للمصروف"
                    required
                  />
                </div>

                <div className="form-group">
                  <label className="form-label">رقم حساب الدفع</label>
                  <input
                    type="text"
                    className="form-control"
                    value={entryData.accountCode}
                    onChange={(e) => {
                      const validation = validateAccountCode(e.target.value);
                      setEntryData({...entryData, accountCode: e.target.value, accountName: validation.account?.name || ''});
                    }}
                    placeholder="أدخل رقم الحساب (مثل: 111)"
                    list="contract-accounts-list"
                  />
                  <datalist id="contract-accounts-list">
                    {accounts.map(account => (
                      <option key={account.id} value={account.code}>
                        {account.code} - {account.name}
                      </option>
                    ))}
                  </datalist>
                  {entryData.accountCode && entryData.accountName && (
                    <small style={{ color: '#28a745' }}>✓ {entryData.accountName}</small>
                  )}
                  {entryData.accountCode && !entryData.accountName && (
                    <small style={{ color: '#dc3545' }}>✗ رقم الحساب غير صحيح</small>
                  )}
                </div>
              </div>

              <div style={{ marginTop: '1rem' }}>
                <button
                  className="btn btn-primary"
                  onClick={createContractExpenseEntry}
                  disabled={!entryData.contractId || !entryData.amount || !entryData.accountCode || !entryData.description.trim()}
                >
                  🔄 إنشاء قيد المصاريف
                </button>
              </div>
            </div>
          )}

          {/* نموذج دفع أذون العمالة */}
          {entryData.type === 'worker-payment' && (
            <div className="card" style={{ marginBottom: '2rem' }}>
              <div className="card-title">دفع أذون صرف العمالة</div>

              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem', marginBottom: '1rem' }}>
                <div className="form-group">
                  <label className="form-label">رقم حساب الدفع</label>
                  <input
                    type="text"
                    className="form-control"
                    value={entryData.accountCode}
                    onChange={(e) => {
                      const validation = validateAccountCode(e.target.value);
                      setEntryData({...entryData, accountCode: e.target.value, accountName: validation.account?.name || ''});
                    }}
                    placeholder="أدخل رقم الحساب (مثل: 111)"
                    list="worker-accounts-list"
                  />
                  <datalist id="worker-accounts-list">
                    {accounts.map(account => (
                      <option key={account.id} value={account.code}>
                        {account.code} - {account.name}
                      </option>
                    ))}
                  </datalist>
                  {entryData.accountCode && entryData.accountName && (
                    <small style={{ color: '#28a745' }}>✓ {entryData.accountName}</small>
                  )}
                  {entryData.accountCode && !entryData.accountName && (
                    <small style={{ color: '#dc3545' }}>✗ رقم الحساب غير صحيح</small>
                  )}
                </div>
              </div>

              <div>
                <h6>أذون الصرف غير المسددة:</h6>
                <div style={{ maxHeight: '300px', overflow: 'auto' }}>
                  <table className="table table-sm">
                    <thead>
                      <tr>
                        <th>اختيار</th>
                        <th>رقم الأذن</th>
                        <th>العامل</th>
                        <th>التاريخ</th>
                        <th>المبلغ الإجمالي</th>
                        <th>المسدد</th>
                        <th>المتبقي</th>
                        <th>مبلغ السداد</th>
                      </tr>
                    </thead>
                    <tbody>
                      {getUnpaidWorkerVouchers().map(voucher => {
                        const totalAmount = voucher.totalAmount || voucher.amount || 0;
                        const paidAmount = voucher.paidAmount || 0;
                        const remainingAmount = totalAmount - paidAmount;
                        const isSelected = entryData.selectedVouchers.some(v => v.id === voucher.id);

                        return (
                          <tr key={voucher.id}>
                            <td>
                              <input
                                type="checkbox"
                                checked={isSelected}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    setEntryData({
                                      ...entryData,
                                      selectedVouchers: [...entryData.selectedVouchers, {...voucher, paymentAmount: remainingAmount}]
                                    });
                                  } else {
                                    setEntryData({
                                      ...entryData,
                                      selectedVouchers: entryData.selectedVouchers.filter(v => v.id !== voucher.id)
                                    });
                                  }
                                }}
                              />
                            </td>
                            <td>
                              <span style={{
                                fontFamily: 'monospace',
                                fontWeight: 'bold',
                                color: '#007bff'
                              }}>
                                {voucher.voucherNumber}
                              </span>
                            </td>
                            <td>{voucher.workerName || 'غير معروف'}</td>
                            <td>{new Date(voucher.date || voucher.createdAt).toLocaleDateString('ar-EG')}</td>
                            <td style={{ fontWeight: 'bold', color: '#007bff' }}>
                              {totalAmount.toLocaleString('ar-EG')} ج.م
                            </td>
                            <td style={{ color: '#28a745' }}>
                              {paidAmount.toLocaleString('ar-EG')} ج.م
                            </td>
                            <td style={{
                              fontWeight: 'bold',
                              color: remainingAmount > 0 ? '#dc3545' : '#28a745'
                            }}>
                              {remainingAmount.toLocaleString('ar-EG')} ج.م
                            </td>
                            <td>
                              {isSelected && (
                                <input
                                  type="number"
                                  className="form-control form-control-sm"
                                  value={entryData.selectedVouchers.find(v => v.id === voucher.id)?.paymentAmount || 0}
                                  onChange={(e) => {
                                    const newAmount = parseFloat(e.target.value) || 0;
                                    setEntryData({
                                      ...entryData,
                                      selectedVouchers: entryData.selectedVouchers.map(v =>
                                        v.id === voucher.id ? {...v, paymentAmount: Math.min(newAmount, remainingAmount)} : v
                                      )
                                    });
                                  }}
                                  max={remainingAmount}
                                  min="0"
                                  step="0.01"
                                />
                              )}
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>

                  {getUnpaidWorkerVouchers().length === 0 && (
                    <div style={{
                      textAlign: 'center',
                      padding: '2rem',
                      color: '#666',
                      background: '#f8f9fa',
                      borderRadius: '5px',
                      margin: '1rem 0'
                    }}>
                      <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📋</div>
                      <h5>لا توجد أذون صرف غير مسددة</h5>
                      <p style={{ marginBottom: '1rem' }}>
                        لا توجد أذون صرف عمالة معتمدة ومتاحة للسداد حالياً
                      </p>
                      <div style={{ fontSize: '0.9rem', color: '#999' }}>
                        <p>للحصول على أذون للسداد، تأكد من:</p>
                        <ul style={{ textAlign: 'right', display: 'inline-block' }}>
                          <li>وجود أذون صرف عمالة مُنشأة</li>
                          <li>أن حالة الأذون "معتمد" وليس "معلق"</li>
                          <li>أن الأذون لم يتم سدادها بالكامل</li>
                        </ul>
                      </div>
                    </div>
                  )}
                </div>

                {entryData.selectedVouchers.length > 0 && (
                  <div style={{ marginTop: '1rem', padding: '1rem', background: '#f8f9fa', borderRadius: '5px' }}>
                    <strong>إجمالي المبلغ المطلوب سداده: {entryData.selectedVouchers.reduce((sum, v) => sum + parseFloat(v.paymentAmount || 0), 0).toLocaleString('ar-EG')} ج.م</strong>
                  </div>
                )}
              </div>

              <div style={{ marginTop: '1rem' }}>
                <button
                  className="btn btn-primary"
                  onClick={createWorkerPaymentEntry}
                  disabled={entryData.selectedVouchers.length === 0 || !entryData.accountCode}
                >
                  🔄 إنشاء قيد دفع العمالة
                </button>
              </div>
            </div>
          )}

          {/* معلومات القيد الأساسية */}
          <div className="card" style={{ marginBottom: '2rem' }}>
            <div className="card-title">معلومات القيد</div>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem' }}>
              <div className="form-group">
                <label className="form-label">رقم القيد</label>
                <input
                  type="text"
                  className="form-control"
                  value={entryData.entryNumber}
                  onChange={(e) => setEntryData({...entryData, entryNumber: e.target.value})}
                  placeholder="سيتم إنشاؤه تلقائياً"
                />
              </div>

              <div className="form-group">
                <label className="form-label">التاريخ</label>
                <input
                  type="date"
                  className="form-control"
                  value={entryData.date}
                  onChange={(e) => setEntryData({...entryData, date: e.target.value})}
                  required
                />
              </div>

              <div className="form-group">
                <label className="form-label">المرجع</label>
                <input
                  type="text"
                  className="form-control"
                  value={entryData.reference}
                  onChange={(e) => setEntryData({...entryData, reference: e.target.value})}
                  placeholder="رقم الفاتورة أو المرجع"
                />
              </div>

              <div className="form-group">
                <label className="form-label">النوع</label>
                <select
                  className="form-control"
                  value={entryData.type}
                  onChange={(e) => setEntryData({...entryData, type: e.target.value})}
                >
                  <option value="manual">يدوي</option>
                  <option value="purchase-payment">سداد مشتريات</option>
                  <option value="sales-collection">تحصيل مبيعات</option>
                  <option value="contract-expense">مصاريف عقد</option>
                  <option value="worker-payment">دفع عمالة</option>
                  <option value="sales">مبيعات</option>
                  <option value="purchase">مشتريات</option>
                  <option value="payment">دفع</option>
                  <option value="receipt">قبض</option>
                  <option value="adjustment">تسوية</option>
                </select>
              </div>
            </div>

            <div className="form-group" style={{ marginTop: '1rem' }}>
              <label className="form-label">وصف القيد</label>
              <textarea
                className="form-control"
                value={entryData.description}
                onChange={(e) => setEntryData({...entryData, description: e.target.value})}
                placeholder="وصف مفصل للقيد"
                rows="3"
                required
              />
            </div>
          </div>

          {/* القيد اليدوي المحسن - يظهر فقط للقيد اليدوي */}
          {entryData.type === 'manual' && (
            <div className="card" style={{ marginBottom: '2rem' }}>
              <div className="card-title">📝 إدخال بنود القيد اليدوي</div>

              {/* تلميحات مساعدة */}
              <div style={{
                background: '#e3f2fd',
                padding: '0.75rem',
                borderRadius: '3px',
                marginBottom: '1rem',
                fontSize: '0.9rem',
                color: '#1976d2'
              }}>
                💡 <strong>تلميح:</strong> أدخل رقم الحساب وسيتم ملء اسم الحساب تلقائياً.
                يمكن إدخال مبلغ في المدين أو الدائن فقط، وليس كلاهما.
              </div>

              {/* أرقام الحسابات من دليل الحسابات */}
              <details style={{ marginBottom: '1rem' }}>
                <summary style={{
                  cursor: 'pointer',
                  color: '#007bff',
                  fontWeight: 'bold',
                  padding: '0.5rem',
                  background: '#f8f9fa',
                  borderRadius: '3px'
                }}>
                  📋 دليل الحسابات ({accounts.length} حساب)
                </summary>
                <div style={{
                  marginTop: '0.5rem',
                  padding: '1rem',
                  background: 'white',
                  borderRadius: '3px',
                  border: '1px solid #dee2e6',
                  maxHeight: '300px',
                  overflow: 'auto'
                }}>
                  {accounts.length > 0 ? (
                    <div style={{ fontSize: '0.85rem' }}>
                      {['أصول', 'التزامات', 'حقوق ملكية', 'إيرادات', 'مصروفات'].map(type => {
                        const typeAccounts = accounts.filter(acc => acc.type === type);
                        if (typeAccounts.length === 0) return null;

                        return (
                          <div key={type} style={{ marginBottom: '1rem' }}>
                            <strong style={{ color: '#007bff' }}>{type}:</strong>
                            <div style={{ marginTop: '0.5rem', marginRight: '1rem' }}>
                              {typeAccounts.map(account => (
                                <div
                                  key={account.id}
                                  style={{
                                    padding: '0.25rem 0.5rem',
                                    cursor: 'pointer',
                                    borderRadius: '3px',
                                    marginBottom: '0.25rem',
                                    ':hover': { background: '#f8f9fa' }
                                  }}
                                  onClick={() => {
                                    setCurrentEntry({
                                      ...currentEntry,
                                      accountCode: account.code,
                                      accountName: account.name
                                    });
                                  }}
                                  title="اضغط لاختيار هذا الحساب"
                                >
                                  <span style={{ fontFamily: 'monospace', fontWeight: 'bold', color: '#007bff' }}>
                                    {account.code}
                                  </span>
                                  {' - '}
                                  <span>{account.name}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <div style={{ textAlign: 'center', color: '#666', padding: '2rem' }}>
                      <p>لا توجد حسابات في دليل الحسابات</p>
                      <p style={{ fontSize: '0.9rem' }}>يرجى إضافة حسابات من قسم "دليل الحسابات" أولاً</p>
                    </div>
                  )}
                </div>
              </details>

              {/* إضافة بند جديد */}
              <div style={{
                background: '#f8f9fa',
                padding: '1.5rem',
                borderRadius: '5px',
                marginBottom: '2rem',
                border: '2px dashed #007bff'
              }}>
                <h4 style={{ marginBottom: '1rem', color: '#007bff' }}>➕ إضافة بند جديد</h4>

                <div className="grid grid-5" style={{ gap: '1rem', marginBottom: '1rem' }}>
                  <div className="form-group" style={{ position: 'relative' }}>
                    <label className="form-label">رقم الحساب *</label>
                    <input
                      type="text"
                      className="form-control"
                      value={currentEntry.accountCode}
                      onChange={(e) => {
                        const accountCode = e.target.value;
                        setCurrentEntry({
                          ...currentEntry,
                          accountCode: accountCode,
                          accountName: getAccountName(accountCode)
                        });
                        setAccountSearchTerm(accountCode);
                        setShowAccountSuggestions(accountCode.length > 0);
                      }}
                      onFocus={() => {
                        if (currentEntry.accountCode) {
                          setAccountSearchTerm(currentEntry.accountCode);
                          setShowAccountSuggestions(true);
                        }
                      }}
                      onBlur={() => {
                        // تأخير إخفاء الاقتراحات للسماح بالنقر عليها
                        setTimeout(() => setShowAccountSuggestions(false), 200);
                      }}
                      placeholder="أدخل رقم أو اسم الحساب"
                      style={{ fontFamily: 'monospace', fontSize: '1rem' }}
                    />

                    {/* قائمة اقتراحات الحسابات */}
                    {showAccountSuggestions && accountSearchTerm && (
                      <div style={{
                        position: 'absolute',
                        top: '100%',
                        left: 0,
                        right: 0,
                        background: 'white',
                        border: '1px solid #dee2e6',
                        borderRadius: '3px',
                        maxHeight: '200px',
                        overflow: 'auto',
                        zIndex: 1000,
                        boxShadow: '0 2px 5px rgba(0,0,0,0.1)'
                      }}>
                        {getFilteredAccounts().slice(0, 10).map(account => (
                          <div
                            key={account.id}
                            style={{
                              padding: '0.5rem',
                              cursor: 'pointer',
                              borderBottom: '1px solid #f8f9fa',
                              ':hover': { background: '#f8f9fa' }
                            }}
                            onMouseDown={() => selectAccount(account)}
                            onMouseEnter={(e) => e.target.style.background = '#f8f9fa'}
                            onMouseLeave={(e) => e.target.style.background = 'white'}
                          >
                            <div style={{ fontWeight: 'bold', color: '#007bff', fontFamily: 'monospace' }}>
                              {account.code}
                            </div>
                            <div style={{ fontSize: '0.9rem', color: '#666' }}>
                              {account.name}
                            </div>
                          </div>
                        ))}
                        {getFilteredAccounts().length === 0 && (
                          <div style={{ padding: '1rem', textAlign: 'center', color: '#666' }}>
                            لا توجد حسابات مطابقة
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  <div className="form-group">
                    <label className="form-label">اسم الحساب</label>
                    <input
                      type="text"
                      className="form-control"
                      value={currentEntry.accountName}
                      onChange={(e) => setCurrentEntry({...currentEntry, accountName: e.target.value})}
                      placeholder="اسم الحساب (يتم ملؤه تلقائياً)"
                      style={{ background: currentEntry.accountName ? '#f8f9fa' : 'white' }}
                    />
                  </div>

                  <div className="form-group">
                    <label className="form-label">البيان</label>
                    <input
                      type="text"
                      className="form-control"
                      value={currentEntry.description}
                      onChange={(e) => setCurrentEntry({...currentEntry, description: e.target.value})}
                      placeholder="بيان البند"
                    />
                  </div>

                  <div className="form-group">
                    <label className="form-label">مدين</label>
                    <input
                      type="number"
                      className="form-control"
                      value={currentEntry.debit}
                      onChange={(e) => setCurrentEntry({...currentEntry, debit: e.target.value, credit: ''})}
                      min="0"
                      step="0.01"
                      placeholder="0.00"
                      style={{
                        color: currentEntry.debit ? '#dc3545' : '#6c757d',
                        fontWeight: currentEntry.debit ? 'bold' : 'normal'
                      }}
                    />
                  </div>

                  <div className="form-group">
                    <label className="form-label">دائن</label>
                    <input
                      type="number"
                      className="form-control"
                      value={currentEntry.credit}
                      onChange={(e) => setCurrentEntry({...currentEntry, credit: e.target.value, debit: ''})}
                      min="0"
                      step="0.01"
                      placeholder="0.00"
                      style={{
                        color: currentEntry.credit ? '#28a745' : '#6c757d',
                        fontWeight: currentEntry.credit ? 'bold' : 'normal'
                      }}
                    />
                  </div>
                </div>

                <div style={{ textAlign: 'center' }}>
                  <button
                    className="btn btn-primary"
                    onClick={addEntryLine}
                    disabled={!currentEntry.accountCode.trim() || (!currentEntry.debit && !currentEntry.credit)}
                    style={{
                      padding: '0.75rem 2rem',
                      fontSize: '1rem',
                      opacity: (!currentEntry.accountCode.trim() || (!currentEntry.debit && !currentEntry.credit)) ? 0.6 : 1
                    }}
                  >
                    ➕ إضافة البند
                  </button>
                </div>
              </div>

              {/* جدول بنود القيد */}
              {entryDetails.length > 0 && (
                <div style={{ marginBottom: '2rem' }}>
                  <h4 style={{ marginBottom: '1rem', color: '#007bff' }}>📋 بنود القيد</h4>

                  <div style={{ overflow: 'auto' }}>
                    <table className="table">
                      <thead>
                        <tr style={{ background: '#007bff', color: 'white' }}>
                          <th>رقم الحساب</th>
                          <th>اسم الحساب</th>
                          <th>البيان</th>
                          <th>مدين</th>
                          <th>دائن</th>
                          <th>الإجراءات</th>
                        </tr>
                      </thead>
                      <tbody>
                        {entryDetails.map((entry, index) => (
                          <tr key={index}>
                            <td style={{ fontFamily: 'monospace', fontWeight: 'bold', color: '#007bff' }}>
                              {entry.accountCode}
                            </td>
                            <td style={{ fontWeight: 'bold' }}>{entry.accountName}</td>
                            <td>{entry.description}</td>
                            <td style={{
                              color: (entry.debit || 0) > 0 ? '#dc3545' : '#6c757d',
                              fontWeight: (entry.debit || 0) > 0 ? 'bold' : 'normal'
                            }}>
                              {(entry.debit || 0) > 0 ? (entry.debit || 0).toLocaleString('ar-EG') : '-'}
                            </td>
                            <td style={{
                              color: (entry.credit || 0) > 0 ? '#28a745' : '#6c757d',
                              fontWeight: (entry.credit || 0) > 0 ? 'bold' : 'normal'
                            }}>
                              {(entry.credit || 0) > 0 ? (entry.credit || 0).toLocaleString('ar-EG') : '-'}
                            </td>
                            <td>
                              <button
                                className="btn btn-sm btn-danger"
                                onClick={() => removeEntryLine(index)}
                                style={{ padding: '0.25rem 0.5rem' }}
                              >
                                🗑️ حذف
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                      <tfoot>
                        <tr style={{
                          background: isBalanced() ? '#d4edda' : '#f8d7da',
                          fontWeight: 'bold'
                        }}>
                          <td colSpan="3" style={{ textAlign: 'center' }}>الإجمالي</td>
                          <td style={{ color: '#dc3545' }}>
                            {getTotalDebit().toLocaleString('ar-EG')} ج.م
                          </td>
                          <td style={{ color: '#28a745' }}>
                            {getTotalCredit().toLocaleString('ar-EG')} ج.م
                          </td>
                          <td>
                            {isBalanced() ? '✅ متوازن' : '❌ غير متوازن'}
                          </td>
                        </tr>
                      </tfoot>
                    </table>
                  </div>
                </div>
              )}

              {/* أزرار الحفظ */}
              <div style={{ textAlign: 'center', marginTop: '2rem' }}>
                <button
                  className="btn btn-success"
                  onClick={saveManualJournalEntry}
                  disabled={!isBalanced()}
                  style={{ padding: '0.75rem 2rem', marginRight: '1rem' }}
                >
                  💾 حفظ القيد
                </button>
                <button
                  className="btn btn-secondary"
                  onClick={resetManualEntryForm}
                  style={{ padding: '0.75rem 2rem' }}
                >
                  🔄 مسح البيانات
                </button>
              </div>
            </div>
          )}

          {/* بنود القيد - للأنواع الأخرى */}
          {entryData.type !== 'manual' && (
          <div className="card" style={{ marginBottom: '2rem' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
              <div className="card-title">بنود القيد</div>
              <button
                className="btn btn-primary"
                onClick={addEntryDetail}
              >
                ➕ إضافة بند
              </button>
            </div>

            <div style={{ overflow: 'auto' }}>
              <table className="table">
                <thead>
                  <tr>
                    <th>الحساب</th>
                    <th>البيان</th>
                    <th>مدين</th>
                    <th>دائن</th>
                    <th>إجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {entryDetails.map((detail, index) => (
                    <tr key={index}>
                      <td>
                        <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                          <div style={{ position: 'relative' }}>
                            <input
                              type="text"
                              className="form-control"
                              value={detail.accountCode || ''}
                              onChange={(e) => {
                                const code = e.target.value;
                                console.log('إدخال رقم الحساب:', code);

                                const account = accounts.find(acc => acc.code === code.trim());
                                console.log('الحساب الموجود:', account);

                                updateEntryDetail(index, 'accountCode', code);
                                updateEntryDetail(index, 'accountId', account ? account.id : '');
                                updateEntryDetail(index, 'accountName', account ? account.name : '');
                              }}
                              placeholder="أدخل رقم الحساب (مثل: 111)"
                              style={{ minWidth: '150px' }}
                              list={`accounts-list-${index}`}
                            />
                            <datalist id={`accounts-list-${index}`}>
                              {accounts.map(account => (
                                <option key={account.id} value={account.code}>
                                  {account.code} - {account.name}
                                </option>
                              ))}
                            </datalist>
                          </div>
                          {detail.accountCode && detail.accountName && (
                            <small style={{ color: '#28a745', fontSize: '0.8rem' }}>
                              ✓ {detail.accountName}
                            </small>
                          )}
                          {detail.accountCode && !detail.accountName && (
                            <small style={{ color: '#dc3545', fontSize: '0.8rem' }}>
                              ✗ رقم غير صحيح
                            </small>
                          )}
                        </div>
                      </td>
                      <td>
                        <input
                          type="text"
                          className="form-control"
                          value={detail.description}
                          onChange={(e) => updateEntryDetail(index, 'description', e.target.value)}
                          placeholder="بيان البند"
                          style={{ minWidth: '150px' }}
                        />
                      </td>
                      <td>
                        <input
                          type="number"
                          className="form-control"
                          value={detail.debit}
                          onChange={(e) => updateEntryDetail(index, 'debit', parseFloat(e.target.value) || 0)}
                          min="0"
                          step="0.01"
                          style={{ minWidth: '120px' }}
                        />
                      </td>
                      <td>
                        <input
                          type="number"
                          className="form-control"
                          value={detail.credit}
                          onChange={(e) => updateEntryDetail(index, 'credit', parseFloat(e.target.value) || 0)}
                          min="0"
                          step="0.01"
                          style={{ minWidth: '120px' }}
                        />
                      </td>
                      <td>
                        <button
                          className="btn btn-danger btn-sm"
                          onClick={() => removeEntryDetail(index)}
                          disabled={entryDetails.length === 1}
                        >
                          🗑️
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
                <tfoot>
                  <tr style={{ fontWeight: 'bold', backgroundColor: '#f8f9fa' }}>
                    <td colSpan="2">الإجمالي</td>
                    <td>{getTotalDebit().toLocaleString('ar-EG')}</td>
                    <td>{getTotalCredit().toLocaleString('ar-EG')}</td>
                    <td>
                      {isBalanced() ? (
                        <span style={{ color: '#28a745' }}>✓ متوازن</span>
                      ) : (
                        <span style={{ color: '#dc3545' }}>✗ غير متوازن</span>
                      )}
                    </td>
                  </tr>
                </tfoot>
              </table>
            </div>

            {!isBalanced() && getTotalDebit() > 0 && (
              <div style={{
                padding: '1rem',
                background: '#f8d7da',
                border: '1px solid #f5c6cb',
                borderRadius: '5px',
                color: '#721c24',
                marginTop: '1rem'
              }}>
                ⚠️ القيد غير متوازن! الفرق: {Math.abs(getTotalDebit() - getTotalCredit()).toLocaleString('ar-EG')} ج.م
              </div>
            )}
          </div>
          )}
        </div>
      )}

      {/* قوالب القيود */}
      {currentView === 'templates' && (
        <div className="card">
          <div className="card-title">قوالب القيود ({templates.length})</div>

          {templates.length === 0 ? (
            <div style={{ textAlign: 'center', padding: '3rem', color: '#6c757d' }}>
              <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📋</div>
              <div style={{ fontSize: '1.2rem', marginBottom: '0.5rem' }}>لا توجد قوالب محفوظة</div>
              <div>قم بإنشاء قيد واحفظه كقالب للاستخدام المستقبلي</div>
            </div>
          ) : (
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '1rem' }}>
              {templates.map(template => (
                <div key={template.id} className="card" style={{ border: '1px solid #ddd' }}>
                  <div style={{ fontWeight: 'bold', marginBottom: '0.5rem' }}>{template.name}</div>
                  <div style={{ color: '#666', marginBottom: '0.5rem', fontSize: '0.9rem' }}>
                    {template.description}
                  </div>
                  <div style={{ marginBottom: '1rem' }}>
                    <span style={{
                      padding: '0.25rem 0.5rem',
                      borderRadius: '3px',
                      fontSize: '0.8rem',
                      background: '#e9ecef',
                      color: '#495057'
                    }}>
                      {template.type === 'manual' ? 'يدوي' :
                       template.type === 'purchase-payment' ? 'سداد مشتريات' :
                       template.type === 'sales-collection' ? 'تحصيل مبيعات' :
                       template.type === 'contract-expense' ? 'مصاريف عقد' :
                       template.type}
                    </span>
                  </div>
                  <div style={{ fontSize: '0.8rem', color: '#666', marginBottom: '1rem' }}>
                    {template.entries.length} بند - تم الإنشاء: {new Date(template.createdAt).toLocaleDateString('ar-EG')}
                  </div>
                  <div style={{ display: 'flex', gap: '0.5rem' }}>
                    <button
                      className="btn btn-primary btn-sm"
                      onClick={() => applyTemplate(template)}
                    >
                      📝 استخدام
                    </button>
                    <button
                      className="btn btn-danger btn-sm"
                      onClick={async () => {
                        if (confirm('هل تريد حذف هذا القالب؟')) {
                          try {
                            if (db.journalTemplates) {
                              await db.journalTemplates.delete(template.id);
                              await loadTemplates();
                            }
                          } catch (error) {
                            console.error('خطأ في حذف القالب:', error);
                            alert('حدث خطأ أثناء حذف القالب');
                          }
                        }
                      }}
                    >
                      🗑️
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* قائمة القيود */}
      {currentView === 'list' && (
        <div>
          {/* فلاتر البحث */}
          <div className="card" style={{ marginBottom: '2rem' }}>
            <div className="card-title">البحث والفلترة</div>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem' }}>
              <div className="form-group">
                <label className="form-label">رقم القيد</label>
                <input
                  type="text"
                  className="form-control"
                  value={searchFilters.entryNumber}
                  onChange={(e) => setSearchFilters({...searchFilters, entryNumber: e.target.value})}
                  placeholder="البحث برقم القيد"
                />
              </div>

              <div className="form-group">
                <label className="form-label">الوصف</label>
                <input
                  type="text"
                  className="form-control"
                  value={searchFilters.description}
                  onChange={(e) => setSearchFilters({...searchFilters, description: e.target.value})}
                  placeholder="البحث في الوصف"
                />
              </div>

              <div className="form-group">
                <label className="form-label">نوع القيد</label>
                <select
                  className="form-control"
                  value={searchFilters.type}
                  onChange={(e) => setSearchFilters({...searchFilters, type: e.target.value})}
                >
                  <option value="">جميع الأنواع</option>
                  <option value="manual">يدوي</option>
                  <option value="purchase-payment">سداد مشتريات</option>
                  <option value="sales-collection">تحصيل مبيعات</option>
                  <option value="contract-expense">مصاريف عقد</option>
                  <option value="worker-payment">دفع عمالة</option>
                  <option value="sales">مبيعات</option>
                  <option value="purchase">مشتريات</option>
                </select>
              </div>

              <div className="form-group">
                <label className="form-label">من تاريخ</label>
                <input
                  type="date"
                  className="form-control"
                  value={searchFilters.dateFrom}
                  onChange={(e) => setSearchFilters({...searchFilters, dateFrom: e.target.value})}
                />
              </div>

              <div className="form-group">
                <label className="form-label">إلى تاريخ</label>
                <input
                  type="date"
                  className="form-control"
                  value={searchFilters.dateTo}
                  onChange={(e) => setSearchFilters({...searchFilters, dateTo: e.target.value})}
                />
              </div>

              <div className="form-group">
                <label className="form-label">الحساب</label>
                <select
                  className="form-control"
                  value={searchFilters.accountId}
                  onChange={(e) => setSearchFilters({...searchFilters, accountId: e.target.value})}
                >
                  <option value="">جميع الحسابات</option>
                  {accounts.map(account => (
                    <option key={account.id} value={account.id}>
                      {account.code} - {account.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div style={{ marginTop: '1rem', display: 'flex', gap: '1rem' }}>
              <button
                className="btn btn-secondary"
                onClick={() => setSearchFilters({
                  entryNumber: '',
                  description: '',
                  type: '',
                  dateFrom: '',
                  dateTo: '',
                  accountId: ''
                })}
              >
                🔄 مسح الفلاتر
              </button>
              <div style={{ marginLeft: 'auto', color: '#666' }}>
                عدد النتائج: {getFilteredEntries().length} من {journalEntries.length}
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-title">قائمة القيود اليومية ({getFilteredEntries().length})</div>

          {getFilteredEntries().length === 0 ? (
            <div style={{ textAlign: 'center', padding: '3rem', color: '#6c757d' }}>
              <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📋</div>
              <div style={{ fontSize: '1.2rem', marginBottom: '0.5rem' }}>
                {journalEntries.length === 0 ? 'لا توجد قيود يومية' : 'لا توجد نتائج مطابقة للبحث'}
              </div>
              <div>
                {journalEntries.length === 0 ?
                  'انقر على "إنشاء قيد جديد" لإضافة أول قيد' :
                  'جرب تعديل فلاتر البحث'
                }
              </div>
            </div>
          ) : (
            <div style={{ overflow: 'auto' }}>
              <table className="table">
                <thead>
                  <tr>
                    <th>رقم القيد</th>
                    <th>التاريخ</th>
                    <th>الوصف</th>
                    <th>المرجع</th>
                    <th>النوع</th>
                    <th>الحالة</th>
                    <th>إجمالي المدين</th>
                    <th>إجمالي الدائن</th>
                    <th>إجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {getFilteredEntries().map(entry => (
                    <tr key={entry.id}>
                      <td>
                        <button
                          className="btn-link"
                          onClick={() => editJournalEntry(entry)}
                          style={{
                            background: 'none',
                            border: 'none',
                            color: '#007bff',
                            textDecoration: 'underline',
                            cursor: 'pointer',
                            fontWeight: 'bold'
                          }}
                        >
                          {entry.entryNumber}
                        </button>
                      </td>
                      <td>{new Date(entry.date).toLocaleDateString('ar-EG')}</td>
                      <td>{entry.description}</td>
                      <td>{entry.reference || '-'}</td>
                      <td>
                        <span style={{
                          padding: '0.25rem 0.5rem',
                          borderRadius: '3px',
                          fontSize: '0.8rem',
                          background:
                            entry.type === 'manual' ? '#e9ecef' :
                            entry.type === 'purchase-payment' ? '#f8d7da' :
                            entry.type === 'sales-collection' ? '#d4edda' :
                            entry.type === 'contract-expense' ? '#fff3cd' :
                            entry.type === 'worker-payment' ? '#d1ecf1' :
                            '#e9ecef',
                          color:
                            entry.type === 'manual' ? '#495057' :
                            entry.type === 'purchase-payment' ? '#721c24' :
                            entry.type === 'sales-collection' ? '#155724' :
                            entry.type === 'contract-expense' ? '#856404' :
                            entry.type === 'worker-payment' ? '#0c5460' :
                            '#495057'
                        }}>
                          {entry.type === 'manual' ? 'يدوي' :
                           entry.type === 'purchase-payment' ? 'سداد مشتريات' :
                           entry.type === 'sales-collection' ? 'تحصيل مبيعات' :
                           entry.type === 'contract-expense' ? 'مصاريف عقد' :
                           entry.type === 'worker-payment' ? 'دفع عمالة' :
                           entry.type}
                        </span>
                      </td>
                      <td>
                        <div>
                          <span style={{
                            padding: '0.25rem 0.5rem',
                            borderRadius: '3px',
                            fontSize: '0.8rem',
                            background:
                              entry.status === 'draft' ? '#fff3cd' :
                              entry.status === 'reviewed' ? '#d1ecf1' :
                              entry.status === 'posted' ? '#d4edda' : '#e9ecef',
                            color:
                              entry.status === 'draft' ? '#856404' :
                              entry.status === 'reviewed' ? '#0c5460' :
                              entry.status === 'posted' ? '#155724' : '#495057'
                          }}>
                            {entry.status === 'draft' ? 'مسودة' :
                             entry.status === 'reviewed' ? 'مراجع' :
                             entry.status === 'posted' ? 'مرحل' : 'غير محدد'}
                          </span>
                          {entry.editedAt && (
                            <div style={{ fontSize: '0.7rem', color: '#666', marginTop: '0.25rem' }}>
                              آخر تعديل: {new Date(entry.editedAt).toLocaleDateString('ar-EG')}
                            </div>
                          )}
                        </div>
                      </td>
                      <td style={{ fontWeight: 'bold', color: '#dc3545' }}>
                        {(entry.totalDebit || 0).toLocaleString('ar-EG')}
                      </td>
                      <td style={{ fontWeight: 'bold', color: '#28a745' }}>
                        {(entry.totalCredit || 0).toLocaleString('ar-EG')}
                      </td>
                      <td>
                        <div style={{ display: 'flex', gap: '0.5rem', flexWrap: 'wrap' }}>
                          <button
                            className="btn btn-info btn-sm"
                            onClick={() => printJournalEntry(entry)}
                          >
                            🖨️
                          </button>

                          {/* أزرار التعديل والمراجعة للقيود المسودة */}
                          {entry.status === 'draft' && (
                            <>
                              <button
                                className="btn btn-primary btn-sm"
                                onClick={() => editJournalEntry(entry)}
                                title="تعديل القيد"
                              >
                                ✏️ تعديل
                              </button>
                              <button
                                className="btn btn-warning btn-sm"
                                onClick={() => reviewJournalEntry(entry.id)}
                                title="مراجعة القيد"
                              >
                                👁️ مراجعة
                              </button>
                            </>
                          )}

                          {/* أزرار التعديل والترحيل للقيود المراجعة */}
                          {entry.status === 'reviewed' && (
                            <>
                              <button
                                className="btn btn-primary btn-sm"
                                onClick={() => editJournalEntry(entry)}
                                title="تعديل القيد (سيعود لحالة المسودة)"
                              >
                                ✏️ تعديل
                              </button>
                              <button
                                className="btn btn-success btn-sm"
                                onClick={() => postJournalEntry(entry.id)}
                                title="ترحيل القيد"
                              >
                                📤 ترحيل
                              </button>
                            </>
                          )}

                          {/* أزرار للقيود المرحلة */}
                          {entry.status === 'posted' && (
                            <>
                              <span
                                className="badge badge-success"
                                title="القيد مرحل"
                                style={{ marginRight: '0.5rem' }}
                              >
                                ✅ مرحل
                              </span>

                              {/* أزرار خاصة للمدير الأعلى */}
                              {(user.role === 'super-admin' || user.permissions?.editPostedEntries) && (
                                <>
                                  <button
                                    className="btn btn-info btn-sm"
                                    onClick={() => unpostJournalEntry(entry.id)}
                                    title="إلغاء ترحيل القيد (صلاحيات خاصة)"
                                    style={{ fontSize: '0.7rem' }}
                                  >
                                    ↩️ إلغاء ترحيل
                                  </button>
                                  <button
                                    className="btn btn-warning btn-sm"
                                    onClick={() => editJournalEntry(entry)}
                                    title="تعديل القيد المرحل (صلاحيات خاصة)"
                                    style={{ fontSize: '0.7rem' }}
                                  >
                                    ⚠️ تعديل مرحل
                                  </button>
                                </>
                              )}

                              {(user.role === 'super-admin' || user.permissions?.editAllData) && (
                                <button
                                  className="btn btn-danger btn-sm"
                                  onClick={() => deleteJournalEntry(entry.id)}
                                  title="حذف القيد المرحل (صلاحيات خاصة)"
                                  style={{ fontSize: '0.7rem' }}
                                >
                                  ⚠️ حذف مرحل
                                </button>
                              )}
                            </>
                          )}

                          {/* زر الحذف للقيود غير المرحلة */}
                          {entry.status !== 'posted' && (
                            <button
                              className="btn btn-danger btn-sm"
                              onClick={() => deleteJournalEntry(entry.id)}
                              title="حذف القيد"
                            >
                              🗑️ حذف
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
          </div>
        </div>
      )}
    </div>
  );
};

export default JournalEntries;
