import React, { useState, useEffect } from 'react';
import { db } from '../database/db';
import * as XLSX from 'xlsx';

const CashFlowReports = () => {
  const [loading, setLoading] = useState(false);
  const [dateFrom, setDateFrom] = useState(new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0]);
  const [dateTo, setDateTo] = useState(new Date().toISOString().split('T')[0]);
  const [cashFlowData, setCashFlowData] = useState({
    openingBalance: 0,
    inflows: [],
    outflows: [],
    closingBalance: 0
  });

  useEffect(() => {
    generateCashFlowReport();
  }, [dateFrom, dateTo]);

  const generateCashFlowReport = async () => {
    try {
      setLoading(true);

      // الحصول على حسابات النقدية والبنوك
      const cashAccounts = await db.accounts
        .where('code')
        .anyOf(['111', '112', '10101']) // النقدية بالصندوق، البنك، النقدية والبنوك
        .toArray();

      if (cashAccounts.length === 0) {
        setCashFlowData({
          openingBalance: 0,
          inflows: [],
          outflows: [],
          closingBalance: 0
        });
        return;
      }

      const cashAccountIds = cashAccounts.map(acc => acc.id);

      // الحصول على القيود في الفترة المحددة
      const entries = await db.journalEntries
        .where('date')
        .between(new Date(dateFrom), new Date(dateTo), true, true)
        .toArray();

      // حساب الرصيد الافتتاحي
      const entriesBeforePeriod = await db.journalEntries
        .where('date')
        .below(new Date(dateFrom))
        .toArray();

      let openingBalance = 0;
      entriesBeforePeriod.forEach(entry => {
        entry.entries.forEach(detail => {
          if (cashAccountIds.includes(detail.accountId)) {
            openingBalance += (detail.debit || 0) - (detail.credit || 0);
          }
        });
      });

      // تصنيف التدفقات النقدية
      const inflows = [];
      const outflows = [];

      entries.forEach(entry => {
        entry.entries.forEach(detail => {
          if (cashAccountIds.includes(detail.accountId)) {
            const amount = (detail.debit || 0) - (detail.credit || 0);
            const cashFlow = {
              date: entry.date,
              description: entry.description,
              reference: entry.reference,
              amount: Math.abs(amount),
              type: getCashFlowType(entry.type),
              entryNumber: entry.entryNumber
            };

            if (amount > 0) {
              inflows.push(cashFlow);
            } else if (amount < 0) {
              outflows.push(cashFlow);
            }
          }
        });
      });

      // حساب الرصيد الختامي
      const totalInflows = inflows.reduce((sum, flow) => sum + flow.amount, 0);
      const totalOutflows = outflows.reduce((sum, flow) => sum + flow.amount, 0);
      const closingBalance = openingBalance + totalInflows - totalOutflows;

      setCashFlowData({
        openingBalance,
        inflows: inflows.sort((a, b) => new Date(b.date) - new Date(a.date)),
        outflows: outflows.sort((a, b) => new Date(b.date) - new Date(a.date)),
        closingBalance
      });

    } catch (error) {
      console.error('خطأ في إنشاء تقرير التدفق النقدي:', error);
    } finally {
      setLoading(false);
    }
  };

  const getCashFlowType = (entryType) => {
    const types = {
      'sales-collection': 'تحصيل مبيعات',
      'purchase-payment': 'سداد مشتريات',
      'contract-expense': 'مصاريف عقود',
      'worker-payment': 'دفع عمالة',
      'manual': 'قيد يدوي'
    };
    return types[entryType] || 'أخرى';
  };

  const exportToExcel = () => {
    try {
      const workbook = XLSX.utils.book_new();

      // ملخص التدفق النقدي
      const summaryData = [
        ['تقرير التدفق النقدي'],
        [`من ${new Date(dateFrom).toLocaleDateString('ar-EG')} إلى ${new Date(dateTo).toLocaleDateString('ar-EG')}`],
        [''],
        ['الرصيد الافتتاحي', cashFlowData.openingBalance.toLocaleString('ar-EG')],
        [''],
        ['التدفقات الداخلة'],
        ...cashFlowData.inflows.map(flow => [
          new Date(flow.date).toLocaleDateString('ar-EG'),
          flow.description,
          flow.type,
          flow.amount.toLocaleString('ar-EG')
        ]),
        ['إجمالي التدفقات الداخلة', '', '', cashFlowData.inflows.reduce((sum, flow) => sum + flow.amount, 0).toLocaleString('ar-EG')],
        [''],
        ['التدفقات الخارجة'],
        ...cashFlowData.outflows.map(flow => [
          new Date(flow.date).toLocaleDateString('ar-EG'),
          flow.description,
          flow.type,
          flow.amount.toLocaleString('ar-EG')
        ]),
        ['إجمالي التدفقات الخارجة', '', '', cashFlowData.outflows.reduce((sum, flow) => sum + flow.amount, 0).toLocaleString('ar-EG')],
        [''],
        ['الرصيد الختامي', cashFlowData.closingBalance.toLocaleString('ar-EG')]
      ];

      const worksheet = XLSX.utils.aoa_to_sheet(summaryData);
      XLSX.utils.book_append_sheet(workbook, worksheet, 'التدفق النقدي');

      const fileName = `cash-flow-${dateFrom}-to-${dateTo}.xlsx`;
      XLSX.writeFile(workbook, fileName);
    } catch (error) {
      console.error('خطأ في تصدير التقرير:', error);
      alert('حدث خطأ أثناء تصدير التقرير');
    }
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '3rem' }}>
        <div>جاري إنشاء التقرير...</div>
      </div>
    );
  }

  const totalInflows = cashFlowData.inflows.reduce((sum, flow) => sum + flow.amount, 0);
  const totalOutflows = cashFlowData.outflows.reduce((sum, flow) => sum + flow.amount, 0);
  const netCashFlow = totalInflows - totalOutflows;

  return (
    <div>
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        marginBottom: '2rem' 
      }}>
        <h2 style={{ margin: 0, color: '#2c3e50' }}>💰 تقرير التدفق النقدي</h2>
        <button
          className="btn btn-success"
          onClick={exportToExcel}
        >
          📊 تصدير Excel
        </button>
      </div>

      {/* فلاتر التاريخ */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div className="card-title">فترة التقرير</div>
        
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem' }}>
          <div className="form-group">
            <label className="form-label">من تاريخ</label>
            <input
              type="date"
              className="form-control"
              value={dateFrom}
              onChange={(e) => setDateFrom(e.target.value)}
            />
          </div>

          <div className="form-group">
            <label className="form-label">إلى تاريخ</label>
            <input
              type="date"
              className="form-control"
              value={dateTo}
              onChange={(e) => setDateTo(e.target.value)}
            />
          </div>
        </div>
      </div>

      {/* ملخص التدفق النقدي */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div className="card-title">ملخص التدفق النقدي</div>
        
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem' }}>
          <div style={{ 
            padding: '1rem', 
            background: '#e3f2fd', 
            borderRadius: '5px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '0.9rem', color: '#666', marginBottom: '0.5rem' }}>الرصيد الافتتاحي</div>
            <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#1976d2' }}>
              {cashFlowData.openingBalance.toLocaleString('ar-EG')} ج.م
            </div>
          </div>

          <div style={{ 
            padding: '1rem', 
            background: '#e8f5e8', 
            borderRadius: '5px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '0.9rem', color: '#666', marginBottom: '0.5rem' }}>التدفقات الداخلة</div>
            <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#388e3c' }}>
              +{totalInflows.toLocaleString('ar-EG')} ج.م
            </div>
          </div>

          <div style={{ 
            padding: '1rem', 
            background: '#ffebee', 
            borderRadius: '5px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '0.9rem', color: '#666', marginBottom: '0.5rem' }}>التدفقات الخارجة</div>
            <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#d32f2f' }}>
              -{totalOutflows.toLocaleString('ar-EG')} ج.م
            </div>
          </div>

          <div style={{ 
            padding: '1rem', 
            background: netCashFlow >= 0 ? '#e8f5e8' : '#ffebee', 
            borderRadius: '5px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '0.9rem', color: '#666', marginBottom: '0.5rem' }}>صافي التدفق</div>
            <div style={{ 
              fontSize: '1.5rem', 
              fontWeight: 'bold', 
              color: netCashFlow >= 0 ? '#388e3c' : '#d32f2f' 
            }}>
              {netCashFlow >= 0 ? '+' : ''}{netCashFlow.toLocaleString('ar-EG')} ج.م
            </div>
          </div>

          <div style={{ 
            padding: '1rem', 
            background: '#fff3e0', 
            borderRadius: '5px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '0.9rem', color: '#666', marginBottom: '0.5rem' }}>الرصيد الختامي</div>
            <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#f57c00' }}>
              {cashFlowData.closingBalance.toLocaleString('ar-EG')} ج.م
            </div>
          </div>
        </div>
      </div>

      {/* التدفقات الداخلة */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div className="card-title">💚 التدفقات الداخلة ({cashFlowData.inflows.length})</div>
        
        {cashFlowData.inflows.length === 0 ? (
          <div style={{ textAlign: 'center', padding: '2rem', color: '#666' }}>
            لا توجد تدفقات داخلة في هذه الفترة
          </div>
        ) : (
          <div style={{ overflow: 'auto' }}>
            <table className="table">
              <thead>
                <tr>
                  <th>التاريخ</th>
                  <th>الوصف</th>
                  <th>النوع</th>
                  <th>المبلغ</th>
                  <th>رقم القيد</th>
                </tr>
              </thead>
              <tbody>
                {cashFlowData.inflows.map((flow, index) => (
                  <tr key={index}>
                    <td>{new Date(flow.date).toLocaleDateString('ar-EG')}</td>
                    <td>{flow.description}</td>
                    <td>
                      <span style={{
                        padding: '0.25rem 0.5rem',
                        borderRadius: '3px',
                        fontSize: '0.8rem',
                        background: '#e8f5e8',
                        color: '#388e3c'
                      }}>
                        {flow.type}
                      </span>
                    </td>
                    <td style={{ fontWeight: 'bold', color: '#388e3c' }}>
                      +{flow.amount.toLocaleString('ar-EG')} ج.م
                    </td>
                    <td>{flow.entryNumber}</td>
                  </tr>
                ))}
              </tbody>
              <tfoot>
                <tr style={{ background: '#f8f9fa', fontWeight: 'bold' }}>
                  <td colSpan="3">الإجمالي</td>
                  <td style={{ color: '#388e3c' }}>
                    +{totalInflows.toLocaleString('ar-EG')} ج.م
                  </td>
                  <td></td>
                </tr>
              </tfoot>
            </table>
          </div>
        )}
      </div>

      {/* التدفقات الخارجة */}
      <div className="card">
        <div className="card-title">❤️ التدفقات الخارجة ({cashFlowData.outflows.length})</div>
        
        {cashFlowData.outflows.length === 0 ? (
          <div style={{ textAlign: 'center', padding: '2rem', color: '#666' }}>
            لا توجد تدفقات خارجة في هذه الفترة
          </div>
        ) : (
          <div style={{ overflow: 'auto' }}>
            <table className="table">
              <thead>
                <tr>
                  <th>التاريخ</th>
                  <th>الوصف</th>
                  <th>النوع</th>
                  <th>المبلغ</th>
                  <th>رقم القيد</th>
                </tr>
              </thead>
              <tbody>
                {cashFlowData.outflows.map((flow, index) => (
                  <tr key={index}>
                    <td>{new Date(flow.date).toLocaleDateString('ar-EG')}</td>
                    <td>{flow.description}</td>
                    <td>
                      <span style={{
                        padding: '0.25rem 0.5rem',
                        borderRadius: '3px',
                        fontSize: '0.8rem',
                        background: '#ffebee',
                        color: '#d32f2f'
                      }}>
                        {flow.type}
                      </span>
                    </td>
                    <td style={{ fontWeight: 'bold', color: '#d32f2f' }}>
                      -{flow.amount.toLocaleString('ar-EG')} ج.م
                    </td>
                    <td>{flow.entryNumber}</td>
                  </tr>
                ))}
              </tbody>
              <tfoot>
                <tr style={{ background: '#f8f9fa', fontWeight: 'bold' }}>
                  <td colSpan="3">الإجمالي</td>
                  <td style={{ color: '#d32f2f' }}>
                    -{totalOutflows.toLocaleString('ar-EG')} ج.م
                  </td>
                  <td></td>
                </tr>
              </tfoot>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default CashFlowReports;
