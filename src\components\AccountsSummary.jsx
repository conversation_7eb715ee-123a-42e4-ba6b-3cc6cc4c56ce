import React, { useState, useEffect } from 'react';
import { db } from '../database/db';

const AccountsSummary = () => {
  const [summary, setSummary] = useState({
    totalAccounts: 0,
    accountsByType: {},
    accountsByLevel: {},
    recentAccounts: []
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadSummary();
  }, []);

  const loadSummary = async () => {
    try {
      setLoading(true);
      
      // إجمالي الحسابات
      const totalAccounts = await db.accounts.count();
      
      // الحسابات حسب النوع
      const allAccounts = await db.accounts.toArray();
      const accountsByType = allAccounts.reduce((acc, account) => {
        acc[account.type] = (acc[account.type] || 0) + 1;
        return acc;
      }, {});
      
      // الحسابات حسب المستوى
      const accountsByLevel = allAccounts.reduce((acc, account) => {
        acc[account.level] = (acc[account.level] || 0) + 1;
        return acc;
      }, {});
      
      // آخر الحسابات المضافة
      const recentAccounts = await db.accounts
        .orderBy('createdAt')
        .reverse()
        .limit(5)
        .toArray();

      setSummary({
        totalAccounts,
        accountsByType,
        accountsByLevel,
        recentAccounts
      });
    } catch (error) {
      console.error('خطأ في تحميل ملخص الحسابات:', error);
    } finally {
      setLoading(false);
    }
  };

  const getTypeColor = (type) => {
    const colors = {
      'أصول': '#007bff',
      'التزامات': '#dc3545',
      'حقوق ملكية': '#28a745',
      'إيرادات': '#17a2b8',
      'مصروفات': '#ffc107'
    };
    return colors[type] || '#6c757d';
  };

  if (loading) {
    return (
      <div className="loading">
        <div className="spinner"></div>
      </div>
    );
  }

  return (
    <div className="grid grid-2" style={{ marginBottom: '2rem' }}>
      {/* إحصائيات عامة */}
      <div className="card">
        <div className="card-title">إحصائيات الحسابات</div>
        
        <div style={{ marginBottom: '1.5rem' }}>
          <div style={{ 
            fontSize: '2rem', 
            fontWeight: 'bold', 
            color: '#007bff',
            textAlign: 'center',
            marginBottom: '0.5rem'
          }}>
            {summary.totalAccounts}
          </div>
          <div style={{ textAlign: 'center', color: '#666' }}>
            إجمالي الحسابات
          </div>
        </div>

        <div>
          <h4 style={{ marginBottom: '1rem', fontSize: '1.1rem' }}>التوزيع حسب النوع:</h4>
          {Object.entries(summary.accountsByType).map(([type, count]) => (
            <div key={type} style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              marginBottom: '0.5rem',
              padding: '0.5rem',
              background: '#f8f9fa',
              borderRadius: '5px'
            }}>
              <span style={{ 
                background: getTypeColor(type), 
                color: 'white', 
                padding: '0.25rem 0.5rem', 
                borderRadius: '3px',
                fontSize: '0.9rem'
              }}>
                {type}
              </span>
              <strong>{count} حساب</strong>
            </div>
          ))}
        </div>

        <div style={{ marginTop: '1.5rem' }}>
          <h4 style={{ marginBottom: '1rem', fontSize: '1.1rem' }}>التوزيع حسب المستوى:</h4>
          {Object.entries(summary.accountsByLevel).map(([level, count]) => (
            <div key={level} style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              marginBottom: '0.5rem',
              padding: '0.5rem',
              background: '#f8f9fa',
              borderRadius: '5px'
            }}>
              <span>المستوى {level}</span>
              <strong>{count} حساب</strong>
            </div>
          ))}
        </div>
      </div>

      {/* آخر الحسابات المضافة */}
      <div className="card">
        <div className="card-title">آخر الحسابات المضافة</div>
        
        {summary.recentAccounts.length > 0 ? (
          <div>
            {summary.recentAccounts.map(account => (
              <div key={account.id} style={{
                padding: '0.75rem',
                border: '1px solid #dee2e6',
                borderRadius: '5px',
                marginBottom: '0.5rem',
                background: '#f8f9fa'
              }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <div>
                    <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>
                      {account.code} - {account.name}
                    </div>
                    <div style={{ fontSize: '0.9rem', color: '#666' }}>
                      <span style={{ 
                        background: getTypeColor(account.type), 
                        color: 'white', 
                        padding: '0.2rem 0.4rem', 
                        borderRadius: '3px',
                        fontSize: '0.8rem',
                        marginLeft: '0.5rem'
                      }}>
                        {account.type}
                      </span>
                      المستوى {account.level}
                    </div>
                  </div>
                  <div style={{ fontSize: '0.8rem', color: '#999' }}>
                    {new Date(account.createdAt).toLocaleDateString('ar-EG')}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div style={{ textAlign: 'center', color: '#666', padding: '2rem' }}>
            لا توجد حسابات مضافة حديثاً
          </div>
        )}
      </div>
    </div>
  );
};

export default AccountsSummary;
