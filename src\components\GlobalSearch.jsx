import React, { useState, useEffect } from 'react';
import { db } from '../database/db';

const GlobalSearch = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState({});
  const [loading, setLoading] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('all');

  const searchCategories = {
    all: 'جميع البيانات',
    customers: 'العملاء',
    suppliers: 'الموردين',
    items: 'الأصناف',
    contracts: 'العقود',
    workers: 'العمال',
    invoices: 'الفواتير',
    expenses: 'المصروفات'
  };

  useEffect(() => {
    if (searchTerm.length >= 2) {
      performSearch();
    } else {
      setSearchResults({});
    }
  }, [searchTerm, selectedCategory]);

  const performSearch = async () => {
    try {
      setLoading(true);
      const results = {};

      if (selectedCategory === 'all' || selectedCategory === 'customers') {
        const customers = await db.customers
          .filter(item => 
            item.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            item.code?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            item.phone?.includes(searchTerm) ||
            item.email?.toLowerCase().includes(searchTerm.toLowerCase())
          )
          .toArray();
        if (customers.length > 0) results.customers = customers;
      }

      if (selectedCategory === 'all' || selectedCategory === 'suppliers') {
        const suppliers = await db.suppliers
          .filter(item => 
            item.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            item.code?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            item.phone?.includes(searchTerm) ||
            item.email?.toLowerCase().includes(searchTerm.toLowerCase())
          )
          .toArray();
        if (suppliers.length > 0) results.suppliers = suppliers;
      }

      if (selectedCategory === 'all' || selectedCategory === 'items') {
        const items = await db.items
          .filter(item => 
            item.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            item.code?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            item.description?.toLowerCase().includes(searchTerm.toLowerCase())
          )
          .toArray();
        if (items.length > 0) results.items = items;
      }

      if (selectedCategory === 'all' || selectedCategory === 'contracts') {
        const contracts = await db.contracts
          .filter(item => 
            item.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            item.contractNumber?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            item.description?.toLowerCase().includes(searchTerm.toLowerCase())
          )
          .toArray();
        if (contracts.length > 0) results.contracts = contracts;
      }

      if (selectedCategory === 'all' || selectedCategory === 'workers') {
        const workers = await db.workers
          .filter(item => 
            item.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            item.code?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            item.phone?.includes(searchTerm)
          )
          .toArray();
        if (workers.length > 0) results.workers = workers;
      }

      if (selectedCategory === 'all' || selectedCategory === 'invoices') {
        const salesInvoices = await db.salesInvoices
          .filter(item => 
            item.invoiceNumber?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            item.notes?.toLowerCase().includes(searchTerm.toLowerCase())
          )
          .toArray();
        
        const purchaseInvoices = await db.purchaseInvoices
          .filter(item => 
            item.invoiceNumber?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            item.notes?.toLowerCase().includes(searchTerm.toLowerCase())
          )
          .toArray();

        if (salesInvoices.length > 0 || purchaseInvoices.length > 0) {
          results.invoices = { sales: salesInvoices, purchase: purchaseInvoices };
        }
      }

      if (selectedCategory === 'all' || selectedCategory === 'expenses') {
        const expenses = await db.contractExpenses
          .filter(item => 
            item.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            item.reference?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            item.voucherNumber?.toLowerCase().includes(searchTerm.toLowerCase())
          )
          .toArray();
        if (expenses.length > 0) results.expenses = expenses;
      }

      setSearchResults(results);
    } catch (error) {
      console.error('خطأ في البحث:', error);
    } finally {
      setLoading(false);
    }
  };

  const getTotalResults = () => {
    return Object.values(searchResults).reduce((total, category) => {
      if (Array.isArray(category)) {
        return total + category.length;
      } else if (category.sales && category.purchase) {
        return total + category.sales.length + category.purchase.length;
      }
      return total;
    }, 0);
  };

  const renderCustomers = (customers) => (
    <div className="search-category">
      <h4>👥 العملاء ({customers.length})</h4>
      <div className="search-results">
        {customers.map(customer => (
          <div key={customer.id} className="search-result-item">
            <div className="result-header">
              <strong>{customer.name}</strong>
              <span className="result-code">{customer.code}</span>
            </div>
            <div className="result-details">
              {customer.phone && <span>📞 {customer.phone}</span>}
              {customer.email && <span>📧 {customer.email}</span>}
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderSuppliers = (suppliers) => (
    <div className="search-category">
      <h4>🏢 الموردين ({suppliers.length})</h4>
      <div className="search-results">
        {suppliers.map(supplier => (
          <div key={supplier.id} className="search-result-item">
            <div className="result-header">
              <strong>{supplier.name}</strong>
              <span className="result-code">{supplier.code}</span>
            </div>
            <div className="result-details">
              {supplier.phone && <span>📞 {supplier.phone}</span>}
              {supplier.email && <span>📧 {supplier.email}</span>}
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderItems = (items) => (
    <div className="search-category">
      <h4>📦 الأصناف ({items.length})</h4>
      <div className="search-results">
        {items.map(item => (
          <div key={item.id} className="search-result-item">
            <div className="result-header">
              <strong>{item.name}</strong>
              <span className="result-code">{item.code}</span>
            </div>
            <div className="result-details">
              <span>💰 {item.sellingPrice?.toLocaleString('ar-EG')} ج.م</span>
              <span>📊 متاح: {item.currentStock} {item.unit}</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderContracts = (contracts) => (
    <div className="search-category">
      <h4>📄 العقود ({contracts.length})</h4>
      <div className="search-results">
        {contracts.map(contract => (
          <div key={contract.id} className="search-result-item">
            <div className="result-header">
              <strong>{contract.name}</strong>
              <span className="result-code">{contract.contractNumber}</span>
            </div>
            <div className="result-details">
              <span>💰 {contract.totalValue?.toLocaleString('ar-EG')} ج.م</span>
              <span className={`status-badge status-${contract.status}`}>
                {contract.status === 'active' ? 'نشط' : 
                 contract.status === 'completed' ? 'مكتمل' : 
                 contract.status === 'cancelled' ? 'ملغي' : 'معلق'}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderWorkers = (workers) => (
    <div className="search-category">
      <h4>👷 العمال ({workers.length})</h4>
      <div className="search-results">
        {workers.map(worker => (
          <div key={worker.id} className="search-result-item">
            <div className="result-header">
              <strong>{worker.name}</strong>
              <span className="result-code">{worker.code}</span>
            </div>
            <div className="result-details">
              {worker.phone && <span>📞 {worker.phone}</span>}
              <span>💰 {worker.dailyRate?.toLocaleString('ar-EG')} ج.م/يوم</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className="container">
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h2 style={{ margin: 0, color: '#333' }}>🔍 البحث الشامل</h2>
        </div>
      </div>

      <div className="card" style={{ marginBottom: '2rem' }}>
        <div className="grid grid-2">
          <div className="form-group">
            <label className="form-label">البحث</label>
            <input
              type="text"
              className="form-control"
              placeholder="ابحث في جميع البيانات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{ fontSize: '1.1rem', padding: '0.75rem' }}
            />
          </div>

          <div className="form-group">
            <label className="form-label">فئة البحث</label>
            <select
              className="form-control"
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
            >
              {Object.keys(searchCategories).map(key => (
                <option key={key} value={key}>
                  {searchCategories[key]}
                </option>
              ))}
            </select>
          </div>
        </div>

        {searchTerm.length > 0 && searchTerm.length < 2 && (
          <div style={{ 
            background: '#fff3cd', 
            border: '1px solid #ffeaa7', 
            borderRadius: '5px', 
            padding: '1rem',
            marginTop: '1rem'
          }}>
            <div style={{ color: '#856404' }}>
              يرجى إدخال حرفين على الأقل للبحث
            </div>
          </div>
        )}
      </div>

      {loading && (
        <div className="card">
          <div style={{ textAlign: 'center', padding: '2rem' }}>
            <div className="spinner"></div>
            <div>جاري البحث...</div>
          </div>
        </div>
      )}

      {!loading && searchTerm.length >= 2 && (
        <div className="card">
          <div className="card-title">
            نتائج البحث ({getTotalResults()})
          </div>

          {getTotalResults() === 0 ? (
            <div style={{ textAlign: 'center', color: '#666', padding: '2rem' }}>
              لا توجد نتائج للبحث "{searchTerm}"
            </div>
          ) : (
            <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
              {searchResults.customers && renderCustomers(searchResults.customers)}
              {searchResults.suppliers && renderSuppliers(searchResults.suppliers)}
              {searchResults.items && renderItems(searchResults.items)}
              {searchResults.contracts && renderContracts(searchResults.contracts)}
              {searchResults.workers && renderWorkers(searchResults.workers)}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default GlobalSearch;
