import React, { useState, useEffect } from 'react';
import { db, dbHelpers, resetDatabase } from '../database/db';

const WorkerVouchers = ({ onRefresh }) => {
  console.log('تم تحميل مكون أذون العمال');

  const [workers, setWorkers] = useState([]);
  const [selectedWorker, setSelectedWorker] = useState('');
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');
  const [unpaidSessions, setUnpaidSessions] = useState([]);
  const [selectedSessions, setSelectedSessions] = useState([]);
  const [vouchers, setVouchers] = useState([]);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [loading, setLoading] = useState(false);
  const [currentView, setCurrentView] = useState('create'); // 'create', 'list', or 'edit'
  const [editingVoucher, setEditingVoucher] = useState(null);

  useEffect(() => {
    loadWorkers();
    loadVouchers();
    
    // تعيين التواريخ الافتراضية (آخر شهر)
    const today = new Date();
    const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
    setDateFrom(lastMonth.toISOString().split('T')[0]);
    setDateTo(today.toISOString().split('T')[0]);
  }, []);

  const loadWorkers = async () => {
    try {
      console.log('بدء تحميل العمال...');

      // تحميل جميع العمال (نشطين وغير نشطين)
      const allWorkers = await db.workers.toArray();
      console.log('جميع العمال:', allWorkers);

      // فلترة العمال النشطين فقط
      const activeWorkers = allWorkers.filter(worker => worker.isActive);
      console.log('العمال النشطين:', activeWorkers);

      setWorkers(activeWorkers);

      // إذا لم يوجد عمال، إنشاء عمال تجريبيين
      if (allWorkers.length === 0) {
        console.log('لا يوجد عمال، سيتم إنشاء عمال تجريبيين...');
        await createSampleWorkers();
        // إعادة تحميل العمال بعد الإنشاء
        const newWorkers = await db.workers.where('isActive').equals(true).toArray();
        setWorkers(newWorkers);
      }

    } catch (error) {
      console.error('خطأ في تحميل العمال:', error);
      alert('حدث خطأ في تحميل العمال: ' + error.message);
    }
  };

  const loadVouchers = async () => {
    try {
      const allVouchers = await db.workerVouchers.orderBy('createdAt').reverse().toArray();
      
      // إضافة بيانات العامل لكل إذن
      const vouchersWithWorkers = await Promise.all(
        allVouchers.map(async (voucher) => {
          const worker = await db.workers.get(voucher.workerId);
          return {
            ...voucher,
            workerName: worker?.name || 'غير معروف'
          };
        })
      );
      
      setVouchers(vouchersWithWorkers);
    } catch (error) {
      console.error('خطأ في تحميل الأذون:', error);
    }
  };

  const loadUnpaidSessions = async () => {
    if (!selectedWorker || !dateFrom || !dateTo) return;

    try {
      setLoading(true);

      const fromDate = new Date(dateFrom);
      const toDate = new Date(dateTo);

      // التحقق من وجود جدول workSessions
      const sessionsCount = await db.workSessions.count();
      console.log('عدد جلسات العمل في قاعدة البيانات:', sessionsCount);

      if (sessionsCount === 0) {
        // إنشاء بيانات تجريبية إذا لم توجد
        await createSampleWorkSessions();
      }

      // البحث عن التشغيلات غير المدفوعة للعامل في الفترة المحددة
      let sessions = [];
      try {
        if (db.workSessions) {
          const allSessions = await db.workSessions.toArray();
          sessions = allSessions.filter(session => {
            const sessionDate = new Date(session.date);
            return session.workerId === parseInt(selectedWorker) &&
                   sessionDate >= fromDate &&
                   sessionDate <= toDate &&
                   !session.isPaid;
          });
        }
      } catch (error) {
        console.warn('تعذر تحميل جلسات العمل:', error);
        sessions = [];
      }

      // إضافة بيانات الموقع والعقد لكل تشغيل
      const sessionsWithDetails = await Promise.all(
        sessions.map(async (session) => {
          const contract = await db.contracts.get(session.contractId);
          return {
            ...session,
            contractName: contract?.name || 'غير معروف',
            siteName: contract?.siteName || 'غير معروف'
          };
        })
      );

      setUnpaidSessions(sessionsWithDetails);
      setSelectedSessions([]); // مسح التحديد السابق

      console.log(`تم العثور على ${sessionsWithDetails.length} تشغيل غير مدفوع`);
    } catch (error) {
      console.error('خطأ في تحميل التشغيلات:', error);
      alert('حدث خطأ في تحميل التشغيلات: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSessionSelect = (sessionId, isSelected) => {
    if (isSelected) {
      setSelectedSessions([...selectedSessions, sessionId]);
    } else {
      setSelectedSessions(selectedSessions.filter(id => id !== sessionId));
    }
  };

  const selectAllSessions = () => {
    setSelectedSessions(unpaidSessions.map(session => session.id));
  };

  const clearSelection = () => {
    setSelectedSessions([]);
  };

  const calculateTotalAmount = () => {
    return selectedSessions.reduce((total, sessionId) => {
      const session = unpaidSessions.find(s => s.id === sessionId);
      return total + (session?.totalAmount || 0);
    }, 0);
  };

  const generateVoucherNumber = async () => {
    const year = new Date().getFullYear();
    const count = await db.workerVouchers.where('voucherNumber').startsWith(`إذن-${year}-`).count();
    return `إذن-${year}-${String(count + 1).padStart(3, '0')}`;
  };

  const createVoucher = async () => {
    if (selectedSessions.length === 0) {
      alert('يرجى اختيار تشغيلات لإنشاء الإذن');
      return;
    }

    try {
      setLoading(true);
      
      const voucherNumber = await generateVoucherNumber();
      const totalAmount = calculateTotalAmount();
      const worker = workers.find(w => w.id === parseInt(selectedWorker));
      
      // إنشاء الإذن
      const voucherId = await db.workerVouchers.add({
        voucherNumber,
        workerId: parseInt(selectedWorker),
        workerName: worker.name,
        dateFrom,
        dateTo,
        sessionIds: selectedSessions,
        totalAmount,
        status: 'pending', // pending, approved, paid, cancelled
        createdAt: new Date(),
        createdBy: 'النظام' // يمكن ربطه بالمستخدم الحالي
      });

      // تحديث حالة التشغيلات إلى مدفوعة
      for (const sessionId of selectedSessions) {
        await db.workSessions.update(sessionId, { 
          isPaid: true,
          voucherId: voucherId,
          paidAt: new Date()
        });
      }

      // إنشاء القيد المحاسبي
      await createAccountingEntry(voucherId, voucherNumber, totalAmount, worker.name);

      alert('تم إنشاء الإذن بنجاح');
      
      // إعادة تحميل البيانات
      await loadVouchers();
      await loadUnpaidSessions();
      setSelectedSessions([]);
      
      if (onRefresh) onRefresh();
      
    } catch (error) {
      console.error('خطأ في إنشاء الإذن:', error);
      alert('حدث خطأ أثناء إنشاء الإذن');
    } finally {
      setLoading(false);
    }
  };

  const createAccountingEntry = async (voucherId, voucherNumber, amount, workerName) => {
    try {
      // إنشاء قيد محاسبي: من رواتب العمالة إلى مصروفات مستحقة
      const entryNumber = await dbHelpers.generateJournalEntryNumber();
      
      const entryId = await db.journalEntries.add({
        entryNumber,
        date: new Date(),
        description: `إذن دفع عامل - ${workerName} - ${voucherNumber}`,
        reference: voucherNumber,
        type: 'worker_voucher',
        isPosted: false,
        createdAt: new Date()
      });

      // الطرف المدين: رواتب العمالة (5020101)
      await db.journalEntryDetails.add({
        entryId,
        accountId: await getAccountByCode('5020101'), // رواتب العمالة
        description: `رواتب عامل - ${workerName}`,
        debit: amount,
        credit: 0
      });

      // الطرف الدائن: مصروفات مستحقة (2010201)
      await db.journalEntryDetails.add({
        entryId,
        accountId: await getAccountByCode('2010201'), // مصروفات مستحقة
        description: `مستحقات عامل - ${workerName}`,
        debit: 0,
        credit: amount
      });

      // ربط الإذن بالقيد المحاسبي
      await db.workerVouchers.update(voucherId, { journalEntryId: entryId });

    } catch (error) {
      console.error('خطأ في إنشاء القيد المحاسبي:', error);
    }
  };

  const getAccountByCode = async (code) => {
    const account = await db.accounts.where('code').equals(code).first();
    return account?.id || null;
  };

  const createSampleWorkers = async () => {
    try {
      console.log('إنشاء عمال تجريبيين...');

      const sampleWorkers = [
        {
          code: 'W001',
          name: 'أحمد محمد',
          phone: '***********',
          address: 'القاهرة',
          dailyRate: 200,
          hourlyRate: 25,
          isActive: true,
          createdAt: new Date()
        },
        {
          code: 'W002',
          name: 'محمد علي',
          phone: '***********',
          address: 'الجيزة',
          dailyRate: 180,
          hourlyRate: 22,
          isActive: true,
          createdAt: new Date()
        },
        {
          code: 'W003',
          name: 'علي حسن',
          phone: '***********',
          address: 'الإسكندرية',
          dailyRate: 220,
          hourlyRate: 28,
          isActive: true,
          createdAt: new Date()
        }
      ];

      for (const worker of sampleWorkers) {
        await db.workers.add(worker);
      }

      console.log(`تم إنشاء ${sampleWorkers.length} عامل تجريبي`);
    } catch (error) {
      console.error('خطأ في إنشاء العمال التجريبيين:', error);
    }
  };

  const createSampleContracts = async () => {
    try {
      console.log('إنشاء عقود تجريبية...');

      const sampleContracts = [
        {
          contractNumber: 'C001',
          name: 'مشروع البناء الأول',
          siteName: 'موقع القاهرة الجديدة',
          clientName: 'شركة التطوير العقاري',
          startDate: new Date('2024-01-01'),
          endDate: new Date('2024-12-31'),
          totalValue: 1000000,
          status: 'active',
          createdAt: new Date()
        },
        {
          contractNumber: 'C002',
          name: 'مشروع الصيانة',
          siteName: 'موقع الجيزة',
          clientName: 'شركة الإنشاءات',
          startDate: new Date('2024-02-01'),
          endDate: new Date('2024-11-30'),
          totalValue: 500000,
          status: 'active',
          createdAt: new Date()
        }
      ];

      for (const contract of sampleContracts) {
        await db.contracts.add(contract);
      }

      console.log(`تم إنشاء ${sampleContracts.length} عقد تجريبي`);
    } catch (error) {
      console.error('خطأ في إنشاء العقود التجريبية:', error);
    }
  };

  const createSampleWorkSessions = async () => {
    try {
      console.log('إنشاء بيانات تجريبية لجلسات العمل...');

      // الحصول على العمال والعقود
      const allWorkers = await db.workers.toArray();
      const allContracts = await db.contracts.toArray();

      if (allWorkers.length === 0) {
        console.log('لا توجد عمال لإنشاء جلسات العمل');
        return;
      }

      if (allContracts.length === 0) {
        console.log('لا توجد عقود، سيتم إنشاء عقود تجريبية...');
        await createSampleContracts();
        // إعادة تحميل العقود
        const newContracts = await db.contracts.toArray();
        if (newContracts.length === 0) {
          console.log('فشل في إنشاء العقود التجريبية');
          return;
        }
      }

      // إنشاء جلسات عمل تجريبية
      const sampleSessions = [];
      const today = new Date();

      for (let i = 0; i < 10; i++) {
        const worker = allWorkers[Math.floor(Math.random() * allWorkers.length)];
        const contract = allContracts[Math.floor(Math.random() * allContracts.length)];
        const sessionDate = new Date(today.getTime() - (Math.random() * 30 * 24 * 60 * 60 * 1000)); // آخر 30 يوم
        const hoursWorked = Math.floor(Math.random() * 8) + 1; // 1-8 ساعات
        const hourlyRate = 50 + Math.floor(Math.random() * 50); // 50-100 ج.م/ساعة

        sampleSessions.push({
          workerId: worker.id,
          contractId: contract.id,
          date: sessionDate,
          hoursWorked,
          hourlyRate,
          totalAmount: hoursWorked * hourlyRate,
          notes: `تشغيل تجريبي ${i + 1}`,
          isPaid: false,
          createdAt: new Date()
        });
      }

      // إضافة الجلسات إلى قاعدة البيانات
      for (const session of sampleSessions) {
        await db.workSessions.add(session);
      }

      console.log(`تم إنشاء ${sampleSessions.length} جلسة عمل تجريبية`);
    } catch (error) {
      console.error('خطأ في إنشاء البيانات التجريبية:', error);
    }
  };

  const updateVoucherStatus = async (voucherId, newStatus) => {
    try {
      await db.workerVouchers.update(voucherId, { 
        status: newStatus,
        updatedAt: new Date()
      });
      
      if (newStatus === 'paid') {
        // إذا تم الدفع، ترحيل القيد المحاسبي
        const voucher = await db.workerVouchers.get(voucherId);
        if (voucher.journalEntryId) {
          await db.journalEntries.update(voucher.journalEntryId, { isPosted: true });
        }
      }
      
      await loadVouchers();
      alert('تم تحديث حالة الإذن بنجاح');
    } catch (error) {
      console.error('خطأ في تحديث حالة الإذن:', error);
      alert('حدث خطأ أثناء تحديث حالة الإذن');
    }
  };

  const printVoucher = async (voucher) => {
    try {
      // تحميل التشغيلات المرتبطة بالإذن
      const voucherSessions = await db.workSessions
        .where('voucherId')
        .equals(voucher.id)
        .toArray();

      // إضافة تفاصيل العقود والمواقع
      const sessionsWithDetails = await Promise.all(
        voucherSessions.map(async (session) => {
          const contract = await db.contracts.get(session.contractId);
          return {
            ...session,
            contractName: contract?.name || 'غير معروف',
            siteName: contract?.siteName || 'غير معروف'
          };
        })
      );

      const printContent = generateVoucherPrintContent(voucher, sessionsWithDetails);

      const printWindow = window.open('', '_blank');
      printWindow.document.write(printContent);
      printWindow.document.close();
      printWindow.focus();

      setTimeout(() => {
        printWindow.print();
      }, 250);
    } catch (error) {
      console.error('خطأ في طباعة الإذن:', error);
      alert('حدث خطأ أثناء طباعة الإذن');
    }
  };

  const generateVoucherPrintContent = (voucher, sessions = []) => {
    const sessionsTableRows = sessions.map(session => `
      <tr>
        <td>${new Date(session.date).toLocaleDateString('ar-EG')}</td>
        <td>${session.siteName}</td>
        <td>${session.contractName}</td>
        <td>${session.hoursWorked}</td>
        <td>${session.hourlyRate.toLocaleString('ar-EG')}</td>
        <td style="font-weight: bold;">${session.totalAmount.toLocaleString('ar-EG')}</td>
        <td>${session.notes || '-'}</td>
      </tr>
    `).join('');

    return `
      <!DOCTYPE html>
      <html dir="rtl">
      <head>
        <meta charset="UTF-8">
        <title>إذن دفع عامل - ${voucher.voucherNumber}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
          .header h1 { color: #333; margin-bottom: 10px; }
          .voucher-info { display: flex; justify-content: space-between; margin-bottom: 20px; }
          .voucher-info div { background: #f8f9fa; padding: 10px; border-radius: 5px; }
          .sessions-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
          .sessions-table th, .sessions-table td { border: 1px solid #ddd; padding: 8px; text-align: right; }
          .sessions-table th { background-color: #f8f9fa; font-weight: bold; }
          .total { text-align: center; font-size: 1.5rem; font-weight: bold; color: #007bff; margin: 20px 0; }
          .signatures { display: flex; justify-content: space-between; margin-top: 50px; }
          .signature { text-align: center; width: 200px; }
          .signature-line { border-top: 1px solid #333; margin-top: 50px; padding-top: 10px; }
          @media print { body { margin: 0; } }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>إذن دفع عامل</h1>
          <p>رقم الإذن: ${voucher.voucherNumber}</p>
        </div>

        <div class="voucher-info">
          <div>
            <strong>اسم العامل:</strong> ${voucher.workerName}<br>
            <strong>الفترة:</strong> من ${voucher.dateFrom} إلى ${voucher.dateTo}
          </div>
          <div>
            <strong>تاريخ الإنشاء:</strong> ${new Date(voucher.createdAt).toLocaleDateString('ar-EG')}<br>
            <strong>الحالة:</strong> ${getStatusText(voucher.status)}
          </div>
        </div>

        ${sessions.length > 0 ? `
        <h3 style="margin-top: 30px; margin-bottom: 15px; color: #333;">تفاصيل التشغيلات:</h3>
        <table class="sessions-table">
          <thead>
            <tr>
              <th>التاريخ</th>
              <th>الموقع</th>
              <th>العقد</th>
              <th>ساعات العمل</th>
              <th>أجر الساعة (ج.م)</th>
              <th>إجمالي المبلغ (ج.م)</th>
              <th>الملاحظات</th>
            </tr>
          </thead>
          <tbody>
            ${sessionsTableRows}
          </tbody>
        </table>
        ` : ''}

        <div class="total">
          إجمالي المبلغ: ${voucher.totalAmount.toLocaleString('ar-EG')} ج.م
        </div>

        <div class="signatures">
          <div class="signature">
            <div class="signature-line">المحاسب</div>
          </div>
          <div class="signature">
            <div class="signature-line">المدير المالي</div>
          </div>
          <div class="signature">
            <div class="signature-line">العامل</div>
          </div>
        </div>
      </body>
      </html>
    `;
  };

  const getStatusText = (status) => {
    const statusMap = {
      'pending': 'معلق',
      'approved': 'معتمد',
      'paid': 'مدفوع',
      'cancelled': 'ملغي'
    };
    return statusMap[status] || status;
  };

  const getStatusColor = (status) => {
    const colorMap = {
      'pending': '#ffc107',
      'approved': '#17a2b8',
      'paid': '#28a745',
      'cancelled': '#dc3545'
    };
    return colorMap[status] || '#6c757d';
  };

  const openVoucherForEdit = async (voucher) => {
    try {
      console.log('فتح الإذن للتعديل:', voucher);

      // تحميل التشغيلات المرتبطة بالإذن
      const voucherSessions = await db.workSessions
        .where('voucherId')
        .equals(voucher.id)
        .toArray();

      // إضافة تفاصيل العقود والمواقع
      const sessionsWithDetails = await Promise.all(
        voucherSessions.map(async (session) => {
          const contract = await db.contracts.get(session.contractId);
          return {
            ...session,
            contractName: contract?.name || 'غير معروف',
            siteName: contract?.siteName || 'غير معروف'
          };
        })
      );

      // تعيين بيانات التعديل
      setEditingVoucher({
        ...voucher,
        sessions: sessionsWithDetails
      });
      setSelectedWorker(voucher.workerId.toString());
      setDateFrom(voucher.dateFrom);
      setDateTo(voucher.dateTo);
      setSelectedSessions(voucher.sessionIds || []);
      setUnpaidSessions(sessionsWithDetails);
      setCurrentView('edit');

    } catch (error) {
      console.error('خطأ في فتح الإذن للتعديل:', error);
      alert('حدث خطأ أثناء فتح الإذن للتعديل');
    }
  };

  const saveVoucherChanges = async () => {
    if (!editingVoucher || selectedSessions.length === 0) {
      alert('يرجى اختيار تشغيلات لحفظ التعديلات');
      return;
    }

    try {
      setLoading(true);

      const totalAmount = calculateTotalAmount();

      // تحديث الإذن
      await db.workerVouchers.update(editingVoucher.id, {
        sessionIds: selectedSessions,
        totalAmount,
        updatedAt: new Date()
      });

      // إعادة تعيين حالة التشغيلات القديمة
      const oldSessions = editingVoucher.sessionIds || [];
      for (const sessionId of oldSessions) {
        if (!selectedSessions.includes(sessionId)) {
          await db.workSessions.update(sessionId, {
            isPaid: false,
            voucherId: null,
            paidAt: null
          });
        }
      }

      // تحديث التشغيلات الجديدة
      for (const sessionId of selectedSessions) {
        if (!oldSessions.includes(sessionId)) {
          await db.workSessions.update(sessionId, {
            isPaid: true,
            voucherId: editingVoucher.id,
            paidAt: new Date()
          });
        }
      }

      alert('تم حفظ التعديلات بنجاح');

      // العودة لقائمة الأذون
      setCurrentView('list');
      setEditingVoucher(null);
      await loadVouchers();

    } catch (error) {
      console.error('خطأ في حفظ التعديلات:', error);
      alert('حدث خطأ أثناء حفظ التعديلات');
    } finally {
      setLoading(false);
    }
  };

  const cancelEdit = () => {
    setCurrentView('list');
    setEditingVoucher(null);
    setSelectedWorker('');
    setDateFrom('');
    setDateTo('');
    setSelectedSessions([]);
    setUnpaidSessions([]);
  };

  return (
    <div>
      {/* شريط التنقل */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div className="card-title">
          {currentView === 'edit' ? `تعديل الإذن - ${editingVoucher?.voucherNumber}` : 'أذون العمال'}
        </div>
        <div style={{ display: 'flex', gap: '1rem' }}>
          {currentView === 'edit' ? (
            <>
              <button
                className="btn btn-secondary"
                onClick={cancelEdit}
                style={{ padding: '1rem', fontSize: '1rem' }}
              >
                ← العودة لقائمة الأذون
              </button>
              <button
                className="btn btn-success"
                onClick={saveVoucherChanges}
                disabled={loading || selectedSessions.length === 0}
                style={{ padding: '1rem', fontSize: '1rem' }}
              >
                {loading ? 'جاري الحفظ...' : '💾 حفظ التعديلات'}
              </button>
            </>
          ) : (
            <>
              <button
                className={`btn ${currentView === 'create' ? 'btn-primary' : 'btn-secondary'}`}
                onClick={() => {
                  setCurrentView('create');
                  setEditingVoucher(null);
                }}
                style={{ padding: '1rem', fontSize: '1rem' }}
              >
                ➕ إنشاء إذن جديد
              </button>
              <button
                className={`btn ${currentView === 'list' ? 'btn-primary' : 'btn-secondary'}`}
                onClick={() => {
                  setCurrentView('list');
                  setEditingVoucher(null);
                }}
                style={{ padding: '1rem', fontSize: '1rem' }}
              >
                📋 قائمة الأذون
              </button>
            </>
          )}
        </div>

      </div>

      {currentView === 'create' || currentView === 'edit' ? (
        <div>

          {/* نموذج إنشاء إذن جديد */}
          <div className="card" style={{ marginBottom: '2rem' }}>
            <div className="card-title">
              {currentView === 'edit' ? `تعديل إذن دفع عامل - ${editingVoucher?.voucherNumber}` : 'إنشاء إذن دفع عامل'}
            </div>
            
            <div className="grid grid-3">
              <div className="form-group">
                <label className="form-label">العامل</label>
                <select
                  className="form-control"
                  value={selectedWorker}
                  onChange={(e) => setSelectedWorker(e.target.value)}
                >
                  <option value="">اختر العامل</option>
                  {workers.map(worker => (
                    <option key={worker.id} value={worker.id}>
                      {worker.name} - {worker.code}
                    </option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label className="form-label">من تاريخ</label>
                <input
                  type="date"
                  className="form-control"
                  value={dateFrom}
                  onChange={(e) => setDateFrom(e.target.value)}
                />
              </div>

              <div className="form-group">
                <label className="form-label">إلى تاريخ</label>
                <input
                  type="date"
                  className="form-control"
                  value={dateTo}
                  onChange={(e) => setDateTo(e.target.value)}
                />
              </div>
            </div>

            <div style={{ marginTop: '1rem' }}>
              <button
                className="btn btn-primary"
                onClick={loadUnpaidSessions}
                disabled={!selectedWorker || !dateFrom || !dateTo || loading}
              >
                {loading ? 'جاري البحث...' : 'البحث عن التشغيلات'}
              </button>
            </div>
          </div>

          {/* قائمة التشغيلات غير المدفوعة */}
          {unpaidSessions.length > 0 && (
            <div className="card">
              <div className="card-title">
                {currentView === 'edit' ?
                  `تشغيلات الإذن ({unpaidSessions.length} تشغيل)` :
                  `التشغيلات غير المدفوعة ({unpaidSessions.length} تشغيل)`
                }
              </div>

              <div style={{ marginBottom: '1rem', display: 'flex', gap: '1rem' }}>
                <button className="btn btn-secondary" onClick={selectAllSessions}>
                  تحديد الكل
                </button>
                <button className="btn btn-secondary" onClick={clearSelection}>
                  مسح التحديد
                </button>
                <div style={{ marginRight: 'auto', fontSize: '1.1rem', fontWeight: 'bold' }}>
                  المحدد: {selectedSessions.length} تشغيل - 
                  إجمالي المبلغ: {calculateTotalAmount().toLocaleString('ar-EG')} ج.م
                </div>
              </div>

              <div style={{ overflow: 'auto' }}>
                <table className="table">
                  <thead>
                    <tr>
                      <th width="50">تحديد</th>
                      <th>التاريخ</th>
                      <th>الموقع</th>
                      <th>العقد</th>
                      <th>ساعات العمل</th>
                      <th>أجر الساعة</th>
                      <th>إجمالي المبلغ</th>
                      <th>الملاحظات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {unpaidSessions.map(session => (
                      <tr key={session.id}>
                        <td>
                          <input
                            type="checkbox"
                            checked={selectedSessions.includes(session.id)}
                            onChange={(e) => handleSessionSelect(session.id, e.target.checked)}
                          />
                        </td>
                        <td>{new Date(session.date).toLocaleDateString('ar-EG')}</td>
                        <td>{session.siteName}</td>
                        <td>{session.contractName}</td>
                        <td>{session.hoursWorked}</td>
                        <td>{session.hourlyRate.toLocaleString('ar-EG')} ج.م</td>
                        <td style={{ fontWeight: 'bold', color: '#007bff' }}>
                          {session.totalAmount.toLocaleString('ar-EG')} ج.م
                        </td>
                        <td>{session.notes || '-'}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {selectedSessions.length > 0 && (
                <div style={{ marginTop: '1rem', textAlign: 'center' }}>
                  <button
                    className="btn btn-success"
                    onClick={currentView === 'edit' ? saveVoucherChanges : createVoucher}
                    disabled={loading}
                  >
                    {loading ?
                      (currentView === 'edit' ? 'جاري الحفظ...' : 'جاري الإنشاء...') :
                      (currentView === 'edit' ? 'حفظ التعديلات' : 'إنشاء الإذن')
                    }
                  </button>
                </div>
              )}
            </div>
          )}

          {selectedWorker && dateFrom && dateTo && unpaidSessions.length === 0 && !loading && (
            <div className="card">
              <div style={{ textAlign: 'center', color: '#666', padding: '2rem' }}>
                <div style={{ fontSize: '3rem', marginBottom: '1rem', opacity: 0.5 }}>📝</div>
                <div>لا توجد تشغيلات غير مدفوعة للعامل في هذه الفترة</div>
              </div>
            </div>
          )}
        </div>
      ) : (
        // قائمة الأذون
        <div className="card">
          <div className="card-title">قائمة أذون العمال ({vouchers.length} إذن)</div>
          
          {vouchers.length > 0 ? (
            <div style={{ overflow: 'auto' }}>
              <table className="table">
                <thead>
                  <tr>
                    <th>رقم الإذن</th>
                    <th>العامل</th>
                    <th>الفترة</th>
                    <th>المبلغ</th>
                    <th>الحالة</th>
                    <th>تاريخ الإنشاء</th>
                    <th>الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {vouchers.map(voucher => (
                    <tr key={voucher.id}>
                      <td>
                        <button
                          className="btn-link"
                          onClick={() => openVoucherForEdit(voucher)}
                          style={{
                            background: 'none',
                            border: 'none',
                            color: '#007bff',
                            textDecoration: 'underline',
                            cursor: 'pointer',
                            fontWeight: 'bold',
                            fontSize: '1rem'
                          }}
                        >
                          {voucher.voucherNumber}
                        </button>
                      </td>
                      <td>{voucher.workerName}</td>
                      <td>{voucher.dateFrom} إلى {voucher.dateTo}</td>
                      <td style={{ fontWeight: 'bold', color: '#007bff' }}>
                        {voucher.totalAmount.toLocaleString('ar-EG')} ج.م
                      </td>
                      <td>
                        <span style={{
                          background: getStatusColor(voucher.status),
                          color: 'white',
                          padding: '0.25rem 0.5rem',
                          borderRadius: '3px',
                          fontSize: '0.8rem'
                        }}>
                          {getStatusText(voucher.status)}
                        </span>
                      </td>
                      <td>{new Date(voucher.createdAt).toLocaleDateString('ar-EG')}</td>
                      <td>
                        <div style={{ display: 'flex', gap: '0.5rem' }}>
                          <button
                            className="btn btn-primary"
                            style={{ padding: '0.25rem 0.5rem', fontSize: '0.8rem' }}
                            onClick={() => printVoucher(voucher)}
                          >
                            طباعة
                          </button>
                          {voucher.status === 'pending' && (
                            <button
                              className="btn btn-success"
                              style={{ padding: '0.25rem 0.5rem', fontSize: '0.8rem' }}
                              onClick={() => updateVoucherStatus(voucher.id, 'approved')}
                            >
                              اعتماد
                            </button>
                          )}
                          {voucher.status === 'approved' && (
                            <button
                              className="btn btn-info"
                              style={{ padding: '0.25rem 0.5rem', fontSize: '0.8rem' }}
                              onClick={() => updateVoucherStatus(voucher.id, 'paid')}
                            >
                              دفع
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div style={{ textAlign: 'center', color: '#666', padding: '2rem' }}>
              <div style={{ fontSize: '3rem', marginBottom: '1rem', opacity: 0.5 }}>📋</div>
              <div>لا توجد أذون مسجلة</div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default WorkerVouchers;
