import React, { useState, useEffect } from 'react';
import { db, dbHelpers, resetDatabase } from '../database/db';

const WorkerVouchers = ({ onRefresh }) => {
  console.log('تم تحميل مكون أذون العمال');

  const [workers, setWorkers] = useState([]);
  const [selectedWorker, setSelectedWorker] = useState('');
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');
  const [unpaidSessions, setUnpaidSessions] = useState([]);
  const [selectedSessions, setSelectedSessions] = useState([]);
  const [vouchers, setVouchers] = useState([]);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [loading, setLoading] = useState(false);
  const [currentView, setCurrentView] = useState('create'); // 'create', 'list', or 'edit'
  const [editingVoucher, setEditingVoucher] = useState(null);

  useEffect(() => {
    loadWorkers();
    loadVouchers();
    
    // تعيين التواريخ الافتراضية (آخر شهر)
    const today = new Date();
    const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
    setDateFrom(lastMonth.toISOString().split('T')[0]);
    setDateTo(today.toISOString().split('T')[0]);
  }, []);

  const loadWorkers = async () => {
    try {
      console.log('بدء تحميل العمال...');

      // تحميل جميع العمال (نشطين وغير نشطين)
      const allWorkers = await db.workers.toArray();
      console.log('جميع العمال:', allWorkers);

      // فلترة العمال النشطين فقط
      const activeWorkers = allWorkers.filter(worker => worker.isActive);
      console.log('العمال النشطين:', activeWorkers);

      setWorkers(activeWorkers);

      // إذا لم يوجد عمال، إنشاء عمال تجريبيين
      if (allWorkers.length === 0) {
        console.log('لا يوجد عمال، سيتم إنشاء عمال تجريبيين...');
        await createSampleWorkers();
        // إعادة تحميل العمال بعد الإنشاء
        const newWorkers = await db.workers.where('isActive').equals(true).toArray();
        setWorkers(newWorkers);
      }

    } catch (error) {
      console.error('خطأ في تحميل العمال:', error);
      alert('حدث خطأ في تحميل العمال: ' + error.message);
    }
  };

  const loadVouchers = async () => {
    try {
      const allVouchers = await db.workerVouchers.orderBy('createdAt').reverse().toArray();
      
      // إضافة بيانات العامل لكل إذن
      const vouchersWithWorkers = await Promise.all(
        allVouchers.map(async (voucher) => {
          const worker = await db.workers.get(voucher.workerId);
          return {
            ...voucher,
            workerName: worker?.name || 'غير معروف'
          };
        })
      );
      
      setVouchers(vouchersWithWorkers);
    } catch (error) {
      console.error('خطأ في تحميل الأذون:', error);
    }
  };

  const loadUnpaidSessions = async () => {
    if (!selectedWorker || !dateFrom || !dateTo) return;

    try {
      setLoading(true);

      const fromDate = new Date(dateFrom);
      const toDate = new Date(dateTo);
      const workerId = parseInt(selectedWorker);

      console.log(`البحث عن تكاليف العامل ${workerId} من ${dateFrom} إلى ${dateTo}`);

      let allSessions = [];

      // 1. البحث في جدول workSessions (التشغيلات المباشرة)
      try {
        if (db.workSessions) {
          const workSessions = await db.workSessions.toArray();
          const filteredWorkSessions = workSessions.filter(session => {
            const sessionDate = new Date(session.date);
            return session.workerId === workerId &&
                   sessionDate >= fromDate &&
                   sessionDate <= toDate &&
                   !session.isPaid &&
                   !session.voucherId; // استبعاد التشغيلات المرتبطة بأذون
          });

          // إضافة تفاصيل العقد لكل تشغيل
          const workSessionsWithDetails = await Promise.all(
            filteredWorkSessions.map(async (session) => {
              const contract = await db.contracts.get(session.contractId);
              return {
                ...session,
                contractName: contract?.name || 'غير معروف',
                siteName: contract?.siteName || 'غير معروف',
                source: 'workSessions',
                sourceLabel: 'تشغيل مباشر',
                totalAmount: session.totalAmount || (session.hoursWorked * session.hourlyRate)
              };
            })
          );

          allSessions = [...allSessions, ...workSessionsWithDetails];
          console.log(`تم العثور على ${workSessionsWithDetails.length} تشغيل من workSessions`);
        }
      } catch (error) {
        console.warn('تعذر تحميل جلسات العمل:', error);
      }

      // 2. البحث في جدول laborCosts (التكاليف المباشرة)
      try {
        if (db.laborCosts) {
          const laborCosts = await db.laborCosts.toArray();
          const filteredLaborCosts = laborCosts.filter(cost => {
            const costDate = new Date(cost.date);
            return cost.workerId === workerId &&
                   costDate >= fromDate &&
                   costDate <= toDate &&
                   !cost.isPaid &&
                   !cost.voucherId; // استبعاد التكاليف المرتبطة بأذون
          });

          // إضافة تفاصيل العقد لكل تكلفة
          const laborCostsWithDetails = await Promise.all(
            filteredLaborCosts.map(async (cost) => {
              const contract = await db.contracts.get(cost.contractId);
              return {
                id: `labor_${cost.id}`,
                date: cost.date,
                workerId: cost.workerId,
                contractId: cost.contractId,
                contractName: contract?.name || 'غير معروف',
                siteName: contract?.siteName || 'غير معروف',
                hoursWorked: cost.hoursWorked,
                hourlyRate: cost.hourlyRate,
                totalAmount: cost.totalCost,
                notes: cost.description || 'تكلفة عمالة مباشرة',
                source: 'laborCosts',
                sourceLabel: 'تكلفة مباشرة',
                isPaid: cost.isPaid,
                originalId: cost.id // للمرجعية
              };
            })
          );

          allSessions = [...allSessions, ...laborCostsWithDetails];
          console.log(`تم العثور على ${laborCostsWithDetails.length} تكلفة من laborCosts`);
        }
      } catch (error) {
        console.warn('تعذر تحميل تكاليف العمالة:', error);
      }

      // 3. البحث في أذون الصرف (contractExpenses)
      try {
        if (db.contractExpenses) {
          const voucherExpenses = await db.contractExpenses
            .where('type')
            .equals('voucher')
            .toArray();

          const voucherLaborCosts = [];
          voucherExpenses.forEach(expense => {
            const expenseDate = new Date(expense.date);
            if (expenseDate >= fromDate && expenseDate <= toDate) {
              if (expense.items && Array.isArray(expense.items)) {
                expense.items.forEach(item => {
                  if (item.type === 'labor' &&
                      item.workerId &&
                      parseInt(item.workerId) === workerId &&
                      !item.isPaid &&
                      !item.voucherId) { // استبعاد التكاليف المرتبطة بأذون عمال
                    voucherLaborCosts.push({
                      id: `voucher_${expense.id}_${item.id}`,
                      date: expense.date,
                      workerId: parseInt(item.workerId),
                      contractId: expense.contractId,
                      hoursWorked: item.quantity,
                      hourlyRate: item.unitCost,
                      totalAmount: item.totalCost,
                      notes: `${expense.description} - ${item.description}`,
                      source: 'voucher',
                      isPaid: false,
                      voucherNumber: expense.voucherNumber
                    });
                  }
                });
              }
            }
          });

          // إضافة تفاصيل العقد لكل تكلفة من أذون الصرف
          const voucherCostsWithDetails = await Promise.all(
            voucherLaborCosts.map(async (cost) => {
              const contract = await db.contracts.get(cost.contractId);
              return {
                ...cost,
                contractName: contract?.name || 'غير معروف',
                siteName: contract?.siteName || 'غير معروف'
              };
            })
          );

          allSessions = [...allSessions, ...voucherCostsWithDetails];
          console.log(`تم العثور على ${voucherCostsWithDetails.length} تكلفة من أذون الصرف`);
        }
      } catch (error) {
        console.warn('تعذر تحميل أذون الصرف:', error);
      }

      // ترتيب النتائج حسب التاريخ (الأحدث أولاً)
      allSessions.sort((a, b) => new Date(b.date) - new Date(a.date));

      setUnpaidSessions(allSessions);
      setSelectedSessions([]); // مسح التحديد السابق

      console.log(`إجمالي التكاليف غير المدفوعة: ${allSessions.length}`);

      if (allSessions.length === 0) {
        console.log('لم يتم العثور على تكاليف غير مدفوعة للعامل في هذه الفترة');
      }

    } catch (error) {
      console.error('خطأ في تحميل التشغيلات:', error);
      alert('حدث خطأ في تحميل التشغيلات: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSessionSelect = (sessionId, isSelected) => {
    if (isSelected) {
      setSelectedSessions([...selectedSessions, sessionId]);
    } else {
      setSelectedSessions(selectedSessions.filter(id => id !== sessionId));
    }
  };

  const selectAllSessions = () => {
    setSelectedSessions(unpaidSessions.map(session => session.id));
  };

  const clearSelection = () => {
    setSelectedSessions([]);
  };

  const calculateTotalAmount = () => {
    return selectedSessions.reduce((total, sessionId) => {
      const session = unpaidSessions.find(s => s.id === sessionId);
      return total + (session?.totalAmount || 0);
    }, 0);
  };

  const generateVoucherNumber = async () => {
    const year = new Date().getFullYear();
    const count = await db.workerVouchers.where('voucherNumber').startsWith(`إذن-${year}-`).count();
    return `إذن-${year}-${String(count + 1).padStart(3, '0')}`;
  };

  const createVoucher = async () => {
    if (selectedSessions.length === 0) {
      alert('يرجى اختيار تشغيلات لإنشاء الإذن');
      return;
    }

    try {
      setLoading(true);

      const voucherNumber = await generateVoucherNumber();
      const totalAmount = calculateTotalAmount();
      const worker = workers.find(w => w.id === parseInt(selectedWorker));

      // إنشاء الإذن
      const voucherId = await db.workerVouchers.add({
        voucherNumber,
        workerId: parseInt(selectedWorker),
        workerName: worker.name,
        dateFrom,
        dateTo,
        sessionIds: selectedSessions,
        totalAmount,
        status: 'pending', // pending, approved, paid, cancelled
        createdAt: new Date(),
        createdBy: 'النظام' // يمكن ربطه بالمستخدم الحالي
      });

      // ربط التشغيلات بالإذن بدون تغيير حالة الدفع
      for (const sessionId of selectedSessions) {
        const session = unpaidSessions.find(s => s.id === sessionId);

        if (session) {
          if (session.source === 'workSessions') {
            // تحديث جدول workSessions - ربط بالإذن فقط
            await db.workSessions.update(sessionId, {
              voucherId: voucherId,
              linkedAt: new Date()
            });
          } else if (session.source === 'laborCosts') {
            // تحديث جدول laborCosts - ربط بالإذن فقط
            const laborCostId = sessionId.replace('labor_', '');
            console.log(`ربط تكلفة العمالة ${laborCostId} بالإذن ${voucherId}`);
            await db.laborCosts.update(parseInt(laborCostId), {
              voucherId: voucherId,
              linkedAt: new Date()
            });
            console.log(`تم ربط تكلفة العمالة ${laborCostId} بنجاح`);
          } else if (session.source === 'voucher') {
            // تحديث عنصر في أذون الصرف - ربط بالإذن فقط
            const [, expenseId, itemId] = sessionId.split('_');
            const expense = await db.contractExpenses.get(parseInt(expenseId));

            if (expense && expense.items) {
              const updatedItems = expense.items.map(item => {
                if (item.id === itemId) {
                  return {
                    ...item,
                    voucherId: voucherId,
                    linkedAt: new Date()
                  };
                }
                return item;
              });

              await db.contractExpenses.update(parseInt(expenseId), {
                items: updatedItems
              });
            }
          }
        }
      }

      // إنشاء القيد المحاسبي
      await createAccountingEntry(voucherId, voucherNumber, totalAmount, worker.name);

      alert('تم إنشاء الإذن بنجاح');

      // إعادة تحميل البيانات
      await loadVouchers();
      await loadUnpaidSessions();
      setSelectedSessions([]);

      if (onRefresh) onRefresh();

    } catch (error) {
      console.error('خطأ في إنشاء الإذن:', error);
      alert('حدث خطأ أثناء إنشاء الإذن');
    } finally {
      setLoading(false);
    }
  };

  const createAccountingEntry = async (voucherId, voucherNumber, amount, workerName) => {
    try {
      // إنشاء قيد محاسبي: من رواتب العمالة إلى مصروفات مستحقة
      const entryNumber = await dbHelpers.generateJournalEntryNumber();
      
      const entryId = await db.journalEntries.add({
        entryNumber,
        date: new Date(),
        description: `إذن دفع عامل - ${workerName} - ${voucherNumber}`,
        reference: voucherNumber,
        type: 'worker_voucher',
        isPosted: false,
        createdAt: new Date()
      });

      // الطرف المدين: رواتب العمالة (5020101)
      await db.journalEntryDetails.add({
        entryId,
        accountId: await getAccountByCode('5020101'), // رواتب العمالة
        description: `رواتب عامل - ${workerName}`,
        debit: amount,
        credit: 0
      });

      // الطرف الدائن: مصروفات مستحقة (2010201)
      await db.journalEntryDetails.add({
        entryId,
        accountId: await getAccountByCode('2010201'), // مصروفات مستحقة
        description: `مستحقات عامل - ${workerName}`,
        debit: 0,
        credit: amount
      });

      // ربط الإذن بالقيد المحاسبي
      await db.workerVouchers.update(voucherId, { journalEntryId: entryId });

    } catch (error) {
      console.error('خطأ في إنشاء القيد المحاسبي:', error);
    }
  };

  const getAccountByCode = async (code) => {
    const account = await db.accounts.where('code').equals(code).first();
    return account?.id || null;
  };

  const createSampleWorkers = async () => {
    try {
      console.log('إنشاء عمال تجريبيين...');

      const sampleWorkers = [
        {
          code: 'W001',
          name: 'أحمد محمد',
          phone: '***********',
          address: 'القاهرة',
          dailyRate: 200,
          hourlyRate: 25,
          isActive: true,
          createdAt: new Date()
        },
        {
          code: 'W002',
          name: 'محمد علي',
          phone: '***********',
          address: 'الجيزة',
          dailyRate: 180,
          hourlyRate: 22,
          isActive: true,
          createdAt: new Date()
        },
        {
          code: 'W003',
          name: 'علي حسن',
          phone: '***********',
          address: 'الإسكندرية',
          dailyRate: 220,
          hourlyRate: 28,
          isActive: true,
          createdAt: new Date()
        }
      ];

      for (const worker of sampleWorkers) {
        await db.workers.add(worker);
      }

      console.log(`تم إنشاء ${sampleWorkers.length} عامل تجريبي`);
    } catch (error) {
      console.error('خطأ في إنشاء العمال التجريبيين:', error);
    }
  };

  const createSampleContracts = async () => {
    try {
      console.log('إنشاء عقود تجريبية...');

      const sampleContracts = [
        {
          contractNumber: 'C001',
          name: 'مشروع البناء الأول',
          siteName: 'موقع القاهرة الجديدة',
          clientName: 'شركة التطوير العقاري',
          startDate: new Date('2024-01-01'),
          endDate: new Date('2024-12-31'),
          totalValue: 1000000,
          status: 'active',
          createdAt: new Date()
        },
        {
          contractNumber: 'C002',
          name: 'مشروع الصيانة',
          siteName: 'موقع الجيزة',
          clientName: 'شركة الإنشاءات',
          startDate: new Date('2024-02-01'),
          endDate: new Date('2024-11-30'),
          totalValue: 500000,
          status: 'active',
          createdAt: new Date()
        }
      ];

      for (const contract of sampleContracts) {
        await db.contracts.add(contract);
      }

      console.log(`تم إنشاء ${sampleContracts.length} عقد تجريبي`);
    } catch (error) {
      console.error('خطأ في إنشاء العقود التجريبية:', error);
    }
  };

  const createSampleWorkSessions = async () => {
    try {
      console.log('إنشاء بيانات تجريبية لجلسات العمل...');

      // الحصول على العمال والعقود
      const allWorkers = await db.workers.toArray();
      const allContracts = await db.contracts.toArray();

      if (allWorkers.length === 0) {
        console.log('لا توجد عمال لإنشاء جلسات العمل');
        return;
      }

      if (allContracts.length === 0) {
        console.log('لا توجد عقود، سيتم إنشاء عقود تجريبية...');
        await createSampleContracts();
        // إعادة تحميل العقود
        const newContracts = await db.contracts.toArray();
        if (newContracts.length === 0) {
          console.log('فشل في إنشاء العقود التجريبية');
          return;
        }
      }

      // إنشاء جلسات عمل تجريبية
      const sampleSessions = [];
      const today = new Date();

      for (let i = 0; i < 10; i++) {
        const worker = allWorkers[Math.floor(Math.random() * allWorkers.length)];
        const contract = allContracts[Math.floor(Math.random() * allContracts.length)];
        const sessionDate = new Date(today.getTime() - (Math.random() * 30 * 24 * 60 * 60 * 1000)); // آخر 30 يوم
        const hoursWorked = Math.floor(Math.random() * 8) + 1; // 1-8 ساعات
        const hourlyRate = 50 + Math.floor(Math.random() * 50); // 50-100 ج.م/ساعة

        sampleSessions.push({
          workerId: worker.id,
          contractId: contract.id,
          date: sessionDate,
          hoursWorked,
          hourlyRate,
          totalAmount: hoursWorked * hourlyRate,
          notes: `تشغيل تجريبي ${i + 1}`,
          isPaid: false,
          createdAt: new Date()
        });
      }

      // إضافة الجلسات إلى قاعدة البيانات
      for (const session of sampleSessions) {
        await db.workSessions.add(session);
      }

      console.log(`تم إنشاء ${sampleSessions.length} جلسة عمل تجريبية`);
    } catch (error) {
      console.error('خطأ في إنشاء البيانات التجريبية:', error);
    }
  };

  const updateVoucherStatus = async (voucherId, newStatus) => {
    try {
      const voucher = await db.workerVouchers.get(voucherId);

      await db.workerVouchers.update(voucherId, {
        status: newStatus,
        updatedAt: new Date()
      });

      if (newStatus === 'paid') {
        // تحديث حالة الدفع للتكاليف المرتبطة
        if (voucher.sessionIds && voucher.sessionIds.length > 0) {
          for (const sessionId of voucher.sessionIds) {
            if (sessionId.toString().startsWith('labor_')) {
              // تحديث laborCosts
              const laborCostId = sessionId.replace('labor_', '');
              await db.laborCosts.update(parseInt(laborCostId), {
                isPaid: true,
                paidAt: new Date()
              });
            } else if (sessionId.toString().startsWith('voucher_')) {
              // تحديث contractExpenses
              const [, expenseId, itemId] = sessionId.split('_');
              const expense = await db.contractExpenses.get(parseInt(expenseId));

              if (expense && expense.items) {
                const updatedItems = expense.items.map(item => {
                  if (item.id === itemId) {
                    return {
                      ...item,
                      isPaid: true,
                      paidAt: new Date()
                    };
                  }
                  return item;
                });

                await db.contractExpenses.update(parseInt(expenseId), {
                  items: updatedItems
                });
              }
            } else {
              // تحديث workSessions
              await db.workSessions.update(sessionId, {
                isPaid: true,
                paidAt: new Date()
              });
            }
          }
        }

        // ترحيل القيد المحاسبي
        if (voucher.journalEntryId) {
          await db.journalEntries.update(voucher.journalEntryId, { isPosted: true });
        }
      }

      await loadVouchers();
      alert('تم تحديث حالة الإذن بنجاح');
    } catch (error) {
      console.error('خطأ في تحديث حالة الإذن:', error);
      alert('حدث خطأ أثناء تحديث حالة الإذن');
    }
  };

  const printVoucher = async (voucher) => {
    try {
      console.log('طباعة الإذن:', voucher);
      console.log('sessionIds في الإذن:', voucher.sessionIds);
      console.log('نوع sessionIds:', typeof voucher.sessionIds);

      // تحميل جميع التكاليف المرتبطة بالإذن من جميع المصادر
      let allVoucherSessions = [];

      // البحث باستخدام sessionIds المحفوظة في الإذن
      if (voucher.sessionIds && voucher.sessionIds.length > 0) {
        console.log('البحث باستخدام sessionIds:', voucher.sessionIds);

        for (const sessionId of voucher.sessionIds) {
          console.log(`البحث عن sessionId: ${sessionId} (نوع: ${typeof sessionId})`);
          console.log(`محتوى sessionId:`, sessionId);
          try {
            if (sessionId.toString().startsWith('labor_')) {
              // البحث في laborCosts
              const laborCostId = sessionId.replace('labor_', '');
              console.log(`البحث في laborCosts عن ID: ${laborCostId}`);
              const cost = await db.laborCosts.get(parseInt(laborCostId));
              console.log(`نتيجة البحث في laborCosts:`, cost);

              if (cost) {
                console.log(`تم العثور على تكلفة عمالة: ${laborCostId}`);
                const contract = await db.contracts.get(cost.contractId);
                allVoucherSessions.push({
                  id: sessionId,
                  date: cost.date,
                  workerId: cost.workerId,
                  contractId: cost.contractId,
                  contractName: contract?.name || 'غير معروف',
                  siteName: contract?.siteName || 'غير معروف',
                  hoursWorked: cost.hoursWorked,
                  hourlyRate: cost.hourlyRate,
                  totalAmount: cost.totalCost,
                  notes: cost.description || 'تكلفة عمالة مباشرة',
                  source: 'laborCosts',
                  sourceLabel: 'تكلفة مباشرة'
                });
                console.log(`تم إضافة تكلفة عمالة للطباعة: ${sessionId}`);
              } else {
                console.warn(`لم يتم العثور على تكلفة عمالة بـ ID: ${laborCostId}`);
              }
            } else if (sessionId.toString().startsWith('voucher_')) {
              // البحث في contractExpenses
              const [, expenseId, itemId] = sessionId.split('_');
              console.log(`البحث في contractExpenses عن expense ID: ${expenseId}, item ID: ${itemId}`);
              const expense = await db.contractExpenses.get(parseInt(expenseId));
              console.log(`نتيجة البحث في contractExpenses:`, expense);

              if (expense && expense.items) {
                const item = expense.items.find(i => i.id === itemId);
                console.log(`العنصر الموجود:`, item);
                if (item && item.type === 'labor') {
                  console.log(`تم العثور على عنصر عمالة في إذن الصرف: ${itemId}`);
                  const contract = await db.contracts.get(expense.contractId);
                  allVoucherSessions.push({
                    id: sessionId,
                    date: expense.date,
                    workerId: parseInt(item.workerId),
                    contractId: expense.contractId,
                    contractName: contract?.name || 'غير معروف',
                    siteName: contract?.siteName || 'غير معروف',
                    hoursWorked: item.quantity,
                    hourlyRate: item.unitCost,
                    totalAmount: item.totalCost,
                    notes: `${expense.description} - ${item.description}`,
                    source: 'voucher',
                    sourceLabel: 'إذن صرف',
                    voucherNumber: expense.voucherNumber
                  });
                  console.log(`تم إضافة تكلفة إذن صرف للطباعة: ${sessionId}`);
                } else {
                  console.warn(`لم يتم العثور على عنصر عمالة في إذن الصرف: ${itemId}`);
                }
              } else {
                console.warn(`لم يتم العثور على إذن صرف بـ ID: ${expenseId}`);
              }
            } else {
              // البحث في workSessions
              console.log(`البحث في workSessions عن ID: ${sessionId}`);
              const session = await db.workSessions.get(parseInt(sessionId));
              console.log(`نتيجة البحث في workSessions:`, session);

              if (session) {
                console.log(`تم العثور على تشغيل مباشر: ${sessionId}`);
                const contract = await db.contracts.get(session.contractId);
                allVoucherSessions.push({
                  ...session,
                  contractName: contract?.name || 'غير معروف',
                  siteName: contract?.siteName || 'غير معروف',
                  source: 'workSessions',
                  sourceLabel: 'تشغيل مباشر',
                  totalAmount: session.totalAmount || (session.hoursWorked * session.hourlyRate)
                });
                console.log(`تم إضافة تشغيل مباشر للطباعة: ${sessionId}`);
              } else {
                console.warn(`لم يتم العثور على تشغيل مباشر بـ ID: ${sessionId}`);
              }
            }
          } catch (error) {
            console.warn(`خطأ في تحميل التكلفة ${sessionId}:`, error);
          }
        }
        console.log(`انتهى البحث باستخدام sessionIds. تم العثور على ${allVoucherSessions.length} تكلفة`);
      } else {
        console.warn('لا توجد sessionIds في الإذن أو القائمة فارغة');
      }

      // إذا لم نجد تكاليف باستخدام sessionIds، نبحث بطريقة أخرى
      if (allVoucherSessions.length === 0) {
        console.log('البحث البديل باستخدام voucherId في جميع الجداول');

        // البحث في جميع الجداول باستخدام voucherId
        try {
          const workSessions = await db.workSessions.toArray();
          const voucherWorkSessions = workSessions.filter(s => s.voucherId === voucher.id);

          for (const session of voucherWorkSessions) {
            const contract = await db.contracts.get(session.contractId);
            allVoucherSessions.push({
              ...session,
              contractName: contract?.name || 'غير معروف',
              siteName: contract?.siteName || 'غير معروف',
              source: 'workSessions',
              sourceLabel: 'تشغيل مباشر',
              totalAmount: session.totalAmount || (session.hoursWorked * session.hourlyRate)
            });
          }
        } catch (error) {
          console.warn('خطأ في البحث في workSessions:', error);
        }

        try {
          const allLaborCosts = await db.laborCosts.toArray();
          const voucherLaborCosts = allLaborCosts.filter(cost => cost.voucherId === voucher.id);

          for (const cost of voucherLaborCosts) {
            const contract = await db.contracts.get(cost.contractId);
            allVoucherSessions.push({
              id: `labor_${cost.id}`,
              date: cost.date,
              workerId: cost.workerId,
              contractId: cost.contractId,
              contractName: contract?.name || 'غير معروف',
              siteName: contract?.siteName || 'غير معروف',
              hoursWorked: cost.hoursWorked,
              hourlyRate: cost.hourlyRate,
              totalAmount: cost.totalCost,
              notes: cost.description || 'تكلفة عمالة مباشرة',
              source: 'laborCosts',
              sourceLabel: 'تكلفة مباشرة'
            });
          }
        } catch (error) {
          console.warn('خطأ في البحث في laborCosts:', error);
        }

        try {
          const allExpenses = await db.contractExpenses.toArray();

          for (const expense of allExpenses) {
            if (expense.items && Array.isArray(expense.items)) {
              for (const item of expense.items) {
                if (item.type === 'labor' && item.voucherId === voucher.id) {
                  const contract = await db.contracts.get(expense.contractId);
                  allVoucherSessions.push({
                    id: `voucher_${expense.id}_${item.id}`,
                    date: expense.date,
                    workerId: parseInt(item.workerId),
                    contractId: expense.contractId,
                    contractName: contract?.name || 'غير معروف',
                    siteName: contract?.siteName || 'غير معروف',
                    hoursWorked: item.quantity,
                    hourlyRate: item.unitCost,
                    totalAmount: item.totalCost,
                    notes: `${expense.description} - ${item.description}`,
                    source: 'voucher',
                    sourceLabel: 'إذن صرف',
                    voucherNumber: expense.voucherNumber
                  });
                }
              }
            }
          }
        } catch (error) {
          console.warn('خطأ في البحث في contractExpenses:', error);
        }
      }

      // ترتيب النتائج حسب التاريخ
      allVoucherSessions.sort((a, b) => new Date(a.date) - new Date(b.date));

      console.log(`تم العثور على ${allVoucherSessions.length} تكلفة للطباعة`);
      console.log('تفاصيل التكاليف للطباعة:', allVoucherSessions);

      if (allVoucherSessions.length === 0) {
        console.warn('تحذير: لا توجد تكاليف للطباعة!');
        alert('تحذير: لا توجد تكاليف مرتبطة بهذا الإذن للطباعة. قد تكون هناك مشكلة في ربط البيانات.');
      }

      const printContent = generateVoucherPrintContent(voucher, allVoucherSessions);

      const printWindow = window.open('', '_blank');
      printWindow.document.write(printContent);
      printWindow.document.close();
      printWindow.focus();

      setTimeout(() => {
        printWindow.print();
      }, 250);

    } catch (error) {
      console.error('خطأ في طباعة الإذن:', error);
      alert('حدث خطأ أثناء طباعة الإذن');
    }
  };

  const generateVoucherPrintContent = (voucher, sessions = []) => {
    const sessionsTableRows = sessions.map(session => `
      <tr>
        <td>${new Date(session.date).toLocaleDateString('ar-EG')}</td>
        <td>${session.siteName}</td>
        <td>${session.contractName}</td>
        <td>${session.hoursWorked || 0}</td>
        <td>${(session.hourlyRate || 0).toLocaleString('ar-EG')}</td>
        <td style="font-weight: bold;">${(session.totalAmount || 0).toLocaleString('ar-EG')}</td>
        <td>${session.sourceLabel || 'غير محدد'}</td>
        <td>${session.notes || '-'}</td>
      </tr>
    `).join('');

    return `
      <!DOCTYPE html>
      <html dir="rtl">
      <head>
        <meta charset="UTF-8">
        <title>إذن دفع عامل - ${voucher.voucherNumber}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
          .header h1 { color: #333; margin-bottom: 10px; }
          .voucher-info { display: flex; justify-content: space-between; margin-bottom: 20px; }
          .voucher-info div { background: #f8f9fa; padding: 10px; border-radius: 5px; }
          .sessions-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
          .sessions-table th, .sessions-table td { border: 1px solid #ddd; padding: 8px; text-align: right; }
          .sessions-table th { background-color: #f8f9fa; font-weight: bold; }
          .total { text-align: center; font-size: 1.5rem; font-weight: bold; color: #007bff; margin: 20px 0; }
          .signatures { display: flex; justify-content: space-between; margin-top: 50px; }
          .signature { text-align: center; width: 200px; }
          .signature-line { border-top: 1px solid #333; margin-top: 50px; padding-top: 10px; }
          @media print { body { margin: 0; } }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>إذن دفع عامل</h1>
          <p>رقم الإذن: ${voucher.voucherNumber}</p>
        </div>

        <div class="voucher-info">
          <div>
            <strong>اسم العامل:</strong> ${voucher.workerName}<br>
            <strong>الفترة:</strong> من ${voucher.dateFrom} إلى ${voucher.dateTo}<br>
            <strong>عدد التكاليف:</strong> ${sessions.length} عنصر
          </div>
          <div>
            <strong>تاريخ الإنشاء:</strong> ${new Date(voucher.createdAt).toLocaleDateString('ar-EG')}<br>
            <strong>الحالة:</strong> ${getStatusText(voucher.status)}<br>
            <strong>معرفات التكاليف:</strong> ${voucher.sessionIds ? voucher.sessionIds.length : 0} عنصر محفوظ
          </div>
        </div>

        ${sessions.length > 0 ? `
        <h3 style="margin-top: 30px; margin-bottom: 15px; color: #333;">تفاصيل التكاليف (${sessions.length} عنصر):</h3>
        <table class="sessions-table">
          <thead>
            <tr>
              <th>التاريخ</th>
              <th>الموقع</th>
              <th>العقد</th>
              <th>ساعات العمل</th>
              <th>أجر الساعة (ج.م)</th>
              <th>إجمالي المبلغ (ج.م)</th>
              <th>المصدر</th>
              <th>الملاحظات</th>
            </tr>
          </thead>
          <tbody>
            ${sessionsTableRows}
          </tbody>
        </table>
        ` : `
        <div style="text-align: center; padding: 2rem; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; margin: 20px 0;">
          <h4 style="color: #856404; margin-bottom: 1rem;">⚠️ تحذير</h4>
          <p style="color: #856404; margin: 0;">لا توجد تكاليف مرتبطة بهذا الإذن للعرض</p>
          <p style="color: #856404; font-size: 0.9rem; margin-top: 0.5rem;">قد تكون هناك مشكلة في ربط البيانات أو أن الإذن فارغ</p>
        </div>
        `}

        <div class="total">
          إجمالي المبلغ: ${voucher.totalAmount.toLocaleString('ar-EG')} ج.م
        </div>

        ${sessions.length > 0 ? `
        <div style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 5px;">
          <h4 style="margin-bottom: 10px; color: #333;">ملخص التكاليف حسب المصدر:</h4>
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
            ${['workSessions', 'laborCosts', 'voucher'].map(source => {
              const sourceSessions = sessions.filter(s => s.source === source);
              const sourceTotal = sourceSessions.reduce((sum, s) => sum + (s.totalAmount || 0), 0);
              const sourceLabel = source === 'workSessions' ? 'تشغيل مباشر' :
                                 source === 'laborCosts' ? 'تكلفة مباشرة' : 'إذن صرف';

              return sourceSessions.length > 0 ? `
                <div style="text-align: center; padding: 10px; border: 1px solid #ddd; border-radius: 3px;">
                  <div style="font-weight: bold; color: #007bff;">${sourceLabel}</div>
                  <div style="font-size: 0.9rem; color: #666;">${sourceSessions.length} عنصر</div>
                  <div style="font-weight: bold; color: #28a745;">${sourceTotal.toLocaleString('ar-EG')} ج.م</div>
                </div>
              ` : '';
            }).join('')}
          </div>
        </div>
        ` : ''}

        <div class="signatures">
          <div class="signature">
            <div class="signature-line">المحاسب</div>
          </div>
          <div class="signature">
            <div class="signature-line">المدير المالي</div>
          </div>
          <div class="signature">
            <div class="signature-line">العامل</div>
          </div>
        </div>
      </body>
      </html>
    `;
  };

  const getStatusText = (status) => {
    const statusMap = {
      'pending': 'معلق',
      'approved': 'معتمد',
      'paid': 'مدفوع',
      'cancelled': 'ملغي'
    };
    return statusMap[status] || status;
  };

  const getStatusColor = (status) => {
    const colorMap = {
      'pending': '#ffc107',
      'approved': '#17a2b8',
      'paid': '#28a745',
      'cancelled': '#dc3545'
    };
    return colorMap[status] || '#6c757d';
  };

  const openVoucherForEdit = async (voucher) => {
    try {
      console.log('فتح الإذن للتعديل:', voucher);

      // تحميل جميع التكاليف المرتبطة بالإذن من جميع المصادر
      let allVoucherSessions = [];

      // 1. البحث في workSessions
      try {
        const workSessions = await db.workSessions
          .where('voucherId')
          .equals(voucher.id)
          .toArray();

        const workSessionsWithDetails = await Promise.all(
          workSessions.map(async (session) => {
            const contract = await db.contracts.get(session.contractId);
            return {
              ...session,
              contractName: contract?.name || 'غير معروف',
              siteName: contract?.siteName || 'غير معروف',
              source: 'workSessions',
              sourceLabel: 'تشغيل مباشر',
              totalAmount: session.totalAmount || (session.hoursWorked * session.hourlyRate)
            };
          })
        );

        allVoucherSessions = [...allVoucherSessions, ...workSessionsWithDetails];
      } catch (error) {
        console.warn('تعذر تحميل workSessions:', error);
      }

      // 2. البحث في laborCosts
      try {
        const allLaborCosts = await db.laborCosts.toArray();
        const laborCosts = allLaborCosts.filter(cost => cost.voucherId === voucher.id);

        const laborCostsWithDetails = await Promise.all(
          laborCosts.map(async (cost) => {
            const contract = await db.contracts.get(cost.contractId);
            return {
              id: `labor_${cost.id}`,
              date: cost.date,
              workerId: cost.workerId,
              contractId: cost.contractId,
              contractName: contract?.name || 'غير معروف',
              siteName: contract?.siteName || 'غير معروف',
              hoursWorked: cost.hoursWorked,
              hourlyRate: cost.hourlyRate,
              totalAmount: cost.totalCost,
              notes: cost.description || 'تكلفة عمالة مباشرة',
              source: 'laborCosts',
              sourceLabel: 'تكلفة مباشرة',
              isPaid: cost.isPaid
            };
          })
        );

        allVoucherSessions = [...allVoucherSessions, ...laborCostsWithDetails];
      } catch (error) {
        console.warn('تعذر تحميل laborCosts:', error);
      }

      // 3. البحث في contractExpenses (أذون الصرف)
      try {
        const allExpenses = await db.contractExpenses.toArray();
        const voucherExpenses = [];

        allExpenses.forEach(expense => {
          if (expense.items && Array.isArray(expense.items)) {
            expense.items.forEach(item => {
              if (item.type === 'labor' &&
                  item.voucherId === voucher.id) {
                voucherExpenses.push({
                  id: `voucher_${expense.id}_${item.id}`,
                  date: expense.date,
                  workerId: parseInt(item.workerId),
                  contractId: expense.contractId,
                  hoursWorked: item.quantity,
                  hourlyRate: item.unitCost,
                  totalAmount: item.totalCost,
                  notes: `${expense.description} - ${item.description}`,
                  source: 'voucher',
                  sourceLabel: 'إذن صرف',
                  isPaid: true,
                  voucherNumber: expense.voucherNumber
                });
              }
            });
          }
        });

        const voucherExpensesWithDetails = await Promise.all(
          voucherExpenses.map(async (cost) => {
            const contract = await db.contracts.get(cost.contractId);
            return {
              ...cost,
              contractName: contract?.name || 'غير معروف',
              siteName: contract?.siteName || 'غير معروف'
            };
          })
        );

        allVoucherSessions = [...allVoucherSessions, ...voucherExpensesWithDetails];
      } catch (error) {
        console.warn('تعذر تحميل contractExpenses:', error);
      }

      // ترتيب النتائج حسب التاريخ
      allVoucherSessions.sort((a, b) => new Date(b.date) - new Date(a.date));

      // تعيين بيانات التعديل
      setEditingVoucher({
        ...voucher,
        sessions: allVoucherSessions
      });
      setSelectedWorker(voucher.workerId.toString());
      setDateFrom(voucher.dateFrom);
      setDateTo(voucher.dateTo);
      setSelectedSessions(voucher.sessionIds || []);
      setUnpaidSessions(allVoucherSessions);
      setCurrentView('edit');

      console.log(`تم تحميل ${allVoucherSessions.length} تكلفة للإذن ${voucher.voucherNumber}`);

    } catch (error) {
      console.error('خطأ في فتح الإذن للتعديل:', error);
      alert('حدث خطأ أثناء فتح الإذن للتعديل');
    }
  };

  const saveVoucherChanges = async () => {
    if (!editingVoucher || selectedSessions.length === 0) {
      alert('يرجى اختيار تشغيلات لحفظ التعديلات');
      return;
    }

    try {
      setLoading(true);

      const totalAmount = calculateTotalAmount();

      // تحديث الإذن
      await db.workerVouchers.update(editingVoucher.id, {
        sessionIds: selectedSessions,
        totalAmount,
        updatedAt: new Date()
      });

      // إعادة تعيين حالة التشغيلات القديمة
      const oldSessions = editingVoucher.sessionIds || [];
      for (const sessionId of oldSessions) {
        if (!selectedSessions.includes(sessionId)) {
          await db.workSessions.update(sessionId, {
            isPaid: false,
            voucherId: null,
            paidAt: null
          });
        }
      }

      // تحديث التشغيلات الجديدة
      for (const sessionId of selectedSessions) {
        if (!oldSessions.includes(sessionId)) {
          await db.workSessions.update(sessionId, {
            isPaid: true,
            voucherId: editingVoucher.id,
            paidAt: new Date()
          });
        }
      }

      alert('تم حفظ التعديلات بنجاح');

      // العودة لقائمة الأذون
      setCurrentView('list');
      setEditingVoucher(null);
      await loadVouchers();

    } catch (error) {
      console.error('خطأ في حفظ التعديلات:', error);
      alert('حدث خطأ أثناء حفظ التعديلات');
    } finally {
      setLoading(false);
    }
  };

  const cancelEdit = () => {
    setCurrentView('list');
    setEditingVoucher(null);
    setSelectedWorker('');
    setDateFrom('');
    setDateTo('');
    setSelectedSessions([]);
    setUnpaidSessions([]);
  };



  const deleteVoucher = async (voucherId) => {
    if (!confirm('هل أنت متأكد من حذف هذا الإذن؟ سيتم إلغاء ربط جميع التكاليف المرتبطة به.')) {
      return;
    }

    try {
      setLoading(true);

      // الحصول على بيانات الإذن
      const voucher = await db.workerVouchers.get(voucherId);
      if (!voucher) {
        alert('الإذن غير موجود');
        return;
      }

      // إلغاء ربط التكاليف من جميع المصادر
      if (voucher.sessionIds && voucher.sessionIds.length > 0) {
        for (const sessionId of voucher.sessionIds) {
          // تحديد نوع المصدر من معرف الجلسة
          if (sessionId.toString().startsWith('labor_')) {
            // تحديث laborCosts
            const laborCostId = sessionId.replace('labor_', '');
            await db.laborCosts.update(parseInt(laborCostId), {
              isPaid: false,
              voucherId: null,
              paidAt: null
            });
          } else if (sessionId.toString().startsWith('voucher_')) {
            // تحديث contractExpenses
            const [, expenseId, itemId] = sessionId.split('_');
            const expense = await db.contractExpenses.get(parseInt(expenseId));

            if (expense && expense.items) {
              const updatedItems = expense.items.map(item => {
                if (item.id === itemId) {
                  return {
                    ...item,
                    isPaid: false,
                    voucherId: null,
                    paidAt: null
                  };
                }
                return item;
              });

              await db.contractExpenses.update(parseInt(expenseId), {
                items: updatedItems
              });
            }
          } else {
            // تحديث workSessions
            await db.workSessions.update(sessionId, {
              isPaid: false,
              voucherId: null,
              paidAt: null
            });
          }
        }
      }

      // حذف القيد المحاسبي إذا كان موجوداً
      if (voucher.journalEntryId) {
        await db.journalEntryDetails.where('entryId').equals(voucher.journalEntryId).delete();
        await db.journalEntries.delete(voucher.journalEntryId);
      }

      // حذف الإذن
      await db.workerVouchers.delete(voucherId);

      alert('تم حذف الإذن بنجاح');

      // إعادة تحميل البيانات
      await loadVouchers();
      if (onRefresh) onRefresh();

    } catch (error) {
      console.error('خطأ في حذف الإذن:', error);
      alert('حدث خطأ أثناء حذف الإذن');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      {/* شريط التنقل */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div className="card-title">
          {currentView === 'edit' ? `تعديل الإذن - ${editingVoucher?.voucherNumber}` : 'أذون العمال'}
        </div>
        <div style={{ display: 'flex', gap: '1rem' }}>
          {currentView === 'edit' ? (
            <>
              <button
                className="btn btn-secondary"
                onClick={cancelEdit}
                style={{ padding: '1rem', fontSize: '1rem' }}
              >
                ← العودة لقائمة الأذون
              </button>
              <button
                className="btn btn-success"
                onClick={saveVoucherChanges}
                disabled={loading || selectedSessions.length === 0}
                style={{ padding: '1rem', fontSize: '1rem' }}
              >
                {loading ? 'جاري الحفظ...' : '💾 حفظ التعديلات'}
              </button>
            </>
          ) : (
            <>
              <button
                className={`btn ${currentView === 'create' ? 'btn-primary' : 'btn-secondary'}`}
                onClick={() => {
                  setCurrentView('create');
                  setEditingVoucher(null);
                }}
                style={{ padding: '1rem', fontSize: '1rem' }}
              >
                ➕ إنشاء إذن جديد
              </button>
              <button
                className={`btn ${currentView === 'list' ? 'btn-primary' : 'btn-secondary'}`}
                onClick={() => {
                  setCurrentView('list');
                  setEditingVoucher(null);
                }}
                style={{ padding: '1rem', fontSize: '1rem' }}
              >
                📋 قائمة الأذون
              </button>
            </>
          )}
        </div>

      </div>

      {currentView === 'create' || currentView === 'edit' ? (
        <div>

          {/* نموذج إنشاء إذن جديد */}
          <div className="card" style={{ marginBottom: '2rem' }}>
            <div className="card-title">
              {currentView === 'edit' ? `تعديل إذن دفع عامل - ${editingVoucher?.voucherNumber}` : 'إنشاء إذن دفع عامل'}
            </div>
            
            <div className="grid grid-3">
              <div className="form-group">
                <label className="form-label">العامل</label>
                <select
                  className="form-control"
                  value={selectedWorker}
                  onChange={(e) => setSelectedWorker(e.target.value)}
                >
                  <option value="">اختر العامل</option>
                  {workers.map(worker => (
                    <option key={worker.id} value={worker.id}>
                      {worker.name} - {worker.code}
                    </option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label className="form-label">من تاريخ</label>
                <input
                  type="date"
                  className="form-control"
                  value={dateFrom}
                  onChange={(e) => setDateFrom(e.target.value)}
                />
              </div>

              <div className="form-group">
                <label className="form-label">إلى تاريخ</label>
                <input
                  type="date"
                  className="form-control"
                  value={dateTo}
                  onChange={(e) => setDateTo(e.target.value)}
                />
              </div>
            </div>

            <div style={{ marginTop: '1rem' }}>
              <button
                className="btn btn-primary"
                onClick={loadUnpaidSessions}
                disabled={!selectedWorker || !dateFrom || !dateTo || loading}
              >
                {loading ? 'جاري البحث...' : 'البحث عن التشغيلات'}
              </button>
            </div>
          </div>

          {/* قائمة التشغيلات غير المدفوعة */}
          {unpaidSessions.length > 0 && (
            <div className="card">
              <div className="card-title">
                {currentView === 'edit' ?
                  `تشغيلات الإذن ({unpaidSessions.length} تشغيل)` :
                  `التشغيلات غير المدفوعة ({unpaidSessions.length} تشغيل)`
                }
              </div>

              <div style={{ marginBottom: '1rem', display: 'flex', gap: '1rem' }}>
                <button className="btn btn-secondary" onClick={selectAllSessions}>
                  تحديد الكل
                </button>
                <button className="btn btn-secondary" onClick={clearSelection}>
                  مسح التحديد
                </button>
                <div style={{ marginRight: 'auto', fontSize: '1.1rem', fontWeight: 'bold' }}>
                  المحدد: {selectedSessions.length} تشغيل - 
                  إجمالي المبلغ: {calculateTotalAmount().toLocaleString('ar-EG')} ج.م
                </div>
              </div>

              <div style={{ overflow: 'auto' }}>
                <table className="table">
                  <thead>
                    <tr>
                      <th width="50">تحديد</th>
                      <th>التاريخ</th>
                      <th>الموقع</th>
                      <th>العقد</th>
                      <th>ساعات العمل</th>
                      <th>أجر الساعة</th>
                      <th>إجمالي المبلغ</th>
                      <th>المصدر</th>
                      <th>الملاحظات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {unpaidSessions.map(session => (
                      <tr key={session.id}>
                        <td>
                          <input
                            type="checkbox"
                            checked={selectedSessions.includes(session.id)}
                            onChange={(e) => handleSessionSelect(session.id, e.target.checked)}
                          />
                        </td>
                        <td>{new Date(session.date).toLocaleDateString('ar-EG')}</td>
                        <td>{session.siteName}</td>
                        <td>{session.contractName}</td>
                        <td>{session.hoursWorked}</td>
                        <td>{(session.hourlyRate || 0).toLocaleString('ar-EG')} ج.م</td>
                        <td style={{ fontWeight: 'bold', color: '#007bff' }}>
                          {(session.totalAmount || 0).toLocaleString('ar-EG')} ج.م
                        </td>
                        <td>
                          <span style={{
                            padding: '0.25rem 0.5rem',
                            borderRadius: '3px',
                            fontSize: '0.8rem',
                            fontWeight: 'bold',
                            background: session.source === 'workSessions' ? '#e3f2fd' :
                                       session.source === 'laborCosts' ? '#f3e5f5' : '#fff3e0',
                            color: session.source === 'workSessions' ? '#1976d2' :
                                   session.source === 'laborCosts' ? '#7b1fa2' : '#f57c00'
                          }}>
                            {session.sourceLabel ||
                             (session.source === 'workSessions' ? 'تشغيل مباشر' :
                              session.source === 'laborCosts' ? 'تكلفة مباشرة' :
                              session.source === 'voucher' ? 'إذن صرف' : 'غير محدد')}
                          </span>
                        </td>
                        <td>{session.notes || '-'}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {selectedSessions.length > 0 && (
                <div style={{ marginTop: '1rem', textAlign: 'center' }}>
                  <button
                    className="btn btn-success"
                    onClick={currentView === 'edit' ? saveVoucherChanges : createVoucher}
                    disabled={loading}
                  >
                    {loading ?
                      (currentView === 'edit' ? 'جاري الحفظ...' : 'جاري الإنشاء...') :
                      (currentView === 'edit' ? 'حفظ التعديلات' : 'إنشاء الإذن')
                    }
                  </button>
                </div>
              )}
            </div>
          )}

          {selectedWorker && dateFrom && dateTo && unpaidSessions.length === 0 && !loading && (
            <div className="card">
              <div style={{ textAlign: 'center', color: '#666', padding: '2rem' }}>
                <div style={{ fontSize: '3rem', marginBottom: '1rem', opacity: 0.5 }}>📝</div>
                <h5>لا توجد تكاليف متاحة للعامل في هذه الفترة</h5>
                <div style={{ fontSize: '0.9rem', marginTop: '1rem', color: '#999' }}>
                  <p>الأسباب المحتملة:</p>
                  <ul style={{ textAlign: 'right', display: 'inline-block', listStyle: 'none', padding: 0 }}>
                    <li>• لا توجد تشغيلات أو تكاليف مسجلة للعامل في هذه الفترة</li>
                    <li>• جميع التكاليف مدفوعة بالفعل</li>
                    <li>• <strong>التكاليف مرتبطة بأذون صرف أخرى</strong></li>
                  </ul>
                  <div style={{
                    marginTop: '1rem',
                    padding: '0.75rem',
                    background: '#e3f2fd',
                    borderRadius: '5px',
                    color: '#1976d2',
                    fontSize: '0.85rem'
                  }}>
                    💡 <strong>ملاحظة:</strong> التكاليف التي تم سحبها لإنشاء أذون صرف سابقة لن تظهر هنا لمنع التكرار
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      ) : (
        // قائمة الأذون
        <div className="card">
          <div className="card-title">قائمة أذون العمال ({vouchers.length} إذن)</div>
          
          {vouchers.length > 0 ? (
            <div style={{ overflow: 'auto' }}>
              <table className="table">
                <thead>
                  <tr>
                    <th>رقم الإذن</th>
                    <th>العامل</th>
                    <th>الفترة</th>
                    <th>المبلغ</th>
                    <th>الحالة</th>
                    <th>تاريخ الإنشاء</th>
                    <th>الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {vouchers.map(voucher => (
                    <tr key={voucher.id}>
                      <td>
                        <button
                          className="btn-link"
                          onClick={() => openVoucherForEdit(voucher)}
                          style={{
                            background: 'none',
                            border: 'none',
                            color: '#007bff',
                            textDecoration: 'underline',
                            cursor: 'pointer',
                            fontWeight: 'bold',
                            fontSize: '1rem'
                          }}
                        >
                          {voucher.voucherNumber}
                        </button>
                      </td>
                      <td>{voucher.workerName}</td>
                      <td>{voucher.dateFrom} إلى {voucher.dateTo}</td>
                      <td style={{ fontWeight: 'bold', color: '#007bff' }}>
                        {voucher.totalAmount.toLocaleString('ar-EG')} ج.م
                      </td>
                      <td>
                        <span style={{
                          background: getStatusColor(voucher.status),
                          color: 'white',
                          padding: '0.25rem 0.5rem',
                          borderRadius: '3px',
                          fontSize: '0.8rem'
                        }}>
                          {getStatusText(voucher.status)}
                        </span>
                      </td>
                      <td>{new Date(voucher.createdAt).toLocaleDateString('ar-EG')}</td>
                      <td>
                        <div style={{ display: 'flex', gap: '0.5rem', flexWrap: 'wrap' }}>
                          <button
                            className="btn btn-primary"
                            style={{ padding: '0.25rem 0.5rem', fontSize: '0.8rem' }}
                            onClick={() => printVoucher(voucher)}
                          >
                            طباعة
                          </button>
                          {voucher.status === 'pending' && (
                            <>
                              <button
                                className="btn btn-success"
                                style={{ padding: '0.25rem 0.5rem', fontSize: '0.8rem' }}
                                onClick={() => updateVoucherStatus(voucher.id, 'approved')}
                              >
                                اعتماد
                              </button>
                              <button
                                className="btn btn-danger"
                                style={{ padding: '0.25rem 0.5rem', fontSize: '0.8rem' }}
                                onClick={() => deleteVoucher(voucher.id)}
                              >
                                حذف
                              </button>
                            </>
                          )}
                          {voucher.status === 'approved' && (
                            <button
                              className="btn btn-info"
                              style={{ padding: '0.25rem 0.5rem', fontSize: '0.8rem' }}
                              onClick={() => updateVoucherStatus(voucher.id, 'paid')}
                            >
                              دفع
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div style={{ textAlign: 'center', color: '#666', padding: '2rem' }}>
              <div style={{ fontSize: '3rem', marginBottom: '1rem', opacity: 0.5 }}>📋</div>
              <div>لا توجد أذون مسجلة</div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default WorkerVouchers;
