import React, { useState, useEffect } from 'react';
import { db } from '../database/db';

const CompanySettings = () => {
  const [loading, setLoading] = useState(false);
  const [companyData, setCompanyData] = useState({
    name: '',
    nameEn: '',
    address: '',
    addressEn: '',
    phone: '',
    mobile: '',
    email: '',
    website: '',
    taxNumber: '',
    commercialRegister: '',
    logo: null,
    logoPreview: null
  });

  useEffect(() => {
    loadCompanyData();
  }, []);

  const loadCompanyData = async () => {
    try {
      setLoading(true);
      const settings = await db.settings.where('key').equals('company_info').first();
      if (settings) {
        setCompanyData(settings.value);
      }
    } catch (error) {
      console.error('خطأ في تحميل بيانات الشركة:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setCompanyData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleLogoChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      // التحقق من نوع الملف
      if (!file.type.startsWith('image/')) {
        alert('يرجى اختيار ملف صورة صحيح');
        return;
      }

      // التحقق من حجم الملف (أقل من 2MB)
      if (file.size > 2 * 1024 * 1024) {
        alert('حجم الصورة يجب أن يكون أقل من 2 ميجابايت');
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const logoData = e.target.result;
        setCompanyData(prev => ({
          ...prev,
          logo: logoData,
          logoPreview: logoData
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  const removeLogo = () => {
    setCompanyData(prev => ({
      ...prev,
      logo: null,
      logoPreview: null
    }));
  };

  const saveCompanyData = async () => {
    try {
      setLoading(true);
      
      // التحقق من البيانات المطلوبة
      if (!companyData.name.trim()) {
        alert('اسم الشركة مطلوب');
        return;
      }

      // حفظ البيانات في قاعدة البيانات
      const existingSetting = await db.settings.where('key').equals('company_info').first();
      
      if (existingSetting) {
        await db.settings.update(existingSetting.id, {
          value: companyData,
          updatedAt: new Date()
        });
      } else {
        await db.settings.add({
          key: 'company_info',
          value: companyData,
          createdAt: new Date()
        });
      }

      alert('تم حفظ بيانات الشركة بنجاح');
    } catch (error) {
      console.error('خطأ في حفظ بيانات الشركة:', error);
      alert('حدث خطأ أثناء حفظ بيانات الشركة');
    } finally {
      setLoading(false);
    }
  };

  const resetData = () => {
    if (confirm('هل تريد إعادة تعيين جميع البيانات؟')) {
      setCompanyData({
        name: '',
        nameEn: '',
        address: '',
        addressEn: '',
        phone: '',
        mobile: '',
        email: '',
        website: '',
        taxNumber: '',
        commercialRegister: '',
        logo: null,
        logoPreview: null
      });
    }
  };

  const previewInvoice = () => {
    const printWindow = window.open('', '_blank');
    const currentDate = new Date().toLocaleDateString('ar-EG');
    
    const previewContent = `
      <!DOCTYPE html>
      <html dir="rtl">
      <head>
        <meta charset="UTF-8">
        <title>معاينة رأس الفاتورة</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .invoice-header { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            border-bottom: 2px solid #007bff; 
            padding-bottom: 20px; 
            margin-bottom: 30px; 
          }
          .company-info { flex: 1; }
          .company-logo { 
            max-width: 150px; 
            max-height: 100px; 
            margin-left: 20px; 
          }
          .company-name { 
            font-size: 1.8rem; 
            font-weight: bold; 
            color: #007bff; 
            margin-bottom: 5px; 
          }
          .company-name-en { 
            font-size: 1.2rem; 
            color: #666; 
            margin-bottom: 10px; 
          }
          .company-details { 
            font-size: 0.9rem; 
            color: #555; 
            line-height: 1.5; 
          }
          .invoice-title { 
            text-align: center; 
            font-size: 1.5rem; 
            font-weight: bold; 
            color: #333; 
            margin: 20px 0; 
          }
          .sample-content { 
            background: #f8f9fa; 
            padding: 20px; 
            border-radius: 5px; 
            text-align: center; 
            color: #666; 
          }
        </style>
      </head>
      <body>
        <div class="invoice-header">
          <div class="company-info">
            <div class="company-name">${companyData.name || 'اسم الشركة'}</div>
            ${companyData.nameEn ? `<div class="company-name-en">${companyData.nameEn}</div>` : ''}
            <div class="company-details">
              ${companyData.address ? `<div>📍 ${companyData.address}</div>` : ''}
              ${companyData.addressEn ? `<div style="direction: ltr; text-align: right;">📍 ${companyData.addressEn}</div>` : ''}
              ${companyData.phone ? `<div>📞 ${companyData.phone}</div>` : ''}
              ${companyData.mobile ? `<div>📱 ${companyData.mobile}</div>` : ''}
              ${companyData.email ? `<div>📧 ${companyData.email}</div>` : ''}
              ${companyData.website ? `<div>🌐 ${companyData.website}</div>` : ''}
              ${companyData.taxNumber ? `<div>الرقم الضريبي: ${companyData.taxNumber}</div>` : ''}
              ${companyData.commercialRegister ? `<div>السجل التجاري: ${companyData.commercialRegister}</div>` : ''}
            </div>
          </div>
          ${companyData.logo ? `<img src="${companyData.logo}" alt="شعار الشركة" class="company-logo">` : ''}
        </div>
        
        <div class="invoice-title">فاتورة بيع</div>
        
        <div class="sample-content">
          <h3>معاينة رأس الفاتورة</h3>
          <p>هذا مثال على كيفية ظهور بيانات الشركة في فواتير البيع</p>
          <p>التاريخ: ${currentDate}</p>
        </div>
      </body>
      </html>
    `;

    printWindow.document.write(previewContent);
    printWindow.document.close();
    printWindow.focus();
  };

  if (loading) {
    return (
      <div className="card">
        <div style={{ textAlign: 'center', padding: '2rem' }}>
          <div className="spinner"></div>
          <div>جاري تحميل بيانات الشركة...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="card">
      <div className="card-title">🏢 بيانات الشركة</div>
      
      <div style={{ 
        background: '#d1ecf1', 
        border: '1px solid #bee5eb', 
        borderRadius: '5px', 
        padding: '1rem',
        marginBottom: '2rem'
      }}>
        <div style={{ color: '#0c5460', fontWeight: 'bold', marginBottom: '0.5rem' }}>
          ℹ️ معلومات مهمة
        </div>
        <div style={{ color: '#0c5460', fontSize: '0.9rem' }}>
          • ستظهر هذه البيانات في رأس جميع فواتير البيع<br/>
          • يمكن إضافة شعار الشركة (أقل من 2 ميجابايت)<br/>
          • جميع البيانات اختيارية عدا اسم الشركة
        </div>
      </div>

      <form onSubmit={(e) => { e.preventDefault(); saveCompanyData(); }}>
        {/* معلومات الشركة الأساسية */}
        <div style={{ marginBottom: '2rem' }}>
          <h4 style={{ marginBottom: '1rem', color: '#495057' }}>المعلومات الأساسية</h4>
          <div className="grid grid-2">
            <div className="form-group">
              <label className="form-label">اسم الشركة (عربي) *</label>
              <input
                type="text"
                className="form-control"
                value={companyData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                required
              />
            </div>

            <div className="form-group">
              <label className="form-label">اسم الشركة (إنجليزي)</label>
              <input
                type="text"
                className="form-control"
                value={companyData.nameEn}
                onChange={(e) => handleInputChange('nameEn', e.target.value)}
              />
            </div>

            <div className="form-group">
              <label className="form-label">العنوان (عربي)</label>
              <textarea
                className="form-control"
                rows="2"
                value={companyData.address}
                onChange={(e) => handleInputChange('address', e.target.value)}
              />
            </div>

            <div className="form-group">
              <label className="form-label">العنوان (إنجليزي)</label>
              <textarea
                className="form-control"
                rows="2"
                value={companyData.addressEn}
                onChange={(e) => handleInputChange('addressEn', e.target.value)}
              />
            </div>
          </div>
        </div>

        {/* معلومات الاتصال */}
        <div style={{ marginBottom: '2rem' }}>
          <h4 style={{ marginBottom: '1rem', color: '#495057' }}>معلومات الاتصال</h4>
          <div className="grid grid-2">
            <div className="form-group">
              <label className="form-label">الهاتف</label>
              <input
                type="text"
                className="form-control"
                value={companyData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
              />
            </div>

            <div className="form-group">
              <label className="form-label">الموبايل</label>
              <input
                type="text"
                className="form-control"
                value={companyData.mobile}
                onChange={(e) => handleInputChange('mobile', e.target.value)}
              />
            </div>

            <div className="form-group">
              <label className="form-label">البريد الإلكتروني</label>
              <input
                type="email"
                className="form-control"
                value={companyData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
              />
            </div>

            <div className="form-group">
              <label className="form-label">الموقع الإلكتروني</label>
              <input
                type="url"
                className="form-control"
                value={companyData.website}
                onChange={(e) => handleInputChange('website', e.target.value)}
              />
            </div>
          </div>
        </div>

        {/* المعلومات القانونية */}
        <div style={{ marginBottom: '2rem' }}>
          <h4 style={{ marginBottom: '1rem', color: '#495057' }}>المعلومات القانونية</h4>
          <div className="grid grid-2">
            <div className="form-group">
              <label className="form-label">الرقم الضريبي</label>
              <input
                type="text"
                className="form-control"
                value={companyData.taxNumber}
                onChange={(e) => handleInputChange('taxNumber', e.target.value)}
              />
            </div>

            <div className="form-group">
              <label className="form-label">السجل التجاري</label>
              <input
                type="text"
                className="form-control"
                value={companyData.commercialRegister}
                onChange={(e) => handleInputChange('commercialRegister', e.target.value)}
              />
            </div>
          </div>
        </div>

        {/* شعار الشركة */}
        <div style={{ marginBottom: '2rem' }}>
          <h4 style={{ marginBottom: '1rem', color: '#495057' }}>شعار الشركة</h4>
          
          <div style={{ display: 'flex', gap: '2rem', alignItems: 'flex-start' }}>
            <div style={{ flex: 1 }}>
              <div className="form-group">
                <label className="form-label">اختر شعار الشركة</label>
                <input
                  type="file"
                  className="form-control"
                  accept="image/*"
                  onChange={handleLogoChange}
                />
                <div style={{ fontSize: '0.8rem', color: '#666', marginTop: '0.5rem' }}>
                  الحد الأقصى: 2 ميجابايت | الأنواع المدعومة: JPG, PNG, GIF
                </div>
              </div>
              
              {companyData.logo && (
                <button
                  type="button"
                  className="btn btn-danger btn-sm"
                  onClick={removeLogo}
                >
                  🗑️ حذف الشعار
                </button>
              )}
            </div>

            {companyData.logoPreview && (
              <div style={{ 
                border: '1px solid #dee2e6', 
                borderRadius: '5px', 
                padding: '1rem',
                background: 'white'
              }}>
                <div style={{ marginBottom: '0.5rem', fontWeight: 'bold', fontSize: '0.9rem' }}>
                  معاينة الشعار:
                </div>
                <img 
                  src={companyData.logoPreview} 
                  alt="شعار الشركة" 
                  style={{ 
                    maxWidth: '150px', 
                    maxHeight: '100px',
                    objectFit: 'contain'
                  }} 
                />
              </div>
            )}
          </div>
        </div>

        {/* أزرار الإجراءات */}
        <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center' }}>
          <button type="submit" className="btn btn-primary" disabled={loading}>
            💾 حفظ البيانات
          </button>
          
          <button type="button" className="btn btn-info" onClick={previewInvoice}>
            👁️ معاينة في الفاتورة
          </button>
          
          <button type="button" className="btn btn-secondary" onClick={resetData}>
            🔄 إعادة تعيين
          </button>
        </div>
      </form>
    </div>
  );
};

export default CompanySettings;
