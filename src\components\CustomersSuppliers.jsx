import React, { useState, useEffect } from 'react';
import { db, generateCustomerAccountNumber, generateSupplierAccountNumber } from '../database/db';
import * as XLSX from 'xlsx';

const CustomersSuppliers = () => {
  const [currentView, setCurrentView] = useState('customers');
  const [customers, setCustomers] = useState([]);
  const [suppliers, setSuppliers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedItem, setSelectedItem] = useState(null);
  const [showInvoices, setShowInvoices] = useState(false);
  const [itemInvoices, setItemInvoices] = useState([]);
  const [showContracts, setShowContracts] = useState(false);
  const [customerContracts, setCustomerContracts] = useState([]);

  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    address: '',
    email: '',
    taxNumber: '',
    notes: '',
    accountNumber: ''
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadCustomers(),
        loadSuppliers()
      ]);
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadCustomers = async () => {
    try {
      const allCustomers = await db.customers.orderBy('name').toArray();
      
      // حساب الرصيد لكل عميل
      const customersWithBalance = await Promise.all(
        allCustomers.map(async (customer) => {
          const salesInvoices = await db.salesInvoices
            .where('customerId')
            .equals(customer.id)
            .toArray();
          
          const totalSales = salesInvoices.reduce((sum, inv) => sum + inv.total, 0);
          const totalPaid = salesInvoices.reduce((sum, inv) => sum + (inv.paidAmount || 0), 0);
          const balance = totalSales - totalPaid;
          
          return {
            ...customer,
            balance,
            totalSales,
            invoicesCount: salesInvoices.length
          };
        })
      );
      
      setCustomers(customersWithBalance);
    } catch (error) {
      console.error('خطأ في تحميل العملاء:', error);
    }
  };

  const loadSuppliers = async () => {
    try {
      const allSuppliers = await db.suppliers.orderBy('name').toArray();
      
      // حساب الرصيد لكل مورد
      const suppliersWithBalance = await Promise.all(
        allSuppliers.map(async (supplier) => {
          const purchaseInvoices = await db.purchaseInvoices
            .where('supplierId')
            .equals(supplier.id)
            .toArray();
          
          const totalPurchases = purchaseInvoices.reduce((sum, inv) => sum + inv.total, 0);
          const totalPaid = purchaseInvoices.reduce((sum, inv) => sum + (inv.paidAmount || 0), 0);
          const balance = totalPurchases - totalPaid;
          
          return {
            ...supplier,
            balance,
            totalPurchases,
            invoicesCount: purchaseInvoices.length
          };
        })
      );
      
      setSuppliers(suppliersWithBalance);
    } catch (error) {
      console.error('خطأ في تحميل الموردين:', error);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      alert('اسم العميل/المورد مطلوب');
      return;
    }

    try {
      const dataToSave = {
        ...formData,
        isActive: true,
        createdAt: new Date()
      };

      // توليد رقم حساب تلقائي إذا لم يكن موجوداً
      if (!editingItem && !formData.accountNumber) {
        if (currentView === 'customers') {
          dataToSave.accountNumber = await generateCustomerAccountNumber();
        } else {
          dataToSave.accountNumber = await generateSupplierAccountNumber();
        }
      }

      if (editingItem) {
        // تحديث
        const table = currentView === 'customers' ? db.customers : db.suppliers;
        await table.update(editingItem.id, {
          ...dataToSave,
          updatedAt: new Date()
        });
        alert('تم تحديث البيانات بنجاح');
      } else {
        // إضافة جديد
        const table = currentView === 'customers' ? db.customers : db.suppliers;
        await table.add(dataToSave);
        alert(`تم إضافة ${currentView === 'customers' ? 'العميل' : 'المورد'} بنجاح`);
      }

      resetForm();
      loadData();
    } catch (error) {
      console.error('خطأ في حفظ البيانات:', error);
      alert('حدث خطأ أثناء حفظ البيانات');
    }
  };

  const handleEdit = (item) => {
    setEditingItem(item);
    setFormData({
      name: item.name,
      phone: item.phone || '',
      address: item.address || '',
      email: item.email || '',
      taxNumber: item.taxNumber || '',
      notes: item.notes || '',
      accountNumber: item.accountNumber || ''
    });
    setShowAddForm(true);
  };

  const handleDelete = async (id) => {
    if (!confirm(`هل أنت متأكد من حذف هذا ${currentView === 'customers' ? 'العميل' : 'المورد'}؟`)) return;
    
    try {
      // التحقق من وجود فواتير
      const table = currentView === 'customers' ? db.salesInvoices : db.purchaseInvoices;
      const field = currentView === 'customers' ? 'customerId' : 'supplierId';
      const invoicesCount = await table.where(field).equals(id).count();
      
      if (invoicesCount > 0) {
        alert(`لا يمكن حذف ${currentView === 'customers' ? 'العميل' : 'المورد'} لوجود فواتير مرتبطة به`);
        return;
      }
      
      const deleteTable = currentView === 'customers' ? db.customers : db.suppliers;
      await deleteTable.delete(id);
      loadData();
      alert('تم الحذف بنجاح');
    } catch (error) {
      console.error('خطأ في الحذف:', error);
      alert('حدث خطأ أثناء الحذف');
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      phone: '',
      address: '',
      email: '',
      taxNumber: '',
      notes: '',
      accountNumber: ''
    });
    setEditingItem(null);
    setShowAddForm(false);
  };

  const handleViewInvoices = async (item) => {
    try {
      setSelectedItem(item);
      let invoices = [];

      if (currentView === 'customers') {
        invoices = await db.salesInvoices
          .where('customerId')
          .equals(item.id)
          .reverse()
          .sortBy('createdAt');
      } else {
        invoices = await db.purchaseInvoices
          .where('supplierId')
          .equals(item.id)
          .reverse()
          .sortBy('createdAt');
      }

      setItemInvoices(invoices);
      setShowInvoices(true);
    } catch (error) {
      console.error('خطأ في تحميل الفواتير:', error);
      alert('حدث خطأ أثناء تحميل الفواتير');
    }
  };

  const handleViewContracts = async (customer) => {
    try {
      setSelectedItem(customer);

      const contracts = await db.contracts
        .where('customerId')
        .equals(parseInt(customer.id))
        .reverse()
        .sortBy('createdAt');

      setCustomerContracts(contracts);
      setShowContracts(true);
    } catch (error) {
      console.error('خطأ في تحميل العقود:', error);
      alert('حدث خطأ أثناء تحميل العقود');
    }
  };

  const handlePayInvoice = (invoice) => {
    const remaining = (invoice.total || invoice.totalAmount || 0) - (invoice.paidAmount || 0);
    const paymentAmount = prompt(`أدخل مبلغ السداد للفاتورة ${invoice.invoiceNumber}\nالمبلغ المتبقي: ${remaining.toLocaleString('ar-EG')} ج.م`, remaining);

    if (paymentAmount === null) return; // المستخدم ألغى العملية

    const amount = parseFloat(paymentAmount);
    if (isNaN(amount) || amount <= 0) {
      alert('يرجى إدخال مبلغ صحيح');
      return;
    }

    if (amount > remaining) {
      alert('مبلغ السداد أكبر من المبلغ المتبقي');
      return;
    }

    // إنشاء قيد السداد/التحصيل
    const entryType = currentView === 'customers' ? 'sales-collection' : 'purchase-payment';
    const accountType = currentView === 'customers' ? 'العملاء' : 'الموردين';

    // توجيه المستخدم إلى صفحة القيود اليومية
    if (confirm(`سيتم إنشاء قيد ${currentView === 'customers' ? 'تحصيل' : 'سداد'} بمبلغ ${amount.toLocaleString('ar-EG')} ج.م\nهل تريد المتابعة؟`)) {
      // حفظ بيانات السداد في localStorage للاستخدام في صفحة القيود
      localStorage.setItem('pendingPayment', JSON.stringify({
        type: entryType,
        invoice: invoice,
        amount: amount,
        itemId: selectedItem.id,
        itemName: selectedItem.name
      }));

      alert('سيتم توجيهك إلى صفحة القيود اليومية لإكمال عملية السداد');
      // يمكن إضافة navigation هنا إذا كان متاحاً
    }
  };

  const exportToExcel = () => {
    try {
      const data = currentView === 'customers' ? customers : suppliers;
      const exportData = data
        .filter(item => !searchTerm || item.name.toLowerCase().includes(searchTerm.toLowerCase()))
        .map(item => ({
          'الاسم': item.name,
          'رقم الحساب': item.accountNumber || '',
          'الهاتف': item.phone || '',
          'العنوان': item.address || '',
          'البريد الإلكتروني': item.email || '',
          'الرقم الضريبي': item.taxNumber || '',
          'الرصيد': item.balance || 0,
          'إجمالي الفواتير': currentView === 'customers' ? item.totalSales : item.totalPurchases,
          'عدد الفواتير': item.invoicesCount,
          'تاريخ الإنشاء': new Date(item.createdAt).toLocaleDateString('ar-EG'),
          'ملاحظات': item.notes || ''
        }));

      const ws = XLSX.utils.json_to_sheet(exportData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, currentView === 'customers' ? 'العملاء' : 'الموردين');
      
      const fileName = `${currentView === 'customers' ? 'العملاء' : 'الموردين'}_${new Date().toISOString().split('T')[0]}.xlsx`;
      XLSX.writeFile(wb, fileName);
      
      alert('تم تصدير البيانات بنجاح');
    } catch (error) {
      console.error('خطأ في تصدير البيانات:', error);
      alert('حدث خطأ أثناء تصدير البيانات');
    }
  };

  const filteredData = (currentView === 'customers' ? customers : suppliers)
    .filter(item => !searchTerm || item.name.toLowerCase().includes(searchTerm.toLowerCase()));

  if (loading) {
    return (
      <div className="container">
        <div className="loading">
          <div className="spinner"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container">
      {/* شريط التنقل */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ display: 'flex', gap: '1rem' }}>
            <button
              className={`btn ${currentView === 'customers' ? 'btn-primary' : 'btn-secondary'}`}
              onClick={() => setCurrentView('customers')}
            >
              👥 العملاء
            </button>
            <button
              className={`btn ${currentView === 'suppliers' ? 'btn-primary' : 'btn-secondary'}`}
              onClick={() => setCurrentView('suppliers')}
            >
              🏢 الموردين
            </button>
          </div>
          
          <div style={{ display: 'flex', gap: '1rem' }}>
            <button
              className="btn btn-success"
              onClick={() => setShowAddForm(true)}
            >
              ➕ إضافة {currentView === 'customers' ? 'عميل' : 'مورد'}
            </button>
            <button
              className="btn btn-info"
              onClick={exportToExcel}
            >
              📊 تصدير Excel
            </button>
          </div>
        </div>
      </div>

      {/* البحث */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div className="form-group">
          <label className="form-label">البحث</label>
          <input
            type="text"
            className="form-control"
            placeholder={`ابحث في ${currentView === 'customers' ? 'العملاء' : 'الموردين'}...`}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {/* القائمة */}
      <div className="card">
        <div className="card-title">
          {currentView === 'customers' ? 'قائمة العملاء' : 'قائمة الموردين'} ({filteredData.length})
        </div>
        
        {filteredData.length > 0 ? (
          <div style={{ overflow: 'auto' }}>
            <table className="table">
              <thead>
                <tr>
                  <th>الاسم</th>
                  <th>رقم الحساب</th>
                  <th>الهاتف</th>
                  <th>العنوان</th>
                  <th>الرصيد</th>
                  <th>عدد الفواتير</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {filteredData.map(item => (
                  <tr key={item.id}>
                    <td><strong>{item.name}</strong></td>
                    <td>
                      <span style={{
                        padding: '0.25rem 0.5rem',
                        background: '#e3f2fd',
                        borderRadius: '3px',
                        fontSize: '0.9rem',
                        fontFamily: 'monospace',
                        color: '#1976d2'
                      }}>
                        {item.accountNumber || 'غير محدد'}
                      </span>
                    </td>
                    <td>{item.phone || '-'}</td>
                    <td>{item.address || '-'}</td>
                    <td style={{
                      color: item.balance > 0 ? '#dc3545' : item.balance < 0 ? '#28a745' : '#333',
                      fontWeight: 'bold'
                    }}>
                      {item.balance?.toLocaleString('ar-EG') || 0} ج.م
                    </td>
                    <td>{item.invoicesCount || 0}</td>
                    <td>
                      <div style={{ display: 'flex', gap: '0.5rem', flexWrap: 'wrap' }}>
                        <button
                          className="btn btn-info"
                          style={{ padding: '0.25rem 0.5rem', fontSize: '0.8rem' }}
                          onClick={() => handleViewInvoices(item)}
                        >
                          📋 الفواتير
                        </button>
                        {currentView === 'customers' && (
                          <button
                            className="btn btn-success"
                            style={{ padding: '0.25rem 0.5rem', fontSize: '0.8rem' }}
                            onClick={() => handleViewContracts(item)}
                          >
                            📄 العقود
                          </button>
                        )}
                        <button
                          className="btn btn-primary"
                          style={{ padding: '0.25rem 0.5rem', fontSize: '0.8rem' }}
                          onClick={() => handleEdit(item)}
                        >
                          تعديل
                        </button>
                        <button
                          className="btn btn-danger"
                          style={{ padding: '0.25rem 0.5rem', fontSize: '0.8rem' }}
                          onClick={() => handleDelete(item.id)}
                        >
                          حذف
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div style={{ textAlign: 'center', color: '#666', padding: '2rem' }}>
            لا يوجد {currentView === 'customers' ? 'عملاء' : 'موردين'} حتى الآن
          </div>
        )}
      </div>

      {/* نموذج الإضافة/التعديل */}
      {showAddForm && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            padding: '2rem',
            borderRadius: '10px',
            width: '90%',
            maxWidth: '600px',
            maxHeight: '90vh',
            overflow: 'auto'
          }}>
            <h3 style={{ marginBottom: '1.5rem' }}>
              {editingItem ? 'تعديل' : 'إضافة'} {currentView === 'customers' ? 'عميل' : 'مورد'}
            </h3>

            <form onSubmit={handleSubmit}>
              <div className="grid grid-2">
                <div className="form-group">
                  <label className="form-label">الاسم *</label>
                  <input
                    type="text"
                    className="form-control"
                    value={formData.name}
                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                    required
                  />
                </div>

                <div className="form-group">
                  <label className="form-label">الهاتف</label>
                  <input
                    type="text"
                    className="form-control"
                    value={formData.phone}
                    onChange={(e) => setFormData({...formData, phone: e.target.value})}
                  />
                </div>

                <div className="form-group">
                  <label className="form-label">البريد الإلكتروني</label>
                  <input
                    type="email"
                    className="form-control"
                    value={formData.email}
                    onChange={(e) => setFormData({...formData, email: e.target.value})}
                  />
                </div>

                <div className="form-group">
                  <label className="form-label">الرقم الضريبي</label>
                  <input
                    type="text"
                    className="form-control"
                    value={formData.taxNumber}
                    onChange={(e) => setFormData({...formData, taxNumber: e.target.value})}
                  />
                </div>

                <div className="form-group">
                  <label className="form-label">رقم الحساب</label>
                  <input
                    type="text"
                    className="form-control"
                    value={formData.accountNumber}
                    onChange={(e) => setFormData({...formData, accountNumber: e.target.value})}
                    placeholder={`سيتم توليده تلقائياً (${currentView === 'customers' ? '1211xxx' : '2111xxx'})`}
                  />
                  <small className="form-text text-muted">
                    {currentView === 'customers'
                      ? 'أرقام حسابات العملاء تبدأ بـ 1211'
                      : 'أرقام حسابات الموردين تبدأ بـ 2111'
                    }
                  </small>
                </div>
              </div>

              <div className="form-group">
                <label className="form-label">العنوان</label>
                <textarea
                  className="form-control"
                  value={formData.address}
                  onChange={(e) => setFormData({...formData, address: e.target.value})}
                  rows="2"
                />
              </div>

              <div className="form-group">
                <label className="form-label">ملاحظات</label>
                <textarea
                  className="form-control"
                  value={formData.notes}
                  onChange={(e) => setFormData({...formData, notes: e.target.value})}
                  rows="3"
                />
              </div>

              <div style={{ display: 'flex', gap: '1rem', marginTop: '1.5rem' }}>
                <button type="submit" className="btn btn-primary">
                  {editingItem ? 'تحديث' : 'حفظ'}
                </button>
                <button 
                  type="button" 
                  className="btn btn-secondary"
                  onClick={resetForm}
                >
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* نافذة عرض الفواتير */}
      {showInvoices && selectedItem && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            padding: '2rem',
            borderRadius: '10px',
            width: '95%',
            maxWidth: '1200px',
            maxHeight: '90vh',
            overflow: 'auto'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1.5rem' }}>
              <h3 style={{ margin: 0 }}>
                فواتير {currentView === 'customers' ? 'العميل' : 'المورد'}: {selectedItem.name}
              </h3>
              <button
                className="btn btn-secondary"
                onClick={() => setShowInvoices(false)}
              >
                ✕ إغلاق
              </button>
            </div>

            {itemInvoices.length > 0 ? (
              <div style={{ overflow: 'auto' }}>
                <table className="table">
                  <thead>
                    <tr>
                      <th>رقم الفاتورة</th>
                      <th>التاريخ</th>
                      <th>الإجمالي</th>
                      <th>المدفوع</th>
                      <th>المتبقي</th>
                      <th>الحالة</th>
                      <th>إجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {itemInvoices.map(invoice => {
                      const remaining = (invoice.total || 0) - (invoice.paidAmount || 0);
                      return (
                        <tr key={invoice.id}>
                          <td><strong>{invoice.invoiceNumber}</strong></td>
                          <td>{new Date(invoice.date).toLocaleDateString('ar-EG')}</td>
                          <td style={{ fontWeight: 'bold', color: '#007bff' }}>
                            {(invoice.total || 0).toLocaleString('ar-EG')} ج.م
                          </td>
                          <td style={{ color: '#28a745' }}>
                            {(invoice.paidAmount || 0).toLocaleString('ar-EG')} ج.م
                          </td>
                          <td style={{
                            color: remaining > 0 ? '#dc3545' : '#28a745',
                            fontWeight: 'bold'
                          }}>
                            {remaining.toLocaleString('ar-EG')} ج.م
                          </td>
                          <td>
                            <span style={{
                              padding: '0.25rem 0.5rem',
                              borderRadius: '4px',
                              fontSize: '0.8rem',
                              background: invoice.paymentStatus === 'pending' ? '#fff3cd' :
                                        invoice.paymentStatus === 'partial' ? '#ffeaa7' :
                                        invoice.paymentStatus === 'paid' ? '#d4edda' : '#f8d7da',
                              color: invoice.paymentStatus === 'pending' ? '#856404' :
                                   invoice.paymentStatus === 'partial' ? '#b8860b' :
                                   invoice.paymentStatus === 'paid' ? '#155724' : '#721c24'
                            }}>
                              {invoice.paymentStatus === 'pending' ? 'غير مسدد' :
                               invoice.paymentStatus === 'partial' ? 'مسدد جزئياً' :
                               invoice.paymentStatus === 'paid' ? 'مسدد بالكامل' : 'غير محدد'}
                            </span>
                          </td>
                          <td>
                            {remaining > 0 && (
                              <button
                                className="btn btn-success btn-sm"
                                onClick={() => handlePayInvoice(invoice)}
                                style={{ fontSize: '0.8rem', padding: '0.25rem 0.5rem' }}
                              >
                                💰 سداد
                              </button>
                            )}
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>

                {/* إجماليات */}
                <div style={{
                  marginTop: '1rem',
                  padding: '1rem',
                  background: '#f8f9fa',
                  borderRadius: '5px',
                  display: 'flex',
                  justifyContent: 'space-around',
                  flexWrap: 'wrap',
                  gap: '1rem'
                }}>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: '0.9rem', color: '#666' }}>عدد الفواتير</div>
                    <div style={{ fontSize: '1.2rem', fontWeight: 'bold', color: '#007bff' }}>
                      {itemInvoices.length}
                    </div>
                  </div>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: '0.9rem', color: '#666' }}>إجمالي المبالغ</div>
                    <div style={{ fontSize: '1.2rem', fontWeight: 'bold', color: '#007bff' }}>
                      {itemInvoices.reduce((sum, inv) => sum + (inv.total || 0), 0).toLocaleString('ar-EG')} ج.م
                    </div>
                  </div>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: '0.9rem', color: '#666' }}>إجمالي المدفوع</div>
                    <div style={{ fontSize: '1.2rem', fontWeight: 'bold', color: '#28a745' }}>
                      {itemInvoices.reduce((sum, inv) => sum + (inv.paidAmount || 0), 0).toLocaleString('ar-EG')} ج.م
                    </div>
                  </div>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: '0.9rem', color: '#666' }}>إجمالي المتبقي</div>
                    <div style={{ fontSize: '1.2rem', fontWeight: 'bold', color: '#dc3545' }}>
                      {itemInvoices.reduce((sum, inv) => sum + ((inv.total || 0) - (inv.paidAmount || 0)), 0).toLocaleString('ar-EG')} ج.م
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div style={{ textAlign: 'center', color: '#666', padding: '2rem' }}>
                لا توجد فواتير لهذا {currentView === 'customers' ? 'العميل' : 'المورد'}
              </div>
            )}
          </div>
        </div>
      )}

      {/* نافذة عرض العقود */}
      {showContracts && selectedItem && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            padding: '2rem',
            borderRadius: '10px',
            width: '95%',
            maxWidth: '1000px',
            maxHeight: '90vh',
            overflow: 'auto'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1.5rem' }}>
              <h3 style={{ margin: 0 }}>عقود العميل: {selectedItem.name}</h3>
              <button
                className="btn btn-secondary"
                onClick={() => setShowContracts(false)}
              >
                ✕ إغلاق
              </button>
            </div>

            {customerContracts.length > 0 ? (
              <div>
                <div style={{ overflow: 'auto', marginBottom: '1.5rem' }}>
                  <table className="table">
                    <thead>
                      <tr>
                        <th>رقم العقد</th>
                        <th>اسم العقد</th>
                        <th>تاريخ البداية</th>
                        <th>تاريخ النهاية</th>
                        <th>قيمة العقد</th>
                        <th>المدفوع</th>
                        <th>المتبقي</th>
                        <th>الحالة</th>
                      </tr>
                    </thead>
                    <tbody>
                      {customerContracts.map(contract => {
                        const remaining = contract.totalValue - contract.paidAmount;
                        const getStatusColor = (status) => {
                          switch (status) {
                            case 'active': return '#28a745';
                            case 'completed': return '#007bff';
                            case 'cancelled': return '#dc3545';
                            case 'pending': return '#ffc107';
                            default: return '#6c757d';
                          }
                        };

                        const getStatusText = (status) => {
                          switch (status) {
                            case 'active': return 'نشط';
                            case 'completed': return 'مكتمل';
                            case 'cancelled': return 'ملغي';
                            case 'pending': return 'معلق';
                            default: return 'غير محدد';
                          }
                        };

                        return (
                          <tr key={contract.id}>
                            <td><strong>{contract.contractNumber}</strong></td>
                            <td>{contract.name}</td>
                            <td>{new Date(contract.startDate).toLocaleDateString('ar-EG')}</td>
                            <td>
                              {contract.endDate ?
                                new Date(contract.endDate).toLocaleDateString('ar-EG') :
                                'غير محدد'
                              }
                            </td>
                            <td style={{ fontWeight: 'bold', color: '#007bff' }}>
                              {contract.totalValue.toLocaleString('ar-EG')} ج.م
                            </td>
                            <td style={{ fontWeight: 'bold', color: '#28a745' }}>
                              {contract.paidAmount.toLocaleString('ar-EG')} ج.م
                            </td>
                            <td style={{
                              fontWeight: 'bold',
                              color: remaining > 0 ? '#dc3545' : '#28a745'
                            }}>
                              {remaining.toLocaleString('ar-EG')} ج.م
                            </td>
                            <td>
                              <span style={{
                                padding: '0.25rem 0.5rem',
                                borderRadius: '4px',
                                fontSize: '0.8rem',
                                color: 'white',
                                background: getStatusColor(contract.status)
                              }}>
                                {getStatusText(contract.status)}
                              </span>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>

                {/* ملخص العقود */}
                <div style={{
                  background: '#f8f9fa',
                  padding: '1rem',
                  borderRadius: '5px',
                  display: 'flex',
                  justifyContent: 'space-around',
                  flexWrap: 'wrap',
                  gap: '1rem'
                }}>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: '0.9rem', color: '#666' }}>عدد العقود</div>
                    <div style={{ fontSize: '1.2rem', fontWeight: 'bold', color: '#007bff' }}>
                      {customerContracts.length}
                    </div>
                  </div>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: '0.9rem', color: '#666' }}>إجمالي قيمة العقود</div>
                    <div style={{ fontSize: '1.2rem', fontWeight: 'bold', color: '#007bff' }}>
                      {customerContracts.reduce((sum, contract) => sum + (contract.totalValue || 0), 0).toLocaleString('ar-EG')} ج.م
                    </div>
                  </div>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: '0.9rem', color: '#666' }}>إجمالي المدفوع</div>
                    <div style={{ fontSize: '1.2rem', fontWeight: 'bold', color: '#28a745' }}>
                      {customerContracts.reduce((sum, contract) => sum + (contract.paidAmount || 0), 0).toLocaleString('ar-EG')} ج.م
                    </div>
                  </div>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: '0.9rem', color: '#666' }}>إجمالي المتبقي</div>
                    <div style={{ fontSize: '1.2rem', fontWeight: 'bold', color: '#dc3545' }}>
                      {customerContracts.reduce((sum, contract) => sum + ((contract.totalValue || 0) - (contract.paidAmount || 0)), 0).toLocaleString('ar-EG')} ج.م
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div style={{ textAlign: 'center', color: '#666', padding: '2rem' }}>
                لا توجد عقود لهذا العميل
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default CustomersSuppliers;
