import React, { useState } from 'react';
import InvoicesView from './InvoicesView';
import IssueVouchersView from './IssueVouchersView';
import AdvancedReports from './AdvancedReports';
import InventoryReports from './InventoryReports';
import AccountsTracker from './AccountsTracker';
import CashFlowReports from './CashFlowReports';

const FinancialReports = () => {
  const [currentReport, setCurrentReport] = useState('invoices');

  const renderReportContent = () => {
    switch (currentReport) {
      case 'invoices':
        return <InvoicesView />;
      case 'issue-vouchers':
        return <IssueVouchersView />;
      case 'trial-balance':
        return (
          <div className="card">
            <div className="card-title">ميزان المراجعة</div>
            <div style={{ textAlign: 'center', color: '#666', padding: '3rem' }}>
              <div style={{ fontSize: '3rem', marginBottom: '1rem', opacity: 0.5 }}>⚖️</div>
              <div style={{ fontSize: '1.2rem', marginBottom: '1rem' }}>ميزان المراجعة</div>
              <div>قريباً...</div>
            </div>
          </div>
        );
      case 'income-statement':
        return (
          <div className="card">
            <div className="card-title">قائمة الدخل</div>
            <div style={{ textAlign: 'center', color: '#666', padding: '3rem' }}>
              <div style={{ fontSize: '3rem', marginBottom: '1rem', opacity: 0.5 }}>📈</div>
              <div style={{ fontSize: '1.2rem', marginBottom: '1rem' }}>قائمة الدخل</div>
              <div>قريباً...</div>
            </div>
          </div>
        );
      case 'balance-sheet':
        return (
          <div className="card">
            <div className="card-title">الميزانية العمومية</div>
            <div style={{ textAlign: 'center', color: '#666', padding: '3rem' }}>
              <div style={{ fontSize: '3rem', marginBottom: '1rem', opacity: 0.5 }}>📊</div>
              <div style={{ fontSize: '1.2rem', marginBottom: '1rem' }}>الميزانية العمومية</div>
              <div>قريباً...</div>
            </div>
          </div>
        );
      case 'cash-flow':
        return (
          <div className="card">
            <div className="card-title">قائمة التدفقات النقدية</div>
            <div style={{ textAlign: 'center', color: '#666', padding: '3rem' }}>
              <div style={{ fontSize: '3rem', marginBottom: '1rem', opacity: 0.5 }}>💰</div>
              <div style={{ fontSize: '1.2rem', marginBottom: '1rem' }}>قائمة التدفقات النقدية</div>
              <div>قريباً...</div>
            </div>
          </div>
        );
      case 'accounts-aging':
        return (
          <div className="card">
            <div className="card-title">تقرير أعمار الحسابات</div>
            <div style={{ textAlign: 'center', color: '#666', padding: '3rem' }}>
              <div style={{ fontSize: '3rem', marginBottom: '1rem', opacity: 0.5 }}>📅</div>
              <div style={{ fontSize: '1.2rem', marginBottom: '1rem' }}>تقرير أعمار الحسابات</div>
              <div>قريباً...</div>
            </div>
          </div>
        );
      case 'inventory-report':
        return <InventoryReports />;
      case 'accounts-tracker':
        return <AccountsTracker />;
      case 'cash-flow':
        return <CashFlowReports />;
      case 'advanced-reports':
        return <AdvancedReports />;
      case 'worker-vouchers':
        return (
          <div className="card">
            <div className="card-title">أذون العمال</div>
            <div style={{ textAlign: 'center', color: '#666', padding: '3rem' }}>
              <div style={{ fontSize: '3rem', marginBottom: '1rem', opacity: 0.5 }}>📋</div>
              <div style={{ fontSize: '1.2rem', marginBottom: '1rem' }}>أذون دفع العمال</div>
              <div>يمكن الوصول إليها من إدارة العمال → أذون العمال</div>
              <div style={{ marginTop: '1rem' }}>
                <button
                  className="btn btn-primary"
                  onClick={() => window.location.hash = '#labor-management'}
                >
                  الذهاب إلى إدارة العمال
                </button>
              </div>
            </div>
          </div>
        );
      default:
        return <InvoicesView />;
    }
  };

  return (
    <div className="container">
      {/* Header */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h2 style={{ margin: 0, color: '#333' }}>التقارير المالية</h2>
        </div>
      </div>

      {/* Navigation */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div className="card-title">اختر التقرير</div>
        <div className="grid grid-3" style={{ gap: '1rem' }}>
          <button
            className={`btn ${currentReport === 'invoices' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setCurrentReport('invoices')}
            style={{ padding: '1rem', height: 'auto' }}
          >
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '1.5rem', marginBottom: '0.5rem' }}>📋</div>
              <div>عرض الفواتير</div>
            </div>
          </button>

          <button
            className={`btn ${currentReport === 'issue-vouchers' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setCurrentReport('issue-vouchers')}
            style={{ padding: '1rem', height: 'auto' }}
          >
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '1.5rem', marginBottom: '0.5rem' }}>📄</div>
              <div>أذون الصرف</div>
            </div>
          </button>

          <button
            className={`btn ${currentReport === 'trial-balance' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setCurrentReport('trial-balance')}
            style={{ padding: '1rem', height: 'auto' }}
          >
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '1.5rem', marginBottom: '0.5rem' }}>⚖️</div>
              <div>ميزان المراجعة</div>
            </div>
          </button>

          <button
            className={`btn ${currentReport === 'income-statement' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setCurrentReport('income-statement')}
            style={{ padding: '1rem', height: 'auto' }}
          >
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '1.5rem', marginBottom: '0.5rem' }}>📈</div>
              <div>قائمة الدخل</div>
            </div>
          </button>

          <button
            className={`btn ${currentReport === 'balance-sheet' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setCurrentReport('balance-sheet')}
            style={{ padding: '1rem', height: 'auto' }}
          >
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '1.5rem', marginBottom: '0.5rem' }}>📊</div>
              <div>الميزانية العمومية</div>
            </div>
          </button>

          <button
            className={`btn ${currentReport === 'cash-flow' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setCurrentReport('cash-flow')}
            style={{ padding: '1rem', height: 'auto' }}
          >
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '1.5rem', marginBottom: '0.5rem' }}>💰</div>
              <div>التدفقات النقدية</div>
            </div>
          </button>

          <button
            className={`btn ${currentReport === 'accounts-aging' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setCurrentReport('accounts-aging')}
            style={{ padding: '1rem', height: 'auto' }}
          >
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '1.5rem', marginBottom: '0.5rem' }}>📅</div>
              <div>أعمار الحسابات</div>
            </div>
          </button>

          <button
            className={`btn ${currentReport === 'inventory-report' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setCurrentReport('inventory-report')}
            style={{ padding: '1rem', height: 'auto' }}
          >
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '1.5rem', marginBottom: '0.5rem' }}>📦</div>
              <div>تقرير المخزون</div>
            </div>
          </button>

          <button
            className={`btn ${currentReport === 'accounts-tracker' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setCurrentReport('accounts-tracker')}
            style={{ padding: '1rem', height: 'auto' }}
          >
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '1.5rem', marginBottom: '0.5rem' }}>📊</div>
              <div>كشف الحساب</div>
            </div>
          </button>



          <button
            className={`btn ${currentReport === 'advanced-reports' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setCurrentReport('advanced-reports')}
            style={{ padding: '1rem', height: 'auto' }}
          >
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '1.5rem', marginBottom: '0.5rem' }}>📈</div>
              <div>التقارير المتقدمة</div>
            </div>
          </button>

          <button
            className={`btn ${currentReport === 'worker-vouchers' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setCurrentReport('worker-vouchers')}
            style={{ padding: '1rem', height: 'auto' }}
          >
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '1.5rem', marginBottom: '0.5rem' }}>📋</div>
              <div>أذون العمال</div>
            </div>
          </button>
        </div>
      </div>

      {/* Report Content */}
      {renderReportContent()}
    </div>
  );
};

export default FinancialReports;
