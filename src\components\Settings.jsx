import React, { useState, useEffect } from 'react';
import { db } from '../database/db';
import DataExport from './DataExport';
import UserManagement from './UserManagement';
import DatabaseCheck from './DatabaseCheck';
import CompanySettings from './CompanySettings';
import DatabaseViewer from './DatabaseViewer';

const Settings = () => {
  const [loading, setLoading] = useState(false);
  const [backups, setBackups] = useState([]);
  const [systemStats, setSystemStats] = useState({});
  const [currentView, setCurrentView] = useState('backup');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadBackups(),
        loadSystemStats()
      ]);
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadBackups = async () => {
    try {
      const allBackups = await db.backups.orderBy('createdAt').reverse().toArray();
      setBackups(allBackups);
    } catch (error) {
      console.error('خطأ في تحميل النسخ الاحتياطية:', error);
    }
  };

  const loadSystemStats = async () => {
    try {
      const stats = {
        customers: await db.customers.count(),
        suppliers: await db.suppliers.count(),
        items: await db.items.count(),
        contracts: await db.contracts.count(),
        workers: await db.workers.count(),
        salesInvoices: await db.salesInvoices.count(),
        purchaseInvoices: await db.purchaseInvoices.count(),
        journalEntries: await db.journalEntries.count(),
        stockMovements: await db.stockMovements.count(),
        contractExpenses: await db.contractExpenses.count()
      };
      setSystemStats(stats);
    } catch (error) {
      console.error('خطأ في تحميل إحصائيات النظام:', error);
    }
  };

  const createBackup = async () => {
    try {
      setLoading(true);
      
      // جمع جميع البيانات
      const backupData = {
        customers: await db.customers.toArray(),
        suppliers: await db.suppliers.toArray(),
        items: await db.items.toArray(),
        accounts: await db.accounts.toArray(),
        contracts: await db.contracts.toArray(),
        workers: await db.workers.toArray(),
        salesInvoices: await db.salesInvoices.toArray(),
        purchaseInvoices: await db.purchaseInvoices.toArray(),
        journalEntries: await db.journalEntries.toArray(),
        stockMovements: await db.stockMovements.toArray(),
        contractExpenses: await db.contractExpenses.toArray(),
        laborCosts: await db.laborCosts.toArray(),
        payrolls: await db.payrolls.toArray(),
        workerPayments: await db.workerPayments.toArray(),
        settings: await db.settings.toArray()
      };

      // إنشاء معلومات النسخة الاحتياطية
      const backupInfo = {
        name: `نسخة احتياطية ${new Date().toLocaleDateString('ar-EG')} ${new Date().toLocaleTimeString('ar-EG')}`,
        data: backupData,
        size: JSON.stringify(backupData).length,
        recordsCount: Object.values(backupData).reduce((total, table) => total + table.length, 0),
        createdAt: new Date(),
        version: '1.0'
      };

      // حفظ النسخة الاحتياطية
      await db.backups.add(backupInfo);

      // تحميل ملف النسخة الاحتياطية
      const blob = new Blob([JSON.stringify(backupData, null, 2)], { 
        type: 'application/json' 
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `backup_${new Date().toISOString().split('T')[0]}.json`;
      link.click();
      URL.revokeObjectURL(url);

      alert('تم إنشاء النسخة الاحتياطية وتحميلها بنجاح');
      loadBackups();
    } catch (error) {
      console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
      alert('حدث خطأ أثناء إنشاء النسخة الاحتياطية');
    } finally {
      setLoading(false);
    }
  };

  const restoreBackup = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    if (!confirm('هل أنت متأكد من استعادة النسخة الاحتياطية؟ سيتم حذف جميع البيانات الحالية!')) {
      return;
    }

    try {
      setLoading(true);
      
      const text = await file.text();
      const backupData = JSON.parse(text);

      // مسح البيانات الحالية
      await db.transaction('rw', [
        db.customers, db.suppliers, db.items, db.accounts, db.contracts,
        db.workers, db.salesInvoices, db.purchaseInvoices, db.journalEntries,
        db.stockMovements, db.contractExpenses, db.laborCosts, db.payrolls,
        db.workerPayments, db.settings
      ], async () => {
        // مسح الجداول
        await db.customers.clear();
        await db.suppliers.clear();
        await db.items.clear();
        await db.accounts.clear();
        await db.contracts.clear();
        await db.workers.clear();
        await db.salesInvoices.clear();
        await db.purchaseInvoices.clear();
        await db.journalEntries.clear();
        await db.stockMovements.clear();
        await db.contractExpenses.clear();
        await db.laborCosts.clear();
        await db.payrolls.clear();
        await db.workerPayments.clear();
        await db.settings.clear();

        // استعادة البيانات
        if (backupData.customers) await db.customers.bulkAdd(backupData.customers);
        if (backupData.suppliers) await db.suppliers.bulkAdd(backupData.suppliers);
        if (backupData.items) await db.items.bulkAdd(backupData.items);
        if (backupData.accounts) await db.accounts.bulkAdd(backupData.accounts);
        if (backupData.contracts) await db.contracts.bulkAdd(backupData.contracts);
        if (backupData.workers) await db.workers.bulkAdd(backupData.workers);
        if (backupData.salesInvoices) await db.salesInvoices.bulkAdd(backupData.salesInvoices);
        if (backupData.purchaseInvoices) await db.purchaseInvoices.bulkAdd(backupData.purchaseInvoices);
        if (backupData.journalEntries) await db.journalEntries.bulkAdd(backupData.journalEntries);
        if (backupData.stockMovements) await db.stockMovements.bulkAdd(backupData.stockMovements);
        if (backupData.contractExpenses) await db.contractExpenses.bulkAdd(backupData.contractExpenses);
        if (backupData.laborCosts) await db.laborCosts.bulkAdd(backupData.laborCosts);
        if (backupData.payrolls) await db.payrolls.bulkAdd(backupData.payrolls);
        if (backupData.workerPayments) await db.workerPayments.bulkAdd(backupData.workerPayments);
        if (backupData.settings) await db.settings.bulkAdd(backupData.settings);
      });

      alert('تم استعادة النسخة الاحتياطية بنجاح! سيتم إعادة تحميل الصفحة.');
      window.location.reload();
    } catch (error) {
      console.error('خطأ في استعادة النسخة الاحتياطية:', error);
      alert('حدث خطأ أثناء استعادة النسخة الاحتياطية');
    } finally {
      setLoading(false);
    }
  };

  const deleteBackup = async (backupId) => {
    if (!confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟')) return;

    try {
      await db.backups.delete(backupId);
      alert('تم حذف النسخة الاحتياطية بنجاح');
      loadBackups();
    } catch (error) {
      console.error('خطأ في حذف النسخة الاحتياطية:', error);
      alert('حدث خطأ أثناء حذف النسخة الاحتياطية');
    }
  };

  const resetDatabase = async () => {
    if (!confirm('هل أنت متأكد من إعادة تعيين قاعدة البيانات؟ سيتم حذف جميع البيانات نهائياً!')) {
      return;
    }

    if (!confirm('تحذير: هذا الإجراء لا يمكن التراجع عنه! هل تريد المتابعة؟')) {
      return;
    }

    try {
      setLoading(true);
      await db.resetDatabase();
      alert('تم إعادة تعيين قاعدة البيانات بنجاح! سيتم إعادة تحميل الصفحة.');
      window.location.reload();
    } catch (error) {
      console.error('خطأ في إعادة تعيين قاعدة البيانات:', error);
      alert('حدث خطأ أثناء إعادة تعيين قاعدة البيانات');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="container">
        <div className="loading">
          <div className="spinner"></div>
          <div>جاري المعالجة...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="container">
      {/* Header */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h2 style={{ margin: 0, color: '#333' }}>⚙️ إعدادات النظام</h2>
        </div>
      </div>

      {/* Navigation */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div style={{ display: 'flex', gap: '1rem' }}>
          <button
            className={`btn ${currentView === 'backup' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setCurrentView('backup')}
          >
            💾 النسخ الاحتياطي
          </button>
          <button
            className={`btn ${currentView === 'stats' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setCurrentView('stats')}
          >
            📊 إحصائيات النظام
          </button>
          <button
            className={`btn ${currentView === 'maintenance' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setCurrentView('maintenance')}
          >
            🔧 صيانة النظام
          </button>
          <button
            className={`btn ${currentView === 'export' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setCurrentView('export')}
          >
            📊 تصدير البيانات
          </button>
          <button
            className={`btn ${currentView === 'users' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setCurrentView('users')}
          >
            👥 إدارة المستخدمين
          </button>
          <button
            className={`btn ${currentView === 'database-check' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setCurrentView('database-check')}
          >
            🔍 فحص قاعدة البيانات
          </button>
          <button
            className={`btn ${currentView === 'company' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setCurrentView('company')}
          >
            🏢 بيانات الشركة
          </button>
          <button
            className={`btn ${currentView === 'database-viewer' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setCurrentView('database-viewer')}
          >
            📋 عرض الجداول
          </button>
        </div>
      </div>

      {/* Backup Management */}
      {currentView === 'backup' && (
        <div>
          <div className="card" style={{ marginBottom: '2rem' }}>
            <div className="card-title">إدارة النسخ الاحتياطية</div>
            
            <div style={{ display: 'flex', gap: '1rem', marginBottom: '2rem' }}>
              <button
                className="btn btn-success"
                onClick={createBackup}
                disabled={loading}
              >
                💾 إنشاء نسخة احتياطية
              </button>
              
              <label className="btn btn-info" style={{ cursor: 'pointer' }}>
                📁 استعادة نسخة احتياطية
                <input
                  type="file"
                  accept=".json"
                  onChange={restoreBackup}
                  style={{ display: 'none' }}
                />
              </label>
            </div>

            <div style={{ 
              background: '#fff3cd', 
              border: '1px solid #ffeaa7', 
              borderRadius: '5px', 
              padding: '1rem',
              marginBottom: '2rem'
            }}>
              <div style={{ color: '#856404', fontWeight: 'bold', marginBottom: '0.5rem' }}>
                ⚠️ تنبيه مهم
              </div>
              <div style={{ color: '#856404', fontSize: '0.9rem' }}>
                • يتم حفظ النسخة الاحتياطية في المتصفح وتحميلها كملف JSON<br/>
                • احتفظ بالملف في مكان آمن<br/>
                • استعادة النسخة الاحتياطية ستحذف جميع البيانات الحالية
              </div>
            </div>
          </div>

          {/* Backups List */}
          <div className="card">
            <div className="card-title">النسخ الاحتياطية المحفوظة ({backups.length})</div>
            
            {backups.length > 0 ? (
              <div className="table-responsive">
                <table className="table">
                  <thead>
                    <tr>
                      <th>اسم النسخة</th>
                      <th>تاريخ الإنشاء</th>
                      <th>عدد السجلات</th>
                      <th>الحجم</th>
                      <th>إجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {backups.map(backup => (
                      <tr key={backup.id}>
                        <td>{backup.name}</td>
                        <td>{new Date(backup.createdAt).toLocaleString('ar-EG')}</td>
                        <td>{backup.recordsCount?.toLocaleString('ar-EG') || '-'}</td>
                        <td>{backup.size ? `${(backup.size / 1024).toFixed(1)} KB` : '-'}</td>
                        <td>
                          <button
                            className="btn btn-danger btn-sm"
                            onClick={() => deleteBackup(backup.id)}
                          >
                            🗑️ حذف
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div style={{ textAlign: 'center', color: '#666', padding: '2rem' }}>
                لا توجد نسخ احتياطية محفوظة
              </div>
            )}
          </div>
        </div>
      )}

      {/* System Statistics */}
      {currentView === 'stats' && (
        <div className="card">
          <div className="card-title">📊 إحصائيات النظام</div>

          <div className="grid grid-3">
            <div className="stat-card">
              <div className="stat-icon">👥</div>
              <div className="stat-content">
                <div className="stat-number">{systemStats.customers?.toLocaleString('ar-EG') || 0}</div>
                <div className="stat-label">العملاء</div>
              </div>
            </div>

            <div className="stat-card">
              <div className="stat-icon">🏢</div>
              <div className="stat-content">
                <div className="stat-number">{systemStats.suppliers?.toLocaleString('ar-EG') || 0}</div>
                <div className="stat-label">الموردين</div>
              </div>
            </div>

            <div className="stat-card">
              <div className="stat-icon">📦</div>
              <div className="stat-content">
                <div className="stat-number">{systemStats.items?.toLocaleString('ar-EG') || 0}</div>
                <div className="stat-label">الأصناف</div>
              </div>
            </div>

            <div className="stat-card">
              <div className="stat-icon">📄</div>
              <div className="stat-content">
                <div className="stat-number">{systemStats.contracts?.toLocaleString('ar-EG') || 0}</div>
                <div className="stat-label">العقود</div>
              </div>
            </div>

            <div className="stat-card">
              <div className="stat-icon">👷</div>
              <div className="stat-content">
                <div className="stat-number">{systemStats.workers?.toLocaleString('ar-EG') || 0}</div>
                <div className="stat-label">العمال</div>
              </div>
            </div>

            <div className="stat-card">
              <div className="stat-icon">🧾</div>
              <div className="stat-content">
                <div className="stat-number">{systemStats.salesInvoices?.toLocaleString('ar-EG') || 0}</div>
                <div className="stat-label">فواتير البيع</div>
              </div>
            </div>

            <div className="stat-card">
              <div className="stat-icon">📋</div>
              <div className="stat-content">
                <div className="stat-number">{systemStats.purchaseInvoices?.toLocaleString('ar-EG') || 0}</div>
                <div className="stat-label">فواتير الشراء</div>
              </div>
            </div>

            <div className="stat-card">
              <div className="stat-icon">📊</div>
              <div className="stat-content">
                <div className="stat-number">{systemStats.journalEntries?.toLocaleString('ar-EG') || 0}</div>
                <div className="stat-label">القيود المحاسبية</div>
              </div>
            </div>

            <div className="stat-card">
              <div className="stat-icon">📈</div>
              <div className="stat-content">
                <div className="stat-number">{systemStats.stockMovements?.toLocaleString('ar-EG') || 0}</div>
                <div className="stat-label">حركات المخزون</div>
              </div>
            </div>
          </div>

          <div style={{ marginTop: '2rem', padding: '1rem', background: '#f8f9fa', borderRadius: '5px' }}>
            <h4 style={{ margin: '0 0 1rem 0', color: '#495057' }}>معلومات إضافية</h4>
            <div className="grid grid-2">
              <div>
                <strong>إجمالي مصروفات العقود:</strong> {systemStats.contractExpenses?.toLocaleString('ar-EG') || 0}
              </div>
              <div>
                <strong>تاريخ آخر تحديث:</strong> {new Date().toLocaleString('ar-EG')}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* System Maintenance */}
      {currentView === 'maintenance' && (
        <div className="card">
          <div className="card-title">🔧 صيانة النظام</div>

          <div style={{ marginBottom: '2rem' }}>
            <h4 style={{ color: '#dc3545', marginBottom: '1rem' }}>⚠️ منطقة خطر</h4>

            <div style={{
              background: '#f8d7da',
              border: '1px solid #f5c6cb',
              borderRadius: '5px',
              padding: '1rem',
              marginBottom: '2rem'
            }}>
              <div style={{ color: '#721c24', fontWeight: 'bold', marginBottom: '0.5rem' }}>
                تحذير: العمليات التالية خطيرة ولا يمكن التراجع عنها!
              </div>
              <div style={{ color: '#721c24', fontSize: '0.9rem' }}>
                تأكد من إنشاء نسخة احتياطية قبل تنفيذ أي من هذه العمليات
              </div>
            </div>

            <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
              <button
                className="btn btn-danger"
                onClick={resetDatabase}
                style={{ padding: '1rem', fontSize: '1rem' }}
              >
                🗑️ إعادة تعيين قاعدة البيانات (حذف جميع البيانات)
              </button>

              <button
                className="btn btn-warning"
                onClick={() => {
                  if (confirm('هل تريد إعادة تحميل الصفحة؟')) {
                    window.location.reload();
                  }
                }}
                style={{ padding: '1rem', fontSize: '1rem' }}
              >
                🔄 إعادة تحميل النظام
              </button>
            </div>
          </div>

          <div style={{ marginTop: '2rem', padding: '1rem', background: '#d1ecf1', borderRadius: '5px' }}>
            <h4 style={{ margin: '0 0 1rem 0', color: '#0c5460' }}>معلومات النظام</h4>
            <div style={{ color: '#0c5460' }}>
              <div><strong>إصدار النظام:</strong> 1.0.0</div>
              <div><strong>قاعدة البيانات:</strong> IndexedDB</div>
              <div><strong>المتصفح:</strong> {navigator.userAgent.split(' ')[0]}</div>
              <div><strong>التاريخ:</strong> {new Date().toLocaleString('ar-EG')}</div>
            </div>
          </div>
        </div>
      )}

      {/* Data Export */}
      {currentView === 'export' && <DataExport />}

      {/* User Management */}
      {currentView === 'users' && <UserManagement />}

      {/* Database Check */}
      {currentView === 'database-check' && <DatabaseCheck />}

      {/* Company Settings */}
      {currentView === 'company' && <CompanySettings />}

      {/* Database Viewer */}
      {currentView === 'database-viewer' && <DatabaseViewer />}
    </div>
  );
};

export default Settings;
