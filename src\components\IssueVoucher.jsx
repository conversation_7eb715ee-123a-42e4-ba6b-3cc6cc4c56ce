import React, { useState, useEffect } from 'react';
import { db } from '../database/db';

const IssueVoucher = ({ contractId, contract, voucherId = null, onClose, onSave }) => {
  const [loading, setLoading] = useState(true);
  const [items, setItems] = useState([]);
  const [workers, setWorkers] = useState([]);
  const [activeTab, setActiveTab] = useState('material');
  const [voucherItems, setVoucherItems] = useState([]);
  const [isEditing, setIsEditing] = useState(false);
  const [isPosted, setIsPosted] = useState(false);
  
  const [voucherData, setVoucherData] = useState({
    voucherNumber: '',
    date: new Date().toISOString().split('T')[0],
    requestedBy: '',
    department: '',
    status: 'pending',
    notes: ''
  });

  const [currentItem, setCurrentItem] = useState({
    type: 'material',
    itemId: '',
    workerId: '',
    description: '',
    quantity: 0,
    unit: '',
    unitCost: 0,
    totalCost: 0,
    notes: ''
  });

  useEffect(() => {
    loadData();
  }, [voucherId]);

  const loadData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadItems(),
        loadWorkers(),
        voucherId ? loadExistingVoucher() : generateVoucherNumber()
      ]);
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadExistingVoucher = async () => {
    try {
      const expense = await db.contractExpenses.get(voucherId);
      if (expense && expense.type === 'voucher') {
        setVoucherData({
          voucherNumber: expense.voucherNumber,
          date: expense.date,
          requestedBy: expense.requestedBy || '',
          department: expense.department || '',
          status: expense.status || 'pending',
          notes: expense.notes || ''
        });
        
        if (expense.items && Array.isArray(expense.items)) {
          setVoucherItems(expense.items);
        }
        setIsEditing(true);
        setIsPosted(expense.status === 'posted');
      }
    } catch (error) {
      console.error('خطأ في تحميل الإذن:', error);
    }
  };

  const loadItems = async () => {
    try {
      const allItems = await db.items.where('currentStock').above(0).toArray();
      setItems(allItems);
    } catch (error) {
      console.error('خطأ في تحميل الأصناف:', error);
    }
  };

  const loadWorkers = async () => {
    try {
      const allWorkers = await db.workers.toArray();
      setWorkers(allWorkers.filter(worker => worker.isActive !== false));
    } catch (error) {
      console.error('خطأ في تحميل العمال:', error);
    }
  };

  const generateVoucherNumber = async () => {
    try {
      const count = await db.contractExpenses.where('type').equals('voucher').count();
      const voucherNumber = `VOU${String(count + 1).padStart(6, '0')}`;
      setVoucherData(prev => ({ ...prev, voucherNumber }));
    } catch (error) {
      console.error('خطأ في إنشاء رقم الإذن:', error);
    }
  };

  const handleSaveVoucher = async () => {
    try {
      if (voucherItems.length === 0) {
        alert('يرجى إضافة أصناف أو عمالة قبل الحفظ');
        return;
      }

      if (!voucherData.requestedBy.trim()) {
        alert('يرجى إدخال اسم طالب الصرف');
        return;
      }

      if (!voucherData.department) {
        alert('يرجى اختيار القسم');
        return;
      }

      const voucherPayload = {
        contractId: parseInt(contractId),
        type: 'voucher',
        voucherNumber: voucherData.voucherNumber,
        description: 'إذن صرف مختلط',
        amount: getTotalVoucherCost(),
        date: voucherData.date,
        reference: voucherData.voucherNumber,
        requestedBy: voucherData.requestedBy,
        department: voucherData.department,
        notes: voucherData.notes,
        status: voucherData.status,
        items: voucherItems,
        userId: 1,
        createdAt: isEditing ? undefined : new Date()
      };

      if (isEditing && voucherId) {
        // في حالة التعديل، لا نخصم من المخزون مرة أخرى
        await db.contractExpenses.update(voucherId, voucherPayload);
        alert('تم تحديث إذن الصرف بنجاح');
      } else {
        // في حالة الإنشاء الجديد، نخصم من المخزون فوراً
        await processInventoryDeductions();
        await db.contractExpenses.add(voucherPayload);
        alert('تم حفظ إذن الصرف وخصم المواد من المخزون بنجاح');
      }

      onSave && onSave();
      onClose && onClose();
    } catch (error) {
      console.error('خطأ في حفظ إذن الصرف:', error);
      alert(`حدث خطأ أثناء حفظ إذن الصرف: ${error.message}`);
    }
  };

  const processInventoryDeductions = async () => {
    // خصم المواد من المخزون
    for (const item of voucherItems) {
      if (item.type === 'material' && item.itemId) {
        try {
          // الحصول على بيانات الصنف الحالية
          const inventoryItem = await db.items.get(parseInt(item.itemId));
          if (inventoryItem) {
            const newStock = inventoryItem.currentStock - item.quantity;

            if (newStock < 0) {
              throw new Error(`الكمية المطلوبة من ${item.description} (${item.quantity}) أكبر من المتاح في المخزون (${inventoryItem.currentStock})`);
            }

            // تحديث رصيد المخزون
            await db.items.update(parseInt(item.itemId), {
              currentStock: newStock
            });

            // إضافة حركة مخزون
            await db.stockMovements.add({
              itemId: parseInt(item.itemId),
              type: 'out',
              quantity: item.quantity,
              cost: item.unitCost,
              reference: `إذن صرف ${voucherData.voucherNumber}`,
              date: new Date(voucherData.date),
              userId: 1,
              notes: `خصم بموجب إذن صرف - العقد: ${contract?.contractNumber || ''}`
            });
          }
        } catch (error) {
          throw new Error(`خطأ في خصم ${item.description}: ${error.message}`);
        }
      }
    }
  };

  const handleDeleteVoucher = async () => {
    if (!isEditing || !voucherId) return;

    if (window.confirm('هل أنت متأكد من حذف إذن الصرف؟ لا يمكن التراجع عن هذا الإجراء.')) {
      try {
        await db.contractExpenses.delete(voucherId);
        alert('تم حذف إذن الصرف بنجاح');
        onSave && onSave();
        onClose && onClose();
      } catch (error) {
        console.error('خطأ في حذف إذن الصرف:', error);
        alert('حدث خطأ أثناء حذف إذن الصرف');
      }
    }
  };

  const printVoucher = () => {
    const printContent = generateVoucherPrintContent();

    const printWindow = window.open('', '_blank');
    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.focus();

    setTimeout(() => {
      printWindow.print();
    }, 250);
  };

  const generateVoucherPrintContent = () => {
    const itemsTableRows = voucherItems.map(item => `
      <tr>
        <td>${item.type === 'material' ? '📦 مادة' : '👷 عمالة'}</td>
        <td>${item.description}</td>
        <td>${item.quantity}</td>
        <td>${item.unit}</td>
        <td>${item.unitCost.toLocaleString('ar-EG')}</td>
        <td style="font-weight: bold;">${item.totalCost.toLocaleString('ar-EG')}</td>
        <td>${item.notes || '-'}</td>
      </tr>
    `).join('');

    const totalCost = voucherItems.reduce((sum, item) => sum + item.totalCost, 0);

    return `
      <!DOCTYPE html>
      <html dir="rtl">
      <head>
        <meta charset="UTF-8">
        <title>إذن صرف - ${voucherData.voucherNumber}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
          .header h1 { color: #333; margin-bottom: 10px; }
          .voucher-info { display: flex; justify-content: space-between; margin-bottom: 20px; }
          .voucher-info div { background: #f8f9fa; padding: 10px; border-radius: 5px; }
          .items-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
          .items-table th, .items-table td { border: 1px solid #ddd; padding: 8px; text-align: right; }
          .items-table th { background-color: #f8f9fa; font-weight: bold; }
          .total { text-align: center; font-size: 1.5rem; font-weight: bold; color: #dc3545; margin: 20px 0; }
          .signatures { display: flex; justify-content: space-between; margin-top: 50px; }
          .signature { text-align: center; width: 200px; }
          .signature-line { border-top: 1px solid #333; margin-top: 50px; padding-top: 10px; }
          @media print { body { margin: 0; } }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>إذن صرف</h1>
          <p>رقم الإذن: ${voucherData.voucherNumber}</p>
        </div>

        <div class="voucher-info">
          <div>
            <strong>العقد:</strong> ${contract?.contractNumber} - ${contract?.name}<br>
            <strong>طالب الصرف:</strong> ${voucherData.requestedBy}<br>
            <strong>القسم:</strong> ${voucherData.department}
          </div>
          <div>
            <strong>التاريخ:</strong> ${new Date(voucherData.date).toLocaleDateString('ar-EG')}<br>
            <strong>الحالة:</strong> ${voucherData.status === 'pending' ? 'معلق' : voucherData.status === 'posted' ? 'مُرحل' : voucherData.status}
          </div>
        </div>

        ${voucherItems.length > 0 ? `
        <h3 style="margin-top: 30px; margin-bottom: 15px; color: #333;">تفاصيل البنود:</h3>
        <table class="items-table">
          <thead>
            <tr>
              <th>النوع</th>
              <th>الوصف</th>
              <th>الكمية</th>
              <th>الوحدة</th>
              <th>سعر الوحدة (ج.م)</th>
              <th>الإجمالي (ج.م)</th>
              <th>الملاحظات</th>
            </tr>
          </thead>
          <tbody>
            ${itemsTableRows}
          </tbody>
        </table>
        ` : ''}

        <div class="total">
          إجمالي التكلفة: ${totalCost.toLocaleString('ar-EG')} ج.م
        </div>

        ${voucherData.notes ? `
        <div style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 5px;">
          <strong>ملاحظات:</strong><br>
          ${voucherData.notes}
        </div>
        ` : ''}

        <div class="signatures">
          <div class="signature">
            <div class="signature-line">طالب الصرف</div>
          </div>
          <div class="signature">
            <div class="signature-line">أمين المخزن</div>
          </div>
          <div class="signature">
            <div class="signature-line">المدير المالي</div>
          </div>
        </div>
      </body>
      </html>
    `;
  };

  const handleItemSelect = (itemId) => {
    const selectedItem = items.find(item => item.id === parseInt(itemId));
    if (selectedItem) {
      setCurrentItem({
        ...currentItem,
        itemId: itemId,
        description: `${selectedItem.code} - ${selectedItem.name}`,
        unit: selectedItem.unit,
        unitCost: selectedItem.lastCost || selectedItem.avgCost || 0
      });
    }
  };

  const handleWorkerSelect = (workerId) => {
    const selectedWorker = workers.find(worker => worker.id === parseInt(workerId));
    if (selectedWorker) {
      setCurrentItem({
        ...currentItem,
        workerId: workerId,
        description: `${selectedWorker.code} - ${selectedWorker.name}`,
        unit: 'ساعة',
        unitCost: selectedWorker.hourlyRate || 0
      });
    }
  };

  const calculateItemTotal = () => {
    const quantity = parseFloat(currentItem.quantity) || 0;
    const unitCost = parseFloat(currentItem.unitCost) || 0;
    return quantity * unitCost;
  };

  const addItemToVoucher = () => {
    if (currentItem.type === 'material' && !currentItem.itemId) {
      alert('يرجى اختيار صنف');
      return;
    }

    if (currentItem.type === 'labor' && !currentItem.workerId) {
      alert('يرجى اختيار عامل');
      return;
    }

    const quantity = parseFloat(currentItem.quantity);
    if (!quantity || quantity <= 0) {
      alert('يرجى إدخال كمية صحيحة أكبر من صفر');
      return;
    }

    const unitCost = parseFloat(currentItem.unitCost);
    if (!unitCost || unitCost <= 0) {
      alert('يرجى إدخال سعر صحيح أكبر من صفر');
      return;
    }

    if (currentItem.type === 'material') {
      const selectedItem = items.find(item => item.id === parseInt(currentItem.itemId));
      if (selectedItem && quantity > selectedItem.currentStock) {
        alert(`الكمية المطلوبة (${quantity}) أكبر من المتاح في المخزون (${selectedItem.currentStock})`);
        return;
      }
    }

    const totalCost = calculateItemTotal();
    const newItem = {
      ...currentItem,
      id: Date.now(),
      quantity: quantity,
      unitCost: unitCost,
      totalCost: totalCost
    };

    setVoucherItems(prev => [...prev, newItem]);

    setCurrentItem({
      type: activeTab,
      itemId: '',
      workerId: '',
      description: '',
      quantity: 0,
      unit: '',
      unitCost: 0,
      totalCost: 0,
      notes: ''
    });
  };

  const removeItemFromVoucher = (itemId) => {
    setVoucherItems(prev => prev.filter(item => item.id !== itemId));
  };

  const getTotalVoucherCost = () => {
    return voucherItems.reduce((total, item) => total + item.totalCost, 0);
  };

  if (loading) {
    return (
      <div style={{ padding: '2rem', textAlign: 'center' }}>
        <div className="spinner"></div>
        <div>جاري التحميل...</div>
      </div>
    );
  }

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'rgba(0,0,0,0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{
        background: 'white',
        width: '95%',
        maxWidth: '1200px',
        height: '90vh',
        borderRadius: '10px',
        overflow: 'auto',
        padding: '2rem'
      }}>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center', 
          marginBottom: '2rem',
          borderBottom: '2px solid #007bff',
          paddingBottom: '1rem'
        }}>
          <h2 style={{ margin: 0, color: '#007bff' }}>
            📋 {isEditing ? (isPosted ? 'عرض إذن صرف (مُرحل)' : 'تعديل إذن صرف') : 'إضافة إذن صرف جديد'}
          </h2>
          <div style={{ display: 'flex', gap: '1rem' }}>
            {isEditing && (
              <button
                className="btn btn-info"
                onClick={printVoucher}
                style={{ padding: '0.5rem 1rem' }}
              >
                🖨️ طباعة
              </button>
            )}
            {isEditing && !isPosted && (
              <button
                className="btn btn-danger"
                onClick={handleDeleteVoucher}
                style={{ padding: '0.5rem 1rem' }}
              >
                🗑️ حذف
              </button>
            )}
            <button
              className="btn btn-secondary"
              onClick={onClose}
              style={{ padding: '0.5rem 1rem' }}
            >
              ✕ إغلاق
            </button>
          </div>
        </div>

        {/* تنبيه للأذون المرحلة */}
        {isPosted && (
          <div style={{
            background: '#d1ecf1',
            border: '1px solid #bee5eb',
            borderRadius: '5px',
            padding: '1rem',
            marginBottom: '2rem',
            textAlign: 'center'
          }}>
            <div style={{ color: '#0c5460', fontWeight: 'bold', marginBottom: '0.5rem' }}>
              📌 إذن صرف مُرحل
            </div>
            <div style={{ color: '#0c5460', fontSize: '0.9rem' }}>
              هذا الإذن تم ترحيله ولا يمكن تعديله. تم خصم المواد من المخزون عند الإنشاء.
            </div>
          </div>
        )}

        {/* تنبيه عند الإنشاء */}
        {!isEditing && (
          <div style={{
            background: '#fff3cd',
            border: '1px solid #ffeaa7',
            borderRadius: '5px',
            padding: '1rem',
            marginBottom: '2rem',
            textAlign: 'center'
          }}>
            <div style={{ color: '#856404', fontWeight: 'bold', marginBottom: '0.5rem' }}>
              ⚠️ تنبيه مهم
            </div>
            <div style={{ color: '#856404', fontSize: '0.9rem' }}>
              سيتم خصم المواد من المخزون فور حفظ إذن الصرف بغض النظر عن حالته.
            </div>
          </div>
        )}

        <div className="card" style={{ marginBottom: '2rem' }}>
          <div className="card-title">المعلومات الأساسية</div>
          
          <div className="grid grid-3">
            <div className="form-group">
              <label className="form-label">رقم الإذن</label>
              <input
                type="text"
                className="form-control"
                value={voucherData.voucherNumber}
                style={{ background: '#f8f9fa' }}
                readOnly
              />
            </div>

            <div className="form-group">
              <label className="form-label">تاريخ الإذن</label>
              <input
                type="date"
                className="form-control"
                value={voucherData.date}
                onChange={(e) => setVoucherData({...voucherData, date: e.target.value})}
                disabled={isPosted}
              />
            </div>

            <div className="form-group">
              <label className="form-label">العقد</label>
              <input
                type="text"
                className="form-control"
                value={contract ? `${contract.contractNumber} - ${contract.name}` : ''}
                style={{ background: '#f8f9fa' }}
                readOnly
              />
            </div>
          </div>

          <div className="grid grid-3">
            <div className="form-group">
              <label className="form-label">طالب الصرف *</label>
              <input
                type="text"
                className="form-control"
                value={voucherData.requestedBy}
                onChange={(e) => setVoucherData({...voucherData, requestedBy: e.target.value})}
                placeholder="اسم طالب الصرف"
                disabled={isPosted}
              />
            </div>

            <div className="form-group">
              <label className="form-label">القسم *</label>
              <select
                className="form-control"
                value={voucherData.department}
                onChange={(e) => setVoucherData({...voucherData, department: e.target.value})}
                disabled={isPosted}
              >
                <option value="">اختر القسم</option>
                <option value="construction">الإنشاءات</option>
                <option value="maintenance">الصيانة</option>
                <option value="electrical">الكهرباء</option>
                <option value="plumbing">السباكة</option>
                <option value="finishing">التشطيبات</option>
                <option value="other">أخرى</option>
              </select>
            </div>

            <div className="form-group">
              <label className="form-label">الحالة</label>
              <select
                className="form-control"
                value={voucherData.status}
                onChange={(e) => setVoucherData({...voucherData, status: e.target.value})}
                disabled={isPosted}
              >
                <option value="pending">في الانتظار</option>
                <option value="issued">مُصدر</option>
                <option value="posted">مُرحل</option>
                <option value="cancelled">ملغي</option>
              </select>
            </div>
          </div>

          <div className="form-group">
            <label className="form-label">ملاحظات</label>
            <textarea
              className="form-control"
              value={voucherData.notes}
              onChange={(e) => setVoucherData({...voucherData, notes: e.target.value})}
              rows="2"
              placeholder="ملاحظات إضافية..."
              disabled={isPosted}
            />
          </div>
        </div>

        {/* إضافة الأصناف والعمالة */}
        {!isPosted && (
          <div className="card" style={{ marginBottom: '2rem' }}>
          <div className="card-title">إضافة أصناف وعمالة</div>

          {/* أزرار التبويب */}
          <div style={{ display: 'flex', marginBottom: '1rem', borderBottom: '1px solid #ddd' }}>
            <button
              className={`btn ${activeTab === 'material' ? 'btn-primary' : 'btn-outline-primary'}`}
              onClick={() => {
                setActiveTab('material');
                setCurrentItem({...currentItem, type: 'material'});
              }}
              style={{ borderRadius: '0', borderBottom: 'none' }}
            >
              📦 المواد
            </button>
            <button
              className={`btn ${activeTab === 'labor' ? 'btn-primary' : 'btn-outline-primary'}`}
              onClick={() => {
                setActiveTab('labor');
                setCurrentItem({...currentItem, type: 'labor'});
              }}
              style={{ borderRadius: '0', borderBottom: 'none' }}
            >
              👷 العمالة
            </button>
          </div>

          {/* نموذج إضافة المواد */}
          {activeTab === 'material' && (
            <div className="grid grid-4">
              <div className="form-group">
                <label className="form-label">الصنف</label>
                <select
                  className="form-control"
                  value={currentItem.itemId}
                  onChange={(e) => handleItemSelect(e.target.value)}
                >
                  <option value="">اختر صنف</option>
                  {items.map(item => (
                    <option key={item.id} value={item.id}>
                      {item.code} - {item.name} (متاح: {item.currentStock})
                    </option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label className="form-label">الكمية</label>
                <input
                  type="number"
                  className="form-control"
                  value={currentItem.quantity}
                  onChange={(e) => setCurrentItem({...currentItem, quantity: e.target.value})}
                  placeholder="0"
                  step="0.01"
                />
              </div>

              <div className="form-group">
                <label className="form-label">سعر الوحدة</label>
                <input
                  type="number"
                  className="form-control"
                  value={currentItem.unitCost}
                  onChange={(e) => setCurrentItem({...currentItem, unitCost: e.target.value})}
                  placeholder="0.00"
                  step="0.01"
                />
              </div>

              <div className="form-group">
                <label className="form-label">الإجمالي</label>
                <input
                  type="text"
                  className="form-control"
                  value={calculateItemTotal().toLocaleString('ar-EG')}
                  style={{ background: '#f8f9fa' }}
                  readOnly
                />
              </div>
            </div>
          )}

          {/* نموذج إضافة العمالة */}
          {activeTab === 'labor' && (
            <div className="grid grid-4">
              <div className="form-group">
                <label className="form-label">العامل</label>
                <select
                  className="form-control"
                  value={currentItem.workerId}
                  onChange={(e) => handleWorkerSelect(e.target.value)}
                >
                  <option value="">اختر عامل</option>
                  {workers.map(worker => (
                    <option key={worker.id} value={worker.id}>
                      {worker.code} - {worker.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label className="form-label">عدد الساعات</label>
                <input
                  type="number"
                  className="form-control"
                  value={currentItem.quantity}
                  onChange={(e) => setCurrentItem({...currentItem, quantity: e.target.value})}
                  placeholder="0"
                  step="0.5"
                />
              </div>

              <div className="form-group">
                <label className="form-label">سعر الساعة</label>
                <input
                  type="number"
                  className="form-control"
                  value={currentItem.unitCost}
                  onChange={(e) => setCurrentItem({...currentItem, unitCost: e.target.value})}
                  placeholder="0.00"
                  step="0.01"
                />
              </div>

              <div className="form-group">
                <label className="form-label">الإجمالي</label>
                <input
                  type="text"
                  className="form-control"
                  value={calculateItemTotal().toLocaleString('ar-EG')}
                  style={{ background: '#f8f9fa' }}
                  readOnly
                />
              </div>
            </div>
          )}

          <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '1rem' }}>
            <button
              className="btn btn-success"
              onClick={addItemToVoucher}
              style={{ padding: '0.5rem 1.5rem' }}
            >
              ➕ إضافة
            </button>
          </div>
        </div>
        )}

        {/* قائمة الأصناف المضافة */}
        {voucherItems.length > 0 && (
          <div className="card" style={{ marginBottom: '2rem' }}>
            <div className="card-title">الأصناف والعمالة المضافة</div>

            <div className="table-responsive">
              <table className="table">
                <thead>
                  <tr>
                    <th>النوع</th>
                    <th>الوصف</th>
                    <th>الكمية</th>
                    <th>الوحدة</th>
                    <th>سعر الوحدة</th>
                    <th>الإجمالي</th>
                    <th>إجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {voucherItems.map(item => (
                    <tr key={item.id}>
                      <td>
                        <span className={`badge ${item.type === 'material' ? 'badge-primary' : 'badge-success'}`}>
                          {item.type === 'material' ? '📦 مادة' : '👷 عمالة'}
                        </span>
                      </td>
                      <td>{item.description}</td>
                      <td>{item.quantity}</td>
                      <td>{item.unit}</td>
                      <td>{item.unitCost.toLocaleString('ar-EG')}</td>
                      <td>{item.totalCost.toLocaleString('ar-EG')}</td>
                      <td>
                        {!isPosted && (
                          <button
                            className="btn btn-danger btn-sm"
                            onClick={() => removeItemFromVoucher(item.id)}
                          >
                            🗑️
                          </button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          borderTop: '1px solid #ddd',
          paddingTop: '1rem'
        }}>
          <div style={{ fontSize: '1.2rem', fontWeight: 'bold', color: '#007bff' }}>
            إجمالي الإذن: {getTotalVoucherCost().toLocaleString('ar-EG')} ج.م
          </div>
          
          <div style={{ display: 'flex', gap: '1rem' }}>
            {!isPosted && (
              <button
                className="btn btn-primary"
                onClick={handleSaveVoucher}
                style={{ padding: '0.75rem 2rem' }}
              >
                💾 {isEditing ? 'تحديث الإذن' : 'حفظ الإذن'}
              </button>
            )}
            <button
              className="btn btn-secondary"
              onClick={onClose}
              style={{ padding: '0.75rem 2rem' }}
            >
              {isPosted ? 'إغلاق' : 'إلغاء'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IssueVoucher;
