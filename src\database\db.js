import Dexie from 'dexie';

// تعريف قاعدة البيانات
export class AccountingDB extends Dexie {
  constructor() {
    super('AccountingSystem');
    
    this.version(5).stores({
      // المستخدمين والصلاحيات
      users: '++id, username, password, role, permissions, isActive, createdAt',
      
      // دليل الحسابات (5 مستويات)
      accounts: '++id, code, name, parentId, level, type, isActive, balance, createdAt',
      
      // العملاء
      customers: '++id, name, phone, email, address, taxNumber, notes, accountNumber, isActive, createdAt, updatedAt',

      // الموردين
      suppliers: '++id, name, phone, email, address, taxNumber, notes, accountNumber, isActive, createdAt, updatedAt',
      
      // الأصناف والمخزون
      items: '++id, code, name, unit, category, minStock, currentStock, avgCost, lastCost, isActive, createdAt',
      
      // حركات المخزون
      stockMovements: '++id, itemId, type, quantity, cost, reference, date, userId, notes',
      
      // فواتير المبيعات
      salesInvoices: '++id, invoiceNumber, customerId, date, dueDate, items, subtotal, discountAmount, taxAmount, total, totalCost, paidAmount, status, notes, hasTax, taxRate, discountType, discount, userId, createdAt',

      // فواتير المشتريات
      purchaseInvoices: '++id, invoiceNumber, supplierId, date, dueDate, items, subtotal, discountAmount, taxAmount, total, paidAmount, status, notes, hasTax, taxRate, discountType, discount, userId, createdAt',
      
      // القيود المحاسبية
      journalEntries: '++id, entryNumber, date, description, reference, type, entries, totalDebit, totalCredit, userId, createdAt',

      // تفاصيل القيود المحاسبية
      journalEntryDetails: '++id, entryId, accountId, debit, credit, description',
      
      // العقود
      contracts: '++id, contractNumber, customerId, name, description, startDate, endDate, totalValue, paidAmount, status, userId, createdAt',
      
      // مصروفات العقود
      contractExpenses: '++id, contractId, type, voucherNumber, description, amount, date, reference, requestedBy, department, notes, status, items, userId, createdAt',

      // أذون الصرف
      issueVouchers: '++id, voucherNumber, contractId, date, requestedBy, department, status, notes, totalCost, userId, createdAt',
      
      // العمال
      workers: '++id, code, name, phone, address, dailyRate, hourlyRate, isActive, createdAt',
      
      // مصنعيات العمال
      workerPayments: '++id, workerId, contractId, date, hours, days, amount, description, voucherNumber, isPaid, userId, createdAt',

      // تكاليف العمالة
      laborCosts: '++id, contractId, workerId, date, hoursWorked, hourlyRate, totalCost, description, voucherNumber, voucherId, isPaid, userId, createdAt',

      // كشوف المرتبات
      payrolls: '++id, payrollNumber, month, year, totalAmount, userId, createdAt, isPosted',
      
      // تفاصيل كشوف المرتبات
      payrollDetails: '++id, payrollId, workerId, basicSalary, overtime, deductions, netSalary',

      // أذون دفع العمال
      workerVouchers: '++id, voucherNumber, workerId, workerName, dateFrom, dateTo, sessionIds, totalAmount, status, journalEntryId, createdAt, createdBy, updatedAt',

      // جلسات عمل العمال
      workSessions: '++id, workerId, contractId, date, hoursWorked, hourlyRate, totalAmount, notes, isPaid, voucherId, paidAt, createdAt',

      // إعدادات النظام
      settings: '++id, key, value, description, userId, updatedAt',

      // ربط الحسابات مع الجداول
      accountLinks: '++id, linkType, accountId, accountCode, accountName, description, isRequired, createdAt, updatedAt',
      
      // النسخ الاحتياطية
      backups: '++id, name, data, size, createdAt, userId',

      // قوالب القيود اليومية
      journalTemplates: '++id, name, description, type, entries, createdAt, userId',

      // أذون صرف العمالة
      workerVouchers: '++id, voucherNumber, workerName, date, amount, paidAmount, paymentStatus, description, createdAt, userId',

      // أذون صرف العمالة (للنظام الجديد)
      laborVouchers: '++id, voucherNumber, workerName, projectName, date, amount, status, createdAt, userId',

      // الفترات المحاسبية
      accountingPeriods: '++id, year, startDate, endDate, status, createdBy, closedBy, reopenedBy, createdAt, closedAt, reopenedAt, updatedAt'
    });

    // معالجة الترقية من الإصدارات السابقة
    this.version(4).stores({
      users: '++id, username, password, role, permissions, isActive, createdAt',
      accounts: '++id, code, name, parentId, level, type, isActive, balance, createdAt',
      customers: '++id, name, phone, email, address, taxNumber, notes, accountNumber, isActive, createdAt, updatedAt',
      suppliers: '++id, name, phone, email, address, taxNumber, notes, accountNumber, isActive, createdAt, updatedAt',
      items: '++id, code, name, unit, category, minStock, currentStock, avgCost, lastCost, isActive, createdAt',
      stockMovements: '++id, itemId, type, quantity, cost, reference, date, userId, notes',
      salesInvoices: '++id, invoiceNumber, customerId, date, dueDate, items, subtotal, discountAmount, taxAmount, total, totalCost, paidAmount, status, notes, hasTax, taxRate, discountType, discount, userId, createdAt',
      purchaseInvoices: '++id, invoiceNumber, supplierId, date, dueDate, items, subtotal, discountAmount, taxAmount, total, paidAmount, status, notes, hasTax, taxRate, discountType, discount, userId, createdAt',
      journalEntries: '++id, entryNumber, date, description, reference, type, entries, totalDebit, totalCredit, userId, createdAt',
      journalEntryDetails: '++id, entryId, accountId, debit, credit, description',
      contracts: '++id, contractNumber, customerId, name, description, startDate, endDate, totalValue, paidAmount, status, userId, createdAt',
      contractExpenses: '++id, contractId, type, voucherNumber, description, amount, date, reference, requestedBy, department, notes, status, items, userId, createdAt',
      issueVouchers: '++id, voucherNumber, contractId, date, requestedBy, department, status, notes, totalCost, userId, createdAt',
      workers: '++id, code, name, phone, address, dailyRate, hourlyRate, isActive, createdAt',
      workerPayments: '++id, workerId, contractId, date, hours, days, amount, description, voucherNumber, isPaid, userId, createdAt',
      laborCosts: '++id, contractId, workerId, date, hoursWorked, hourlyRate, totalCost, description, voucherNumber, voucherId, isPaid, userId, createdAt',
      payrolls: '++id, payrollNumber, month, year, totalAmount, userId, createdAt, isPosted',
      payrollDetails: '++id, payrollId, workerId, basicSalary, overtime, deductions, netSalary',
      settings: '++id, key, value, description, userId, updatedAt',
      backups: '++id, name, data, size, createdAt, userId',
      journalTemplates: '++id, name, description, type, entries, createdAt, userId',
      workerVouchers: '++id, voucherNumber, workerName, date, amount, paidAmount, paymentStatus, description, createdAt, userId'
    });

    this.version(3).stores({
      users: '++id, username, password, role, permissions, isActive, createdAt',
      accounts: '++id, code, name, parentId, level, type, isActive, balance, createdAt',
      customers: '++id, name, phone, email, address, taxNumber, notes, accountNumber, isActive, createdAt, updatedAt',
      suppliers: '++id, name, phone, email, address, taxNumber, notes, accountNumber, isActive, createdAt, updatedAt',
      items: '++id, code, name, unit, category, minStock, currentStock, avgCost, lastCost, isActive, createdAt',
      stockMovements: '++id, itemId, type, quantity, cost, reference, date, userId, notes',
      salesInvoices: '++id, invoiceNumber, customerId, date, dueDate, items, subtotal, discountAmount, taxAmount, total, totalCost, paidAmount, status, notes, hasTax, taxRate, discountType, discount, userId, createdAt',
      purchaseInvoices: '++id, invoiceNumber, supplierId, date, dueDate, items, subtotal, discountAmount, taxAmount, total, paidAmount, status, notes, hasTax, taxRate, discountType, discount, userId, createdAt',
      journalEntries: '++id, entryNumber, date, description, reference, type, entries, totalDebit, totalCredit, userId, createdAt',
      journalEntryDetails: '++id, entryId, accountId, debit, credit, description',
      contracts: '++id, contractNumber, customerId, name, description, startDate, endDate, totalValue, paidAmount, status, userId, createdAt',
      contractExpenses: '++id, contractId, type, voucherNumber, description, amount, date, reference, requestedBy, department, notes, status, items, userId, createdAt',
      issueVouchers: '++id, voucherNumber, contractId, date, requestedBy, department, status, notes, totalCost, userId, createdAt',
      workers: '++id, code, name, phone, address, dailyRate, hourlyRate, isActive, createdAt',
      workerPayments: '++id, workerId, contractId, date, hours, days, amount, description, voucherNumber, isPaid, userId, createdAt',
      laborCosts: '++id, contractId, workerId, date, hoursWorked, hourlyRate, totalCost, description, voucherNumber, voucherId, isPaid, userId, createdAt',
      payrolls: '++id, payrollNumber, month, year, totalAmount, userId, createdAt, isPosted',
      payrollDetails: '++id, payrollId, workerId, basicSalary, overtime, deductions, netSalary',
      settings: '++id, key, value, description, userId, updatedAt',
      backups: '++id, name, data, size, createdAt, userId',
      journalTemplates: '++id, name, description, type, entries, createdAt, userId',
      workerVouchers: '++id, voucherNumber, workerName, date, amount, paidAmount, paymentStatus, description, createdAt, userId'
    });
  }
}

// إنشاء مثيل من قاعدة البيانات
export const db = new AccountingDB();

// وظيفة لحذف قاعدة البيانات وإعادة إنشائها
export const resetDatabase = async () => {
  try {
    console.log('بدء حذف قاعدة البيانات...');
    await db.delete();
    console.log('تم حذف قاعدة البيانات');

    // إعادة فتح قاعدة البيانات
    await db.open();
    console.log('تم إعادة فتح قاعدة البيانات');

    // تهيئة البيانات الأساسية
    await initializeDatabase();
    console.log('تم إعادة تهيئة قاعدة البيانات');

    return true;
  } catch (error) {
    console.error('خطأ في إعادة تعيين قاعدة البيانات:', error);
    return false;
  }
};

// دالة لتهيئة البيانات الأساسية
export const initializeDatabase = async () => {
  try {
    console.log('بدء تهيئة قاعدة البيانات...');

    // التحقق من وجود مستخدم افتراضي
    const userCount = await db.users.count();
    console.log('عدد المستخدمين:', userCount);
    if (userCount === 0) {
      // إنشاء مستخدم افتراضي (مدير أعلى)
      await db.users.add({
        username: 'admin',
        password: 'admin123', // في التطبيق الحقيقي يجب تشفير كلمة المرور
        fullName: 'مدير النظام',
        email: '<EMAIL>',
        role: 'super-admin',
        permissions: {
          sales: true,
          purchases: true,
          inventory: true,
          contracts: true,
          labor: true,
          payroll: true,
          reports: true,
          settings: true,
          editPostedEntries: true,
          editAllData: true,

          // صلاحيات أقسام الشاشة الرئيسية
          dashboardChartOfAccounts: true,
          dashboardJournalEntries: true,
          dashboardAccountsTracker: true,
          dashboardPaymentManagement: true,
          dashboardJournalReports: true,
          dashboardInventoryManagement: true,
          dashboardSalesInvoice: true,
          dashboardPurchaseInvoice: true,
          dashboardCustomersSuppliers: true,
          dashboardFinancialReports: true,
          dashboardContractManagement: true,
          dashboardLaborManagement: true,
          dashboardPayrollSystem: true,
          dashboardGlobalSearch: true,
          dashboardSystemSettings: true,
          dashboardDatabaseCheck: true
        },
        isActive: true,
        createdAt: new Date()
      });
      console.log('تم إنشاء المستخدم الافتراضي (مدير أعلى)');
    }

    // التحقق من وجود دليل الحسابات الأساسي
    const accountCount = await db.accounts.count();
    console.log('عدد الحسابات:', accountCount);
    if (accountCount === 0) {
      await initializeChartOfAccounts();
      console.log('تم إنشاء دليل الحسابات');
    }

    // إعدادات النظام الافتراضية
    const settingsCount = await db.settings.count();
    console.log('عدد الإعدادات:', settingsCount);
    if (settingsCount === 0) {
      await initializeSettings();
      console.log('تم إنشاء الإعدادات');
    }

    // تم إزالة البيانات التجريبية - النظام يبدأ فارغاً

    // تهيئة روابط الحسابات الافتراضية
    const accountLinksCount = await db.accountLinks.count();
    console.log('عدد روابط الحسابات:', accountLinksCount);
    if (accountLinksCount === 0) {
      await initializeAccountLinks();
      console.log('تم إنشاء روابط الحسابات الافتراضية');
    } else {
      // تنظيف الروابط المكررة إذا وجدت
      await cleanupDuplicateAccountLinksInternal();
    }

    console.log('تم تهيئة قاعدة البيانات بنجاح');

    // التحقق من روابط الحسابات المطلوبة
    await checkRequiredAccountLinks();
  } catch (error) {
    console.error('خطأ في تهيئة قاعدة البيانات:', error);
  }
};

// دالة لتهيئة دليل الحسابات الأساسي
const initializeChartOfAccounts = async () => {
  const accounts = [
    // المستوى الأول - الحسابات الرئيسية
    { code: '1', name: 'الأصول', parentId: null, level: 1, type: 'أصول', isActive: true, balance: 0 },
    { code: '2', name: 'الالتزامات', parentId: null, level: 1, type: 'التزامات', isActive: true, balance: 0 },
    { code: '3', name: 'حقوق الملكية', parentId: null, level: 1, type: 'حقوق ملكية', isActive: true, balance: 0 },
    { code: '4', name: 'الإيرادات', parentId: null, level: 1, type: 'إيرادات', isActive: true, balance: 0 },
    { code: '5', name: 'المصروفات', parentId: null, level: 1, type: 'مصروفات', isActive: true, balance: 0 },
  ];

  // إضافة الحسابات الرئيسية أولاً
  const addedAccounts = [];
  for (const account of accounts) {
    const id = await db.accounts.add({
      ...account,
      createdAt: new Date()
    });
    addedAccounts.push({ ...account, id });
  }

  // إضافة حسابات المستوى الثاني
  const level2Accounts = [
    // الأصول
    { code: '101', name: 'الأصول المتداولة', parentId: addedAccounts[0].id, level: 2, type: 'أصول' },
    { code: '102', name: 'الأصول الثابتة', parentId: addedAccounts[0].id, level: 2, type: 'أصول' },

    // الالتزامات
    { code: '201', name: 'الالتزامات المتداولة', parentId: addedAccounts[1].id, level: 2, type: 'التزامات' },
    { code: '202', name: 'الالتزامات طويلة الأجل', parentId: addedAccounts[1].id, level: 2, type: 'التزامات' },

    // حقوق الملكية
    { code: '301', name: 'رأس المال', parentId: addedAccounts[2].id, level: 2, type: 'حقوق ملكية' },
    { code: '302', name: 'الأرباح المحتجزة', parentId: addedAccounts[2].id, level: 2, type: 'حقوق ملكية' },

    // الإيرادات
    { code: '401', name: 'إيرادات المبيعات', parentId: addedAccounts[3].id, level: 2, type: 'إيرادات' },
    { code: '402', name: 'إيرادات أخرى', parentId: addedAccounts[3].id, level: 2, type: 'إيرادات' },

    // المصروفات
    { code: '501', name: 'تكلفة البضاعة المباعة', parentId: addedAccounts[4].id, level: 2, type: 'مصروفات' },
    { code: '502', name: 'مصروفات تشغيلية', parentId: addedAccounts[4].id, level: 2, type: 'مصروفات' },
  ];

  const addedLevel2 = [];
  for (const account of level2Accounts) {
    const id = await db.accounts.add({
      ...account,
      isActive: true,
      balance: 0,
      createdAt: new Date()
    });
    addedLevel2.push({ ...account, id });
  }

  // إضافة حسابات المستوى الثالث (أمثلة)
  const level3Accounts = [
    // الأصول المتداولة
    { code: '10101', name: 'النقدية والبنوك', parentId: addedLevel2[0].id, level: 3, type: 'أصول' },
    { code: '10102', name: 'العملاء', parentId: addedLevel2[0].id, level: 3, type: 'أصول' },
    { code: '10103', name: 'المخزون', parentId: addedLevel2[0].id, level: 3, type: 'أصول' },

    // الأصول الثابتة
    { code: '10201', name: 'الأراضي والمباني', parentId: addedLevel2[1].id, level: 3, type: 'أصول' },
    { code: '10202', name: 'المعدات والآلات', parentId: addedLevel2[1].id, level: 3, type: 'أصول' },

    // الالتزامات المتداولة
    { code: '20101', name: 'الموردين', parentId: addedLevel2[2].id, level: 3, type: 'التزامات' },
    { code: '20102', name: 'مصروفات مستحقة', parentId: addedLevel2[2].id, level: 3, type: 'التزامات' },

    // مصروفات تشغيلية
    { code: '50201', name: 'رواتب وأجور', parentId: addedLevel2[9].id, level: 3, type: 'مصروفات' },
    { code: '50202', name: 'إيجارات', parentId: addedLevel2[9].id, level: 3, type: 'مصروفات' },
    { code: '50203', name: 'كهرباء ومياه', parentId: addedLevel2[9].id, level: 3, type: 'مصروفات' },

    // حسابات إضافية للضرائب والمبيعات
    { code: '20103', name: 'ضريبة قيمة مضافة', parentId: addedLevel2[2].id, level: 3, type: 'التزامات' },
    { code: '40101', name: 'مبيعات بضائع', parentId: addedLevel2[6].id, level: 3, type: 'إيرادات' },
  ];

  const addedLevel3 = [];
  for (const account of level3Accounts) {
    const id = await db.accounts.add({
      ...account,
      isActive: true,
      balance: 0,
      createdAt: new Date()
    });
    addedLevel3.push({ ...account, id });
  }

  // إضافة حسابات المستوى الرابع للأذون
  const level4Accounts = [
    // رواتب وأجور - تفصيل
    { code: '5020101', name: 'رواتب العمالة', parentId: addedLevel3.find(acc => acc.code === '50201')?.id, level: 4, type: 'مصروفات' },
    { code: '5020102', name: 'مكافآت العمال', parentId: addedLevel3.find(acc => acc.code === '50201')?.id, level: 4, type: 'مصروفات' },

    // مصروفات مستحقة - تفصيل
    { code: '2010201', name: 'مستحقات عمال', parentId: addedLevel3.find(acc => acc.code === '20102')?.id, level: 4, type: 'التزامات' },
    { code: '2010202', name: 'مستحقات موردين', parentId: addedLevel3.find(acc => acc.code === '20102')?.id, level: 4, type: 'التزامات' },
  ];

  // إضافة حسابات المستوى الرابع
  for (const account of level4Accounts) {
    await db.accounts.add({
      ...account,
      isActive: true,
      balance: 0,
      createdAt: new Date()
    });
  }
};

// دالة لتهيئة إعدادات النظام - فارغة (بدون بيانات افتراضية)
const initializeSettings = async () => {
  // لا توجد إعدادات افتراضية - يقوم المستخدم بإدخالها
  console.log('تم تخطي إضافة الإعدادات الافتراضية');
};

// تم إزالة دالة البيانات التجريبية للمخزون

// تم إزالة دالة البيانات التجريبية للعملاء والموردين

// تم إزالة دالة البيانات التجريبية للعقود والعمال

// دالة لتهيئة روابط الحسابات الافتراضية
const initializeAccountLinks = async () => {
  // التحقق من وجود روابط مسبقاً لتجنب المكررات
  const existingLinksCount = await db.accountLinks.count();
  if (existingLinksCount > 0) {
    console.log('روابط الحسابات موجودة بالفعل، تخطي التهيئة');
    return;
  }

  const defaultLinks = [
    {
      linkType: 'inventory',
      accountId: null,
      accountCode: '',
      accountName: '',
      description: 'حساب المخزون - يستخدم لربط جميع أصناف المخزون',
      isRequired: true
    },
    {
      linkType: 'vat',
      accountId: null,
      accountCode: '',
      accountName: '',
      description: 'حساب ضريبة القيمة المضافة - يستخدم في الفواتير',
      isRequired: true
    },
    {
      linkType: 'sales',
      accountId: null,
      accountCode: '',
      accountName: '',
      description: 'حساب المبيعات - يستخدم في فواتير المبيعات',
      isRequired: true
    },
    {
      linkType: 'purchases',
      accountId: null,
      accountCode: '',
      accountName: '',
      description: 'حساب المشتريات - يستخدم في فواتير المشتريات',
      isRequired: true
    },
    {
      linkType: 'cost_of_goods_sold',
      accountId: null,
      accountCode: '',
      accountName: '',
      description: 'حساب تكلفة البضاعة المباعة - يستخدم عند البيع',
      isRequired: true
    },
    {
      linkType: 'workers_salaries',
      accountId: null,
      accountCode: '',
      accountName: '',
      description: 'حساب العمال والمرتبات - يستخدم في كشوف المرتبات',
      isRequired: true
    },
    {
      linkType: 'accrued_liabilities',
      accountId: null,
      accountCode: '',
      accountName: '',
      description: 'حساب الالتزامات المرحلة - يستخدم في أذون الصرف',
      isRequired: true
    },
    {
      linkType: 'transport_expenses',
      accountId: null,
      accountCode: '',
      accountName: '',
      description: 'حساب مصروفات النقل - يستخدم في مصروفات النقل',
      isRequired: false
    },
    {
      linkType: 'sales_discounts',
      accountId: null,
      accountCode: '',
      accountName: '',
      description: 'حساب خصومات المبيعات - يستخدم في خصومات المبيعات',
      isRequired: false
    },
    {
      linkType: 'purchase_discounts',
      accountId: null,
      accountCode: '',
      accountName: '',
      description: 'حساب خصومات المشتريات - يستخدم في خصومات المشتريات',
      isRequired: false
    },
    {
      linkType: 'sales_returns',
      accountId: null,
      accountCode: '',
      accountName: '',
      description: 'حساب مردودات المبيعات - يستخدم في مردودات المبيعات',
      isRequired: false
    },
    {
      linkType: 'purchase_returns',
      accountId: null,
      accountCode: '',
      accountName: '',
      description: 'حساب مردودات المشتريات - يستخدم في مردودات المشتريات',
      isRequired: false
    }
  ];

  for (const link of defaultLinks) {
    await db.accountLinks.add({
      ...link,
      createdAt: new Date(),
      updatedAt: new Date()
    });
  }
};

// دالة لتنظيف الروابط المكررة (داخلية)
const cleanupDuplicateAccountLinksInternal = async () => {
  try {
    console.log('بدء تنظيف روابط الحسابات المكررة...');

    const allLinks = await db.accountLinks.toArray();
    const uniqueLinks = [];
    const seenTypes = new Set();

    // الاحتفاظ بأول ربط لكل نوع
    for (const link of allLinks) {
      if (!seenTypes.has(link.linkType)) {
        uniqueLinks.push(link);
        seenTypes.add(link.linkType);
      }
    }

    // إذا كان هناك مكررات
    if (uniqueLinks.length !== allLinks.length) {
      console.log(`تم العثور على ${allLinks.length - uniqueLinks.length} روابط مكررة`);

      // حذف جميع الروابط
      await db.accountLinks.clear();

      // إعادة إضافة الروابط الفريدة
      for (const link of uniqueLinks) {
        await db.accountLinks.add({
          ...link,
          updatedAt: new Date()
        });
      }

      console.log('تم تنظيف الروابط المكررة بنجاح');
    } else {
      console.log('لا توجد روابط مكررة');
    }

    return true;
  } catch (error) {
    console.error('خطأ في تنظيف الروابط المكررة:', error);
    return false;
  }
};

// دالة لتنظيف الروابط المكررة (للتصدير)
export const cleanupDuplicateAccountLinks = cleanupDuplicateAccountLinksInternal;

// دالة للتحقق من روابط الحسابات المطلوبة عند بدء التشغيل
const checkRequiredAccountLinks = async () => {
  try {
    const validation = await dbHelpers.validateRequiredAccountLinks();

    if (!validation.isValid && validation.missingLinks.length > 0) {
      const linkTypeNames = {
        inventory: 'حساب المخزون',
        vat: 'حساب ضريبة القيمة المضافة',
        sales: 'حساب المبيعات',
        purchases: 'حساب المشتريات',
        cost_of_goods_sold: 'حساب تكلفة البضاعة المباعة',
        workers_salaries: 'حساب العمال والمرتبات',
        accrued_liabilities: 'حساب الالتزامات المرحلة'
      };

      const missingNames = validation.missingLinks
        .map(link => linkTypeNames[link.linkType])
        .filter(name => name)
        .join('، ');

      console.warn('⚠️ تحذير: يجب ربط الحسابات التالية:', missingNames);

      // يمكن إضافة تنبيه للمستخدم هنا إذا لزم الأمر
      // لكن لا نريد إيقاف النظام تماماً
    } else {
      console.log('✅ جميع روابط الحسابات المطلوبة مكتملة');
    }
  } catch (error) {
    console.error('خطأ في التحقق من روابط الحسابات:', error);
  }
};

// دوال مساعدة للعمليات الشائعة
export const dbHelpers = {
  // الحصول على رصيد حساب
  async getAccountBalance(accountId) {
    try {
      if (!db.journalEntryDetails || !accountId) return 0;

      const allEntries = await db.journalEntryDetails.toArray();
      const accountEntries = allEntries.filter(entry => entry.accountId === accountId);

      const totalDebits = accountEntries.reduce((sum, entry) => sum + (entry.debit || 0), 0);
      const totalCredits = accountEntries.reduce((sum, entry) => sum + (entry.credit || 0), 0);

      return totalDebits - totalCredits;
    } catch (error) {
      console.warn('تعذر حساب رصيد الحساب:', error);
      return 0;
    }
  },

  // الحصول على رصيد صنف في المخزون
  async getItemStock(itemId) {
    try {
      if (!db.stockMovements || !itemId) return 0;

      const allMovements = await db.stockMovements.toArray();
      const itemMovements = allMovements.filter(movement => movement.itemId === itemId);

      return itemMovements.reduce((stock, movement) => {
        return movement.type === 'in' ? stock + movement.quantity : stock - movement.quantity;
      }, 0);
    } catch (error) {
      console.warn('تعذر حساب رصيد المخزون:', error);
      return 0;
    }
  },

  // الحصول على متوسط تكلفة صنف (LIFO)
  async getItemCost(itemId) {
    const lastMovement = await db.stockMovements
      .where('itemId')
      .equals(itemId)
      .and(movement => movement.type === 'in')
      .last();
    
    return lastMovement ? lastMovement.cost : 0;
  },

  // إنشاء رقم فاتورة جديد
  async generateInvoiceNumber(type) {
    const prefix = type === 'sales' ? 'S' : 'P';
    const count = type === 'sales' 
      ? await db.salesInvoices.count()
      : await db.purchaseInvoices.count();
    
    return `${prefix}${String(count + 1).padStart(6, '0')}`;
  },

  // إنشاء رقم قيد جديد
  async generateEntryNumber() {
    const count = await db.journalEntries.count();
    return `JE${String(count + 1).padStart(6, '0')}`;
  },

  // الحصول على الحساب المربوط حسب النوع
  async getLinkedAccount(linkType) {
    try {
      // البحث في جميع الروابط وتصفية النوع المطلوب
      const allLinks = await db.accountLinks.toArray();
      const link = allLinks.find(l => l.linkType === linkType);

      if (link && link.accountId) {
        const account = await db.accounts.get(link.accountId);
        return account;
      }
      return null;
    } catch (error) {
      console.error('خطأ في الحصول على الحساب المربوط:', error);
      return null;
    }
  },

  // التحقق من وجود جميع الحسابات المطلوبة
  async validateRequiredAccountLinks() {
    try {
      // الحصول على جميع الروابط وتصفية المطلوبة
      const allLinks = await db.accountLinks.toArray();
      const requiredLinks = allLinks.filter(link => link.isRequired === true);
      const missingLinks = [];

      for (const link of requiredLinks) {
        if (!link.accountId || !link.accountCode) {
          missingLinks.push(link);
        }
      }

      return {
        isValid: missingLinks.length === 0,
        missingLinks: missingLinks
      };
    } catch (error) {
      console.error('خطأ في التحقق من روابط الحسابات:', error);
      return { isValid: false, missingLinks: [] };
    }
  },

  // تحديث ربط حساب
  async updateAccountLink(linkType, accountId, accountCode, accountName) {
    try {
      // البحث في جميع الروابط وتصفية النوع المطلوب
      const allLinks = await db.accountLinks.toArray();
      const existingLink = allLinks.find(l => l.linkType === linkType);

      if (existingLink) {
        await db.accountLinks.update(existingLink.id, {
          accountId: accountId,
          accountCode: accountCode,
          accountName: accountName,
          updatedAt: new Date()
        });
      } else {
        await db.accountLinks.add({
          linkType: linkType,
          accountId: accountId,
          accountCode: accountCode,
          accountName: accountName,
          description: `حساب ${linkType}`,
          isRequired: true,
          createdAt: new Date(),
          updatedAt: new Date()
        });
      }

      return true;
    } catch (error) {
      console.error('خطأ في تحديث ربط الحساب:', error);
      return false;
    }
  }
};

// دالة لتوليد رقم حساب تلقائي للعملاء
export const generateCustomerAccountNumber = async () => {
  try {
    // البحث عن آخر رقم حساب للعملاء (يبدأ بـ 1211)
    const customers = await db.customers.toArray();
    const customerAccountNumbers = customers
      .filter(customer => customer.accountNumber && customer.accountNumber.startsWith('1211'))
      .map(customer => parseInt(customer.accountNumber))
      .filter(num => !isNaN(num));

    let nextNumber = 1211001; // الرقم الافتراضي الأول

    if (customerAccountNumbers.length > 0) {
      const maxNumber = Math.max(...customerAccountNumbers);
      nextNumber = maxNumber + 1;
    }

    return nextNumber.toString();
  } catch (error) {
    console.error('خطأ في توليد رقم حساب العميل:', error);
    return '1211001'; // رقم افتراضي في حالة الخطأ
  }
};

// دالة لتوليد رقم حساب تلقائي للموردين
export const generateSupplierAccountNumber = async () => {
  try {
    // البحث عن آخر رقم حساب للموردين (يبدأ بـ 2111)
    const suppliers = await db.suppliers.toArray();
    const supplierAccountNumbers = suppliers
      .filter(supplier => supplier.accountNumber && supplier.accountNumber.startsWith('2111'))
      .map(supplier => parseInt(supplier.accountNumber))
      .filter(num => !isNaN(num));

    let nextNumber = 2111001; // الرقم الافتراضي الأول

    if (supplierAccountNumbers.length > 0) {
      const maxNumber = Math.max(...supplierAccountNumbers);
      nextNumber = maxNumber + 1;
    }

    return nextNumber.toString();
  } catch (error) {
    console.error('خطأ في توليد رقم حساب المورد:', error);
    return '2111001'; // رقم افتراضي في حالة الخطأ
  }
};

// دالة إنشاء القيد المحاسبي لفاتورة المشتريات
export const createPurchaseInvoiceJournalEntry = async (invoiceData, invoiceItems) => {
  try {
    // حساب الإجماليات
    const subtotal = invoiceItems.reduce((sum, item) => sum + (item.quantity * item.cost), 0);
    const discountAmount = invoiceData.discount || 0;
    const subtotalAfterDiscount = subtotal - discountAmount;
    const vatAmount = invoiceData.hasTax ? (subtotalAfterDiscount * invoiceData.taxRate / 100) : 0;
    const total = subtotalAfterDiscount + vatAmount;

    // الحصول على بيانات المورد
    const supplier = await db.suppliers.get(invoiceData.supplierId);
    if (!supplier) {
      throw new Error('المورد غير موجود');
    }

    // التأكد من وجود رقم حساب للمورد
    if (!supplier.accountNumber) {
      // إنشاء رقم حساب تلقائي للمورد
      const suppliersCount = await db.suppliers.count();
      supplier.accountNumber = `2111${String(suppliersCount).padStart(3, '0')}`;
      await db.suppliers.update(supplier.id, { accountNumber: supplier.accountNumber });
    }

    // إنشاء القيد المحاسبي
    const entries = [];

    // 1. قيد المشتريات (مدين)
    entries.push({
      accountCode: '5111001',
      accountName: 'مشتريات بضاعة',
      description: `مشتريات من ${supplier.name} - فاتورة ${invoiceData.invoiceNumber}`,
      debit: subtotalAfterDiscount,
      credit: 0
    });

    // 2. قيد ضريبة القيمة المضافة المدفوعة (مدين) - إن وجدت
    if (vatAmount > 0) {
      entries.push({
        accountCode: '1161001',
        accountName: 'ضريبة القيمة المضافة المدفوعة',
        description: `ضريبة قيمة مضافة ${invoiceData.taxRate}% - فاتورة ${invoiceData.invoiceNumber}`,
        debit: vatAmount,
        credit: 0
      });
    }

    // 3. قيد المورد أو الصندوق (دائن)
    if (invoiceData.paymentMethod === 'cash') {
      // دفع نقدي - قيد الصندوق
      entries.push({
        accountCode: '1111001',
        accountName: 'الصندوق',
        description: `دفع نقدي لفاتورة مشتريات ${invoiceData.invoiceNumber}`,
        debit: 0,
        credit: total
      });
    } else {
      // دفع آجل - قيد المورد
      entries.push({
        accountCode: supplier.accountNumber,
        accountName: supplier.name,
        description: `مشتريات آجلة - فاتورة ${invoiceData.invoiceNumber}`,
        debit: 0,
        credit: total
      });
    }

    // إنشاء كائن القيد المحاسبي
    const journalEntry = {
      entryNumber: `JE-PUR-${invoiceData.invoiceNumber}`,
      date: new Date(invoiceData.date),
      description: `فاتورة مشتريات ${invoiceData.invoiceNumber} - ${supplier.name}`,
      reference: invoiceData.invoiceNumber,
      type: 'purchase',
      entries: entries,
      totalDebit: entries.reduce((sum, entry) => sum + entry.debit, 0),
      totalCredit: entries.reduce((sum, entry) => sum + entry.credit, 0),
      userId: invoiceData.userId,
      createdAt: new Date()
    };

    // التحقق من توازن القيد
    if (journalEntry.totalDebit !== journalEntry.totalCredit) {
      throw new Error('القيد المحاسبي غير متوازن');
    }

    return journalEntry;

  } catch (error) {
    console.error('خطأ في إنشاء القيد المحاسبي لفاتورة المشتريات:', error);
    throw error;
  }
};

// دالة لإنشاء القيد المحاسبي لفاتورة المبيعات
export const createSalesInvoiceJournalEntry = async (invoice, invoiceItems) => {
  try {
    // حساب المبالغ
    const subtotal = invoiceItems.reduce((sum, item) => sum + (item.quantity * item.price), 0);
    const vatAmount = subtotal * (invoice.vatRate / 100);
    const total = subtotal + vatAmount;
    const costOfGoods = invoiceItems.reduce((sum, item) => sum + (item.quantity * (item.cost || 0)), 0);

    // البحث عن العميل للحصول على رقم الحساب
    const customer = await db.customers.get(invoice.customerId);
    if (!customer) {
      throw new Error('لم يتم العثور على العميل');
    }

    // التأكد من وجود رقم حساب للعميل، وإنشاؤه إذا لم يكن موجوداً
    if (!customer.accountNumber) {
      // إنشاء رقم حساب تلقائي للعميل
      const customersCount = await db.customers.count();
      const newAccountNumber = `1211${String(customersCount).padStart(3, '0')}`;

      // تحديث العميل برقم الحساب الجديد
      await db.customers.update(customer.id, { accountNumber: newAccountNumber });
      customer.accountNumber = newAccountNumber;

      console.log(`تم إنشاء رقم حساب جديد للعميل ${customer.name}: ${newAccountNumber}`);
    }

    // إنشاء رقم مرجعي للقيد
    const entryNumber = await generateJournalEntryNumber();
    const reference = `SI-${invoice.invoiceNumber}`;

    // إنشاء القيد المحاسبي
    const journalEntry = {
      entryNumber,
      date: new Date(invoice.date),
      description: `فاتورة مبيعات رقم ${invoice.invoiceNumber} - ${customer.name}`,
      reference,
      type: 'sales-invoice',
      status: 'draft',
      entries: [
        // من حـ/ العميل (مدين)
        {
          accountId: null, // سيتم تحديده لاحقاً
          accountCode: customer.accountNumber,
          accountName: customer.name,
          debit: total,
          credit: 0,
          description: `فاتورة مبيعات رقم ${invoice.invoiceNumber}`
        },
        // إلى حـ/ المبيعات (دائن)
        {
          accountId: null,
          accountCode: '4101', // حساب المبيعات
          accountName: 'المبيعات',
          debit: 0,
          credit: subtotal,
          description: `مبيعات فاتورة رقم ${invoice.invoiceNumber}`
        }
      ],
      userId: invoice.userId,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // إضافة قيد ضريبة القيمة المضافة إذا كانت موجودة
    if (vatAmount > 0) {
      journalEntry.entries.push({
        accountId: null,
        accountCode: '2301', // حساب ضريبة القيمة المضافة
        accountName: 'ضريبة القيمة المضافة',
        debit: 0,
        credit: vatAmount,
        description: `ضريبة قيمة مضافة ${invoice.vatRate}% فاتورة رقم ${invoice.invoiceNumber}`
      });
    }

    // إضافة قيد تكلفة البضاعة المباعة إذا كانت موجودة
    if (costOfGoods > 0) {
      journalEntry.entries.push(
        // من حـ/ تكلفة البضاعة المباعة (مدين)
        {
          accountId: null,
          accountCode: '5101', // حساب تكلفة البضاعة المباعة
          accountName: 'تكلفة البضاعة المباعة',
          debit: costOfGoods,
          credit: 0,
          description: `تكلفة بضاعة مباعة فاتورة رقم ${invoice.invoiceNumber}`
        },
        // إلى حـ/ المخزون (دائن)
        {
          accountId: null,
          accountCode: '1301', // حساب المخزون
          accountName: 'المخزون',
          debit: 0,
          credit: costOfGoods,
          description: `خصم مخزون فاتورة رقم ${invoice.invoiceNumber}`
        }
      );
    }

    return journalEntry;
  } catch (error) {
    console.error('خطأ في إنشاء القيد المحاسبي:', error);
    throw error;
  }
};

// دالة إنشاء قيد التسوية للمدفوعات
export const createPaymentJournalEntry = async (paymentData) => {
  try {
    const {
      type, // 'payment' أو 'receipt'
      amount,
      customerId,
      supplierId,
      paymentMethod, // 'cash' أو 'bank'
      description,
      reference,
      userId
    } = paymentData;

    const entries = [];

    if (type === 'receipt') {
      // قيد تحصيل من العميل
      const customer = await db.customers.get(customerId);
      if (!customer) {
        throw new Error('العميل غير موجود');
      }

      // التأكد من وجود رقم حساب للعميل
      if (!customer.accountNumber) {
        const customersCount = await db.customers.count();
        customer.accountNumber = `1211${String(customersCount).padStart(3, '0')}`;
        await db.customers.update(customer.id, { accountNumber: customer.accountNumber });
      }

      // مدين: الصندوق أو البنك
      entries.push({
        accountCode: paymentMethod === 'cash' ? '1111001' : '1121001',
        accountName: paymentMethod === 'cash' ? 'الصندوق' : 'البنك',
        description: `تحصيل من ${customer.name} - ${description}`,
        debit: amount,
        credit: 0
      });

      // دائن: العميل
      entries.push({
        accountCode: customer.accountNumber,
        accountName: customer.name,
        description: `تحصيل - ${description}`,
        debit: 0,
        credit: amount
      });

    } else if (type === 'payment') {
      // قيد دفع للمورد
      const supplier = await db.suppliers.get(supplierId);
      if (!supplier) {
        throw new Error('المورد غير موجود');
      }

      // التأكد من وجود رقم حساب للمورد
      if (!supplier.accountNumber) {
        const suppliersCount = await db.suppliers.count();
        supplier.accountNumber = `2111${String(suppliersCount).padStart(3, '0')}`;
        await db.suppliers.update(supplier.id, { accountNumber: supplier.accountNumber });
      }

      // مدين: المورد
      entries.push({
        accountCode: supplier.accountNumber,
        accountName: supplier.name,
        description: `دفع - ${description}`,
        debit: amount,
        credit: 0
      });

      // دائن: الصندوق أو البنك
      entries.push({
        accountCode: paymentMethod === 'cash' ? '1111001' : '1121001',
        accountName: paymentMethod === 'cash' ? 'الصندوق' : 'البنك',
        description: `دفع لـ ${supplier.name} - ${description}`,
        debit: 0,
        credit: amount
      });
    }

    // إنشاء كائن القيد المحاسبي
    const journalEntry = {
      entryNumber: `JE-PAY-${Date.now()}`,
      date: new Date(),
      description: description,
      reference: reference,
      type: type,
      entries: entries,
      totalDebit: entries.reduce((sum, entry) => sum + entry.debit, 0),
      totalCredit: entries.reduce((sum, entry) => sum + entry.credit, 0),
      userId: userId,
      createdAt: new Date()
    };

    // التحقق من توازن القيد
    if (journalEntry.totalDebit !== journalEntry.totalCredit) {
      throw new Error('القيد المحاسبي غير متوازن');
    }

    return journalEntry;

  } catch (error) {
    console.error('خطأ في إنشاء قيد التسوية:', error);
    throw error;
  }
};

// دالة لتوليد رقم القيد التلقائي
const generateJournalEntryNumber = async () => {
  try {
    const entries = await db.journalEntries.toArray();
    const maxNumber = entries.reduce((max, entry) => {
      const num = parseInt(entry.entryNumber);
      return isNaN(num) ? max : Math.max(max, num);
    }, 0);

    return (maxNumber + 1).toString().padStart(6, '0');
  } catch (error) {
    console.error('خطأ في توليد رقم القيد:', error);
    return '000001';
  }
};

// تم إزالة دالة البيانات التجريبية للقيود المحاسبية
