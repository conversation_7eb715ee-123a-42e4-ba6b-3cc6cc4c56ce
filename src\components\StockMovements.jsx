import React, { useState, useEffect } from 'react';
import { db, dbHelpers } from '../database/db';
import { useAuth } from '../contexts/AuthContext';
import * as XLSX from 'xlsx';

const StockMovements = ({ onRefresh }) => {
  const { user } = useAuth();
  const [movements, setMovements] = useState([]);
  const [items, setItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [filterType, setFilterType] = useState('');
  const [filterItem, setFilterItem] = useState('');
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');

  const [formData, setFormData] = useState({
    itemId: '',
    type: 'in',
    quantity: 0,
    cost: 0,
    reference: '',
    notes: ''
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadMovements(),
        loadItems()
      ]);
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadMovements = async () => {
    try {
      // تحميل حركات المخزون بشكل آمن
      let allMovements = [];
      try {
        if (db.stockMovements) {
          allMovements = await db.stockMovements
            .orderBy('date')
            .reverse()
            .toArray();
        }
      } catch (error) {
        console.warn('تعذر تحميل حركات المخزون:', error);
        allMovements = [];
      }

      // إضافة معلومات الصنف والمستخدم لكل حركة
      const movementsWithDetails = await Promise.all(
        allMovements.map(async (movement) => {
          let item = null;
          let user = null;

          try {
            if (db.items && movement.itemId) {
              item = await db.items.get(movement.itemId);
            }
          } catch (error) {
            console.warn('تعذر تحميل بيانات الصنف:', error);
          }

          try {
            if (db.users && movement.userId) {
              user = await db.users.get(movement.userId);
            }
          } catch (error) {
            console.warn('تعذر تحميل بيانات المستخدم:', error);
          }

          return {
            ...movement,
            itemName: item?.name || 'غير معروف',
            itemCode: item?.code || '',
            userName: user?.username || 'غير معروف'
          };
        })
      );

      setMovements(movementsWithDetails);
    } catch (error) {
      console.error('خطأ في تحميل حركات المخزون:', error);
      setMovements([]);
    }
  };

  const loadItems = async () => {
    try {
      // تحميل جميع الأصناف ثم تصفيتها محلياً لتجنب مشاكل المفاتيح
      const allItems = await db.items.toArray();
      const activeItems = allItems.filter(item => item.isActive === true);
      setItems(activeItems);
    } catch (error) {
      console.error('خطأ في تحميل الأصناف:', error);
      setItems([]); // تعيين مصفوفة فارغة في حالة الخطأ
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.itemId || !formData.quantity || formData.quantity <= 0) {
      alert('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    if (formData.type === 'in' && (!formData.cost || formData.cost <= 0)) {
      alert('يجب إدخال التكلفة للحركات الواردة');
      return;
    }

    try {
      // للحركات الصادرة، التحقق من توفر الكمية
      if (formData.type === 'out') {
        const currentStock = await dbHelpers.getItemStock(formData.itemId);
        if (currentStock < formData.quantity) {
          alert(`الكمية المتاحة في المخزون: ${currentStock}`);
          return;
        }
        
        // استخدام آخر تكلفة للحركات الصادرة (LIFO)
        const lastCost = await dbHelpers.getItemCost(formData.itemId);
        formData.cost = lastCost;
      }

      // إضافة حركة المخزون
      await db.stockMovements.add({
        itemId: formData.itemId,
        type: formData.type,
        quantity: formData.quantity,
        cost: formData.cost,
        reference: formData.reference,
        date: new Date(),
        userId: user.id,
        notes: formData.notes
      });

      // تحديث رصيد الصنف
      const item = await db.items.get(formData.itemId);
      const newStock = formData.type === 'in' 
        ? item.currentStock + formData.quantity
        : item.currentStock - formData.quantity;

      await db.items.update(formData.itemId, {
        currentStock: newStock,
        lastCost: formData.type === 'in' ? formData.cost : item.lastCost
      });

      alert('تم تسجيل حركة المخزون بنجاح');
      setShowAddForm(false);
      setFormData({
        itemId: '',
        type: 'in',
        quantity: 0,
        cost: 0,
        reference: '',
        notes: ''
      });
      loadData();
      onRefresh();
    } catch (error) {
      console.error('خطأ في تسجيل حركة المخزون:', error);
      alert('حدث خطأ أثناء تسجيل حركة المخزون');
    }
  };

  const filteredMovements = movements.filter(movement => {
    const matchesType = !filterType || movement.type === filterType;
    const matchesItem = !filterItem || movement.itemId.toString() === filterItem;
    
    let matchesDate = true;
    if (dateFrom || dateTo) {
      const movementDate = new Date(movement.date);
      if (dateFrom) {
        matchesDate = matchesDate && movementDate >= new Date(dateFrom);
      }
      if (dateTo) {
        matchesDate = matchesDate && movementDate <= new Date(dateTo + 'T23:59:59');
      }
    }
    
    return matchesType && matchesItem && matchesDate;
  });

  const exportToExcel = () => {
    try {
      const exportData = filteredMovements.map(movement => ({
        'التاريخ': new Date(movement.date).toLocaleDateString('ar-EG'),
        'كود الصنف': movement.itemCode,
        'اسم الصنف': movement.itemName,
        'نوع الحركة': movement.type === 'in' ? 'وارد' : 'صادر',
        'الكمية': movement.quantity,
        'التكلفة': movement.cost,
        'إجمالي القيمة': movement.quantity * movement.cost,
        'المرجع': movement.reference,
        'المستخدم': movement.userName,
        'ملاحظات': movement.notes || ''
      }));

      const ws = XLSX.utils.json_to_sheet(exportData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'حركات المخزون');
      
      const fileName = `حركات_المخزون_${new Date().toISOString().split('T')[0]}.xlsx`;
      XLSX.writeFile(wb, fileName);
      
      alert('تم تصدير حركات المخزون بنجاح');
    } catch (error) {
      console.error('خطأ في تصدير البيانات:', error);
      alert('حدث خطأ أثناء تصدير البيانات');
    }
  };

  if (loading) {
    return (
      <div className="loading">
        <div className="spinner"></div>
      </div>
    );
  }

  return (
    <div>
      {/* أدوات التصفية */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div className="card-title">تصفية حركات المخزون</div>
        <div className="grid grid-4">
          <div className="form-group">
            <label className="form-label">نوع الحركة</label>
            <select
              className="form-control"
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
            >
              <option value="">جميع الحركات</option>
              <option value="in">وارد</option>
              <option value="out">صادر</option>
            </select>
          </div>
          
          <div className="form-group">
            <label className="form-label">الصنف</label>
            <select
              className="form-control"
              value={filterItem}
              onChange={(e) => setFilterItem(e.target.value)}
            >
              <option value="">جميع الأصناف</option>
              {items.map(item => (
                <option key={item.id} value={item.id}>
                  {item.code} - {item.name}
                </option>
              ))}
            </select>
          </div>
          
          <div className="form-group">
            <label className="form-label">من تاريخ</label>
            <input
              type="date"
              className="form-control"
              value={dateFrom}
              onChange={(e) => setDateFrom(e.target.value)}
            />
          </div>
          
          <div className="form-group">
            <label className="form-label">إلى تاريخ</label>
            <input
              type="date"
              className="form-control"
              value={dateTo}
              onChange={(e) => setDateTo(e.target.value)}
            />
          </div>
        </div>
        
        <div style={{ display: 'flex', gap: '1rem', marginTop: '1rem' }}>
          <button
            className="btn btn-primary"
            onClick={() => setShowAddForm(true)}
          >
            ➕ إضافة حركة مخزون
          </button>
          <button
            className="btn btn-success"
            onClick={exportToExcel}
          >
            📊 تصدير Excel
          </button>
          <button
            className="btn btn-secondary"
            onClick={() => {
              setFilterType('');
              setFilterItem('');
              setDateFrom('');
              setDateTo('');
            }}
          >
            🔄 مسح التصفية
          </button>
        </div>
      </div>

      {/* قائمة حركات المخزون */}
      <div className="card">
        <div className="card-title">
          حركات المخزون ({filteredMovements.length} حركة)
        </div>
        
        {filteredMovements.length > 0 ? (
          <div style={{ overflow: 'auto' }}>
            <table className="table">
              <thead>
                <tr>
                  <th>التاريخ</th>
                  <th>الصنف</th>
                  <th>نوع الحركة</th>
                  <th>الكمية</th>
                  <th>التكلفة</th>
                  <th>إجمالي القيمة</th>
                  <th>المرجع</th>
                  <th>المستخدم</th>
                </tr>
              </thead>
              <tbody>
                {filteredMovements.map(movement => (
                  <tr key={movement.id}>
                    <td>{new Date(movement.date).toLocaleDateString('ar-EG')}</td>
                    <td>
                      <div>
                        <strong>{movement.itemCode}</strong><br />
                        <small>{movement.itemName}</small>
                      </div>
                    </td>
                    <td>
                      <span style={{
                        background: movement.type === 'in' ? '#28a745' : '#dc3545',
                        color: 'white',
                        padding: '0.25rem 0.5rem',
                        borderRadius: '3px',
                        fontSize: '0.8rem'
                      }}>
                        {movement.type === 'in' ? 'وارد' : 'صادر'}
                      </span>
                    </td>
                    <td>{movement.quantity}</td>
                    <td>{movement.cost.toLocaleString('ar-EG')} ج.م</td>
                    <td>{(movement.quantity * movement.cost).toLocaleString('ar-EG')} ج.م</td>
                    <td>{movement.reference}</td>
                    <td>{movement.userName}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div style={{ textAlign: 'center', color: '#666', padding: '2rem' }}>
            لا توجد حركات مخزون تطابق معايير البحث
          </div>
        )}
      </div>

      {/* نموذج إضافة حركة مخزون */}
      {showAddForm && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            padding: '2rem',
            borderRadius: '10px',
            width: '90%',
            maxWidth: '600px',
            maxHeight: '90vh',
            overflow: 'auto'
          }}>
            <h3 style={{ marginBottom: '1.5rem' }}>إضافة حركة مخزون</h3>

            <form onSubmit={handleSubmit}>
              <div className="grid grid-2">
                <div className="form-group">
                  <label className="form-label">الصنف *</label>
                  <select
                    className="form-control"
                    value={formData.itemId}
                    onChange={(e) => setFormData({...formData, itemId: e.target.value})}
                    required
                  >
                    <option value="">اختر الصنف</option>
                    {items.map(item => (
                      <option key={item.id} value={item.id}>
                        {item.code} - {item.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label className="form-label">نوع الحركة *</label>
                  <select
                    className="form-control"
                    value={formData.type}
                    onChange={(e) => setFormData({...formData, type: e.target.value})}
                    required
                  >
                    <option value="in">وارد</option>
                    <option value="out">صادر</option>
                  </select>
                </div>

                <div className="form-group">
                  <label className="form-label">الكمية *</label>
                  <input
                    type="number"
                    className="form-control"
                    value={formData.quantity}
                    onChange={(e) => setFormData({...formData, quantity: parseFloat(e.target.value) || 0})}
                    min="0.01"
                    step="0.01"
                    required
                  />
                </div>

                {formData.type === 'in' && (
                  <div className="form-group">
                    <label className="form-label">التكلفة (للوحدة) *</label>
                    <input
                      type="number"
                      className="form-control"
                      value={formData.cost}
                      onChange={(e) => setFormData({...formData, cost: parseFloat(e.target.value) || 0})}
                      min="0.01"
                      step="0.01"
                      required
                    />
                  </div>
                )}
              </div>

              <div className="form-group">
                <label className="form-label">المرجع</label>
                <input
                  type="text"
                  className="form-control"
                  value={formData.reference}
                  onChange={(e) => setFormData({...formData, reference: e.target.value})}
                  placeholder="رقم الفاتورة أو المرجع"
                />
              </div>

              <div className="form-group">
                <label className="form-label">ملاحظات</label>
                <textarea
                  className="form-control"
                  value={formData.notes}
                  onChange={(e) => setFormData({...formData, notes: e.target.value})}
                  rows="3"
                  placeholder="ملاحظات إضافية"
                />
              </div>

              {formData.quantity > 0 && formData.cost > 0 && (
                <div style={{
                  background: '#e7f3ff',
                  border: '1px solid #b3d9ff',
                  borderRadius: '5px',
                  padding: '1rem',
                  marginTop: '1rem'
                }}>
                  <strong>إجمالي القيمة: {(formData.quantity * formData.cost).toLocaleString('ar-EG')} ج.م</strong>
                </div>
              )}

              <div style={{ display: 'flex', gap: '1rem', marginTop: '1.5rem' }}>
                <button type="submit" className="btn btn-primary">
                  حفظ الحركة
                </button>
                <button 
                  type="button" 
                  className="btn btn-secondary"
                  onClick={() => setShowAddForm(false)}
                >
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default StockMovements;
