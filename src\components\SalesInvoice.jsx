import React, { useState, useEffect } from 'react';
import { db, dbHelpers, createSalesInvoiceJournalEntry } from '../database/db';
import { useAuth } from '../contexts/AuthContext';
import AccountingEntries from './AccountingEntries';

const SalesInvoice = () => {
  const { user } = useAuth();
  const [customers, setCustomers] = useState([]);
  const [items, setItems] = useState([]);
  const [accounts, setAccounts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showEntries, setShowEntries] = useState(false);
  const [currentEntries, setCurrentEntries] = useState([]);
  const [showJournalEntry, setShowJournalEntry] = useState(false);
  const [journalEntryPreview, setJournalEntryPreview] = useState(null);

  const [invoiceData, setInvoiceData] = useState({
    invoiceNumber: '',
    customerId: '',
    date: new Date().toISOString().split('T')[0],
    dueDate: '',
    notes: '',
    hasTax: true,
    taxRate: 14,
    discount: 0,
    discountType: 'amount'
  });

  const [invoiceItems, setInvoiceItems] = useState([
    {
      itemId: '',
      quantity: 0,
      price: 0,
      cost: 0,
      total: 0
    }
  ]);

  const [totals, setTotals] = useState({
    subtotal: 0,
    discount: 0,
    taxAmount: 0,
    total: 0,
    totalCost: 0
  });

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    calculateTotals();
  }, [invoiceItems, invoiceData.discount, invoiceData.discountType, invoiceData.hasTax, invoiceData.taxRate]);

  useEffect(() => {
    if (totals.total > 0) {
      generateAccountingEntries();
    }
  }, [totals, invoiceData.customerId]);

  const loadData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadCustomers(),
        loadItems(),
        loadAccounts(),
        generateInvoiceNumber()
      ]);
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadCustomers = async () => {
    try {
      const allCustomers = await db.customers.toArray();
      console.log('العملاء المحملين:', allCustomers);
      setCustomers(allCustomers.filter(customer => customer.isActive !== false));
    } catch (error) {
      console.error('خطأ في تحميل العملاء:', error);
    }
  };

  const loadItems = async () => {
    try {
      const allItems = await db.items.toArray();
      console.log('الأصناف المحملة في فاتورة المبيعات:', allItems);

      // إضافة الرصيد الحالي والتكلفة لكل صنف
      const itemsWithStock = await Promise.all(
        allItems.filter(item => item.isActive !== false).map(async (item) => {
          const currentStock = await dbHelpers.getItemStock(item.id);
          const currentCost = await dbHelpers.getItemCost(item.id);
          return {
            ...item,
            currentStock,
            currentCost
          };
        })
      );

      console.log('الأصناف مع الأرصدة:', itemsWithStock);
      setItems(itemsWithStock);
    } catch (error) {
      console.error('خطأ في تحميل الأصناف:', error);
    }
  };

  const loadAccounts = async () => {
    try {
      const allAccounts = await db.accounts.toArray();
      setAccounts(allAccounts);
    } catch (error) {
      console.error('خطأ في تحميل الحسابات:', error);
    }
  };

  const generateInvoiceNumber = async () => {
    try {
      const lastInvoice = await db.salesInvoices.orderBy('invoiceNumber').last();
      let newNumber = 'SAL0001';
      
      if (lastInvoice) {
        const lastNumber = parseInt(lastInvoice.invoiceNumber.replace('SAL', ''));
        newNumber = `SAL${String(lastNumber + 1).padStart(4, '0')}`;
      }
      
      setInvoiceData(prev => ({ ...prev, invoiceNumber: newNumber }));
    } catch (error) {
      console.error('خطأ في توليد رقم الفاتورة:', error);
    }
  };

  const calculateTotals = async () => {
    const subtotal = invoiceItems.reduce((sum, item) => sum + item.total, 0);
    
    let discount = 0;
    if (invoiceData.discountType === 'percentage') {
      discount = (subtotal * invoiceData.discount) / 100;
    } else {
      discount = invoiceData.discount;
    }
    
    const afterDiscount = subtotal - discount;
    const taxAmount = invoiceData.hasTax ? (afterDiscount * invoiceData.taxRate) / 100 : 0;
    const total = afterDiscount + taxAmount;
    
    // حساب إجمالي التكلفة
    const totalCost = invoiceItems.reduce((sum, item) => sum + (item.cost * item.quantity), 0);
    
    setTotals({
      subtotal,
      discount,
      taxAmount,
      total,
      totalCost
    });
  };

  const generateAccountingEntries = () => {
    if (!invoiceData.customerId || totals.total === 0) {
      setCurrentEntries([]);
      return;
    }

    const customer = customers.find(c => c.id === parseInt(invoiceData.customerId));
    const customersAccount = accounts.find(acc => acc.name.includes('العملاء'));
    const salesAccount = accounts.find(acc => acc.name.includes('المبيعات'));
    const inventoryAccount = accounts.find(acc => acc.name.includes('المخزون'));
    const cogsAccount = accounts.find(acc => acc.name.includes('تكلفة البضاعة'));
    const taxAccount = accounts.find(acc => acc.name.includes('ضريبة'));

    const entries = [];

    // قيد العملاء (مدين)
    if (customersAccount) {
      entries.push({
        accountId: customersAccount.id,
        accountName: customersAccount.name,
        accountCode: customersAccount.code,
        debit: totals.total,
        credit: 0,
        description: `مبيعات آجلة للعميل ${customer?.name || 'عميل'} - فاتورة ${invoiceData.invoiceNumber}`
      });
    }

    // قيد المبيعات (دائن)
    if (salesAccount) {
      entries.push({
        accountId: salesAccount.id,
        accountName: salesAccount.name,
        accountCode: salesAccount.code,
        debit: 0,
        credit: totals.subtotal - totals.discount,
        description: `مبيعات للعميل ${customer?.name || 'عميل'} - فاتورة ${invoiceData.invoiceNumber}`
      });
    }

    // قيد الضريبة (دائن) إذا كانت موجودة
    if (invoiceData.hasTax && totals.taxAmount > 0 && taxAccount) {
      entries.push({
        accountId: taxAccount.id,
        accountName: taxAccount.name,
        accountCode: taxAccount.code,
        debit: 0,
        credit: totals.taxAmount,
        description: `ضريبة قيمة مضافة ${invoiceData.taxRate}% - فاتورة ${invoiceData.invoiceNumber}`
      });
    }

    // قيد تكلفة البضاعة المباعة (مدين)
    if (cogsAccount && totals.totalCost > 0) {
      entries.push({
        accountId: cogsAccount.id,
        accountName: cogsAccount.name,
        accountCode: cogsAccount.code,
        debit: totals.totalCost,
        credit: 0,
        description: `تكلفة البضاعة المباعة - فاتورة ${invoiceData.invoiceNumber}`
      });
    }

    // قيد المخزون (دائن)
    if (inventoryAccount && totals.totalCost > 0) {
      entries.push({
        accountId: inventoryAccount.id,
        accountName: inventoryAccount.name,
        accountCode: inventoryAccount.code,
        debit: 0,
        credit: totals.totalCost,
        description: `خروج بضاعة من المخزون - فاتورة ${invoiceData.invoiceNumber}`
      });
    }

    setCurrentEntries(entries);
  };

  const handleItemChange = async (index, field, value) => {
    const newItems = [...invoiceItems];
    newItems[index][field] = value;
    
    // إذا تم تغيير الصنف، تحديث التكلفة تلقائياً
    if (field === 'itemId' && value) {
      const selectedItem = items.find(item => item.id === parseInt(value));
      if (selectedItem) {
        newItems[index].cost = selectedItem.currentCost;
        newItems[index].price = selectedItem.currentCost * 1.3; // هامش ربح افتراضي 30%
      }
    }
    
    if (field === 'quantity' || field === 'price') {
      newItems[index].total = newItems[index].quantity * newItems[index].price;
    }
    
    setInvoiceItems(newItems);
  };

  const addItem = () => {
    setInvoiceItems([...invoiceItems, {
      itemId: '',
      quantity: 0,
      price: 0,
      cost: 0,
      total: 0
    }]);
  };

  const removeItem = (index) => {
    if (invoiceItems.length > 1) {
      const newItems = invoiceItems.filter((_, i) => i !== index);
      setInvoiceItems(newItems);
    }
  };

  const checkStock = (itemId, quantity) => {
    const item = items.find(i => i.id === parseInt(itemId));
    return item ? item.currentStock >= quantity : false;
  };

  const handleSave = async () => {
    try {
      // التحقق من روابط الحسابات المطلوبة
      const validation = await dbHelpers.validateRequiredAccountLinks();
      if (!validation.isValid) {
        const linkTypeNames = {
          inventory: 'حساب المخزون',
          vat: 'حساب ضريبة القيمة المضافة',
          sales: 'حساب المبيعات',
          cost_of_goods_sold: 'حساب تكلفة البضاعة المباعة'
        };

        const missingNames = validation.missingLinks
          .map(link => linkTypeNames[link.linkType])
          .filter(name => name)
          .join('، ');

        alert(`يجب ربط الحسابات التالية قبل إنشاء فاتورة المبيعات:\n${missingNames}\n\nيرجى الذهاب إلى دليل الحسابات > ربط الحسابات`);
        return;
      }

      // التحقق من البيانات
      if (!invoiceData.customerId) {
        alert('يرجى اختيار العميل');
        return;
      }

      if (invoiceItems.some(item => !item.itemId || item.quantity <= 0 || item.price <= 0)) {
        alert('يرجى ملء جميع بيانات الأصناف');
        return;
      }

      // التحقق من توفر المخزون
      for (const item of invoiceItems) {
        if (!checkStock(item.itemId, item.quantity)) {
          const itemData = items.find(i => i.id === parseInt(item.itemId));
          alert(`الكمية المتاحة للصنف ${itemData?.name}: ${itemData?.currentStock}`);
          return;
        }
      }

      // إنشاء القيد المحاسبي للمراجعة
      const journalEntry = await createSalesInvoiceJournalEntry(
        {
          ...invoiceData,
          customerId: parseInt(invoiceData.customerId),
          vatRate: invoiceData.hasTax ? invoiceData.taxRate : 0,
          userId: user.id
        },
        invoiceItems.map(item => ({
          ...item,
          itemId: parseInt(item.itemId)
        }))
      );

      // عرض القيد المحاسبي للمراجعة
      setJournalEntryPreview(journalEntry);
      setShowJournalEntry(true);

    } catch (error) {
      console.error('خطأ في إنشاء القيد المحاسبي:', error);
      alert('حدث خطأ أثناء إنشاء القيد المحاسبي: ' + error.message);
    }
  };

  // دالة الحفظ النهائي بعد مراجعة القيد
  const handleFinalSave = async () => {
    try {
      // حفظ الفاتورة
      const invoiceId = await db.salesInvoices.add({
        ...invoiceData,
        customerId: parseInt(invoiceData.customerId),
        items: invoiceItems.map(item => ({
          ...item,
          itemId: parseInt(item.itemId)
        })),
        subtotal: totals.subtotal,
        discountAmount: totals.discount,
        taxAmount: totals.taxAmount,
        total: totals.total,
        totalCost: totals.totalCost,
        paidAmount: 0,
        paymentStatus: 'pending',
        status: 'pending',
        userId: user.id,
        createdAt: new Date()
      });

      // تحديث المخزون
      for (const item of invoiceItems) {
        if (item.itemId && item.quantity > 0) {
          // إضافة حركة مخزون (صادر)
          await db.stockMovements.add({
            itemId: parseInt(item.itemId),
            type: 'out',
            quantity: item.quantity,
            cost: item.cost,
            reference: invoiceData.invoiceNumber,
            date: new Date(invoiceData.date),
            userId: user.id,
            notes: `مبيعات - فاتورة ${invoiceData.invoiceNumber}`
          });

          // تحديث رصيد الصنف
          const currentItem = await db.items.get(parseInt(item.itemId));
          const newStock = currentItem.currentStock - item.quantity;

          await db.items.update(parseInt(item.itemId), {
            currentStock: newStock
          });
        }
      }

      // حفظ القيد المحاسبي
      if (journalEntryPreview) {
        await db.journalEntries.add({
          ...journalEntryPreview,
          invoiceId: invoiceId,
          status: 'posted' // ترحيل القيد مباشرة
        });
      }

      alert('تم حفظ فاتورة المبيعات والقيد المحاسبي بنجاح');
      setShowJournalEntry(false);
      setJournalEntryPreview(null);
      resetForm();
    } catch (error) {
      console.error('خطأ في حفظ الفاتورة:', error);
      alert('حدث خطأ أثناء حفظ الفاتورة');
    }
  };

  const printInvoice = async () => {
    try {
      // التحقق من وجود البيانات المطلوبة
      if (!invoiceData.customerId || invoiceItems.some(item => !item.itemId)) {
        alert('يرجى ملء جميع البيانات قبل الطباعة');
        return;
      }

      // تحميل بيانات الشركة
      const companySettings = await db.settings.where('key').equals('company_info').first();
      const companyData = companySettings?.value || {};

      // بيانات العميل
      const customer = customers.find(c => c.id === parseInt(invoiceData.customerId));

      const printWindow = window.open('', '_blank');
      const printContent = `
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
          <meta charset="UTF-8">
          <title>فاتورة مبيعات - ${invoiceData.invoiceNumber}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
            .invoice-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              border-bottom: 3px solid #007bff;
              padding-bottom: 20px;
              margin-bottom: 30px;
            }
            .company-info { flex: 1; }
            .company-logo {
              max-width: 150px;
              max-height: 100px;
              margin-left: 20px;
            }
            .company-name {
              font-size: 2rem;
              font-weight: bold;
              color: #007bff;
              margin-bottom: 5px;
            }
            .company-name-en {
              font-size: 1.3rem;
              color: #666;
              margin-bottom: 10px;
            }
            .company-details {
              font-size: 0.9rem;
              color: #555;
              line-height: 1.6;
            }
            .invoice-title {
              text-align: center;
              font-size: 1.8rem;
              font-weight: bold;
              color: #333;
              margin: 30px 0;
              background: #f8f9fa;
              padding: 15px;
              border-radius: 8px;
            }
            .invoice-info {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 30px;
              margin-bottom: 30px;
            }
            .info-section {
              background: #f8f9fa;
              padding: 15px;
              border-radius: 8px;
            }
            .info-section h3 {
              margin-top: 0;
              color: #007bff;
              border-bottom: 2px solid #007bff;
              padding-bottom: 5px;
            }
            .items-table {
              width: 100%;
              border-collapse: collapse;
              margin-bottom: 30px;
            }
            .items-table th, .items-table td {
              border: 1px solid #ddd;
              padding: 12px;
              text-align: center;
            }
            .items-table th {
              background-color: #007bff;
              color: white;
              font-weight: bold;
            }
            .items-table tr:nth-child(even) {
              background-color: #f9f9f9;
            }
            .totals {
              float: left;
              width: 300px;
              background: #f8f9fa;
              padding: 20px;
              border-radius: 8px;
              border: 2px solid #007bff;
            }
            .total-row {
              display: flex;
              justify-content: space-between;
              margin-bottom: 8px;
            }
            .final-total {
              font-size: 1.3rem;
              font-weight: bold;
              color: #007bff;
              border-top: 2px solid #007bff;
              padding-top: 10px;
              margin-top: 10px;
            }
            .footer {
              clear: both;
              margin-top: 50px;
              text-align: center;
              color: #666;
              font-size: 0.9rem;
            }
            @media print {
              body { margin: 0; padding: 15px; }
              .no-print { display: none; }
            }
          </style>
        </head>
        <body>
          <!-- رأس الفاتورة -->
          <div class="invoice-header">
            <div class="company-info">
              <div class="company-name">${companyData.name || 'اسم الشركة'}</div>
              ${companyData.nameEn ? `<div class="company-name-en">${companyData.nameEn}</div>` : ''}
              <div class="company-details">
                ${companyData.address ? `<div>📍 ${companyData.address}</div>` : ''}
                ${companyData.addressEn ? `<div style="direction: ltr; text-align: right;">📍 ${companyData.addressEn}</div>` : ''}
                ${companyData.phone ? `<div>📞 ${companyData.phone}</div>` : ''}
                ${companyData.mobile ? `<div>📱 ${companyData.mobile}</div>` : ''}
                ${companyData.email ? `<div>📧 ${companyData.email}</div>` : ''}
                ${companyData.website ? `<div>🌐 ${companyData.website}</div>` : ''}
                ${companyData.taxNumber ? `<div><strong>الرقم الضريبي:</strong> ${companyData.taxNumber}</div>` : ''}
                ${companyData.commercialRegister ? `<div><strong>السجل التجاري:</strong> ${companyData.commercialRegister}</div>` : ''}
              </div>
            </div>
            ${companyData.logo ? `<img src="${companyData.logo}" alt="شعار الشركة" class="company-logo">` : ''}
          </div>

          <div class="invoice-title">فاتورة مبيعات</div>

          <!-- معلومات الفاتورة والعميل -->
          <div class="invoice-info">
            <div class="info-section">
              <h3>بيانات الفاتورة</h3>
              <div><strong>رقم الفاتورة:</strong> ${invoiceData.invoiceNumber}</div>
              <div><strong>التاريخ:</strong> ${new Date(invoiceData.date).toLocaleDateString('ar-EG')}</div>
              ${invoiceData.dueDate ? `<div><strong>تاريخ الاستحقاق:</strong> ${new Date(invoiceData.dueDate).toLocaleDateString('ar-EG')}</div>` : ''}
            </div>

            <div class="info-section">
              <h3>بيانات العميل</h3>
              <div><strong>اسم العميل:</strong> ${customer?.name || 'غير محدد'}</div>
              ${customer?.code ? `<div><strong>كود العميل:</strong> ${customer.code}</div>` : ''}
              ${customer?.phone ? `<div><strong>الهاتف:</strong> ${customer.phone}</div>` : ''}
              ${customer?.address ? `<div><strong>العنوان:</strong> ${customer.address}</div>` : ''}
            </div>
          </div>

          <!-- جدول الأصناف -->
          <table class="items-table">
            <thead>
              <tr>
                <th>م</th>
                <th>الصنف</th>
                <th>الكمية</th>
                <th>السعر</th>
                <th>الإجمالي</th>
              </tr>
            </thead>
            <tbody>
              ${invoiceItems.map((item, index) => {
                const itemData = items.find(i => i.id === parseInt(item.itemId));
                return `
                  <tr>
                    <td>${index + 1}</td>
                    <td>${itemData?.name || 'غير محدد'}</td>
                    <td>${item.quantity}</td>
                    <td>${item.price.toLocaleString('ar-EG')} ج.م</td>
                    <td>${item.total.toLocaleString('ar-EG')} ج.م</td>
                  </tr>
                `;
              }).join('')}
            </tbody>
          </table>

          <!-- الإجماليات -->
          <div class="totals">
            <div class="total-row">
              <span>المجموع الفرعي:</span>
              <span>${totals.subtotal.toLocaleString('ar-EG')} ج.م</span>
            </div>
            ${totals.discount > 0 ? `
              <div class="total-row">
                <span>الخصم:</span>
                <span>${totals.discount.toLocaleString('ar-EG')} ج.م</span>
              </div>
            ` : ''}
            ${invoiceData.hasTax && totals.taxAmount > 0 ? `
              <div class="total-row">
                <span>ضريبة القيمة المضافة (${invoiceData.taxRate}%):</span>
                <span>${totals.taxAmount.toLocaleString('ar-EG')} ج.م</span>
              </div>
            ` : ''}
            <div class="total-row final-total">
              <span>الإجمالي النهائي:</span>
              <span>${totals.total.toLocaleString('ar-EG')} ج.م</span>
            </div>
          </div>

          ${invoiceData.notes ? `
            <div style="clear: both; margin-top: 30px; background: #f8f9fa; padding: 15px; border-radius: 8px;">
              <h3 style="margin-top: 0; color: #007bff;">ملاحظات:</h3>
              <p>${invoiceData.notes}</p>
            </div>
          ` : ''}

          <div class="footer">
            <p>شكراً لتعاملكم معنا</p>
            <p>تم إنشاء هذه الفاتورة بتاريخ: ${new Date().toLocaleString('ar-EG')}</p>
          </div>
        </body>
        </html>
      `;

      printWindow.document.write(printContent);
      printWindow.document.close();
      printWindow.focus();

      setTimeout(() => {
        printWindow.print();
      }, 500);
    } catch (error) {
      console.error('خطأ في طباعة الفاتورة:', error);
      alert('حدث خطأ أثناء طباعة الفاتورة');
    }
  };

  const resetForm = () => {
    setInvoiceData({
      invoiceNumber: '',
      customerId: '',
      date: new Date().toISOString().split('T')[0],
      dueDate: '',
      notes: '',
      hasTax: true,
      taxRate: 14,
      discount: 0,
      discountType: 'amount'
    });

    setInvoiceItems([{
      itemId: '',
      quantity: 0,
      price: 0,
      cost: 0,
      total: 0
    }]);

    generateInvoiceNumber();
  };

  if (loading) {
    return (
      <div className="container">
        <div className="loading">
          <div className="spinner"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container">
      {showEntries ? (
        /* شاشة القيود المحاسبية في المقدمة */
        <div style={{ position: 'relative' }}>
          <div style={{ marginBottom: '1rem' }}>
            <button
              className="btn btn-secondary"
              onClick={() => setShowEntries(false)}
            >
              ← العودة للفاتورة
            </button>
          </div>
          <AccountingEntries
            entries={currentEntries}
            title={`القيود المحاسبية - فاتورة مبيعات ${invoiceData.invoiceNumber}`}
          />
        </div>
      ) : (
        /* فاتورة المبيعات */
        <div>
          {/* Header */}
          <div className="card" style={{ marginBottom: '2rem' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <h2 style={{ margin: 0, color: '#333' }}>فاتورة مبيعات</h2>
              <div style={{ display: 'flex', gap: '1rem' }}>
                <button
                  className="btn btn-info"
                  onClick={() => setShowEntries(true)}
                >
                  📋 عرض القيود المحاسبية
                </button>
                <button className="btn btn-primary" onClick={printInvoice}>
                  🖨️ طباعة الفاتورة
                </button>
                <button className="btn btn-success" onClick={handleSave}>
                  💾 حفظ الفاتورة
                </button>
                <button className="btn btn-secondary" onClick={resetForm}>
                  🔄 فاتورة جديدة
                </button>
              </div>
            </div>
          </div>

          {/* معلومات الفاتورة */}
          <div className="card" style={{ marginBottom: '2rem' }}>
            <div className="card-title">معلومات الفاتورة</div>
            <div className="grid grid-3">
              <div className="form-group">
                <label className="form-label">رقم الفاتورة</label>
                <input
                  type="text"
                  className="form-control"
                  value={invoiceData.invoiceNumber}
                  readOnly
                  style={{ background: '#f8f9fa' }}
                />
              </div>

              <div className="form-group">
                <label className="form-label">العميل *</label>
                <select
                  className="form-control"
                  value={invoiceData.customerId}
                  onChange={(e) => setInvoiceData({...invoiceData, customerId: e.target.value})}
                  required
                >
                  <option value="">اختر العميل</option>
                  {customers.map(customer => (
                    <option key={customer.id} value={customer.id}>
                      {customer.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label className="form-label">تاريخ الفاتورة</label>
                <input
                  type="date"
                  className="form-control"
                  value={invoiceData.date}
                  onChange={(e) => setInvoiceData({...invoiceData, date: e.target.value})}
                />
              </div>

              <div className="form-group">
                <label className="form-label">تاريخ الاستحقاق</label>
                <input
                  type="date"
                  className="form-control"
                  value={invoiceData.dueDate}
                  onChange={(e) => setInvoiceData({...invoiceData, dueDate: e.target.value})}
                />
              </div>

              <div className="form-group">
                <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                  <input
                    type="checkbox"
                    checked={invoiceData.hasTax}
                    onChange={(e) => setInvoiceData({...invoiceData, hasTax: e.target.checked})}
                  />
                  تشمل ضريبة قيمة مضافة
                </label>
              </div>

              {invoiceData.hasTax && (
                <div className="form-group">
                  <label className="form-label">معدل الضريبة (%)</label>
                  <input
                    type="number"
                    className="form-control"
                    value={invoiceData.taxRate}
                    onChange={(e) => setInvoiceData({...invoiceData, taxRate: parseFloat(e.target.value) || 0})}
                    min="0"
                    max="100"
                    step="0.1"
                  />
                </div>
              )}
            </div>

            <div className="form-group">
              <label className="form-label">ملاحظات</label>
              <textarea
                className="form-control"
                value={invoiceData.notes}
                onChange={(e) => setInvoiceData({...invoiceData, notes: e.target.value})}
                rows="2"
                placeholder="ملاحظات إضافية..."
              />
            </div>
          </div>

          {/* أصناف الفاتورة */}
          <div className="card" style={{ marginBottom: '2rem' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
              <div className="card-title" style={{ margin: 0 }}>أصناف الفاتورة</div>
              <button className="btn btn-primary" onClick={addItem}>
                ➕ إضافة صنف
              </button>
            </div>

            <div style={{ overflow: 'auto' }}>
              <table className="table">
                <thead>
                  <tr>
                    <th style={{ width: '25%' }}>الصنف</th>
                    <th style={{ width: '10%' }}>المتاح</th>
                    <th style={{ width: '15%' }}>الكمية</th>
                    <th style={{ width: '20%' }}>السعر</th>
                    <th style={{ width: '20%' }}>الإجمالي</th>
                    <th style={{ width: '10%' }}>إجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {invoiceItems.map((item, index) => {
                    const selectedItem = items.find(i => i.id === parseInt(item.itemId));
                    const hasStock = selectedItem ? selectedItem.currentStock >= item.quantity : true;
                    
                    return (
                      <tr key={index} style={{ 
                        background: !hasStock && item.quantity > 0 ? '#f8d7da' : 'transparent' 
                      }}>
                        <td>
                          <select
                            className="form-control"
                            value={item.itemId}
                            onChange={(e) => handleItemChange(index, 'itemId', e.target.value)}
                          >
                            <option value="">اختر الصنف</option>
                            {items.map(itm => (
                              <option key={itm.id} value={itm.id}>
                                {itm.code} - {itm.name}
                              </option>
                            ))}
                          </select>
                        </td>
                        <td style={{ 
                          textAlign: 'center',
                          color: selectedItem?.currentStock <= 0 ? '#dc3545' : '#28a745',
                          fontWeight: 'bold'
                        }}>
                          {selectedItem?.currentStock || 0}
                        </td>
                        <td>
                          <input
                            type="number"
                            className="form-control"
                            value={item.quantity}
                            onChange={(e) => handleItemChange(index, 'quantity', parseFloat(e.target.value) || 0)}
                            min="0"
                            step="0.01"
                            style={{ 
                              borderColor: !hasStock && item.quantity > 0 ? '#dc3545' : '#ced4da' 
                            }}
                          />
                        </td>
                        <td>
                          <input
                            type="number"
                            className="form-control"
                            value={item.price}
                            onChange={(e) => handleItemChange(index, 'price', parseFloat(e.target.value) || 0)}
                            min="0"
                            step="0.01"
                          />
                        </td>
                        <td>
                          <input
                            type="text"
                            className="form-control"
                            value={item.total.toLocaleString('ar-EG')}
                            readOnly
                            style={{ background: '#f8f9fa' }}
                          />
                        </td>
                        <td>
                          <button
                            className="btn btn-danger"
                            onClick={() => removeItem(index)}
                            disabled={invoiceItems.length === 1}
                            style={{ padding: '0.25rem 0.5rem', fontSize: '0.8rem' }}
                          >
                            حذف
                          </button>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>

          {/* الإجماليات */}
          <div className="card">
            <div className="card-title">إجماليات الفاتورة</div>
            <div className="grid grid-2">
              <div>
                <div className="form-group">
                  <label className="form-label">نوع الخصم</label>
                  <select
                    className="form-control"
                    value={invoiceData.discountType}
                    onChange={(e) => setInvoiceData({...invoiceData, discountType: e.target.value})}
                  >
                    <option value="amount">مبلغ ثابت</option>
                    <option value="percentage">نسبة مئوية</option>
                  </select>
                </div>

                <div className="form-group">
                  <label className="form-label">
                    الخصم {invoiceData.discountType === 'percentage' ? '(%)' : '(ج.م)'}
                  </label>
                  <input
                    type="number"
                    className="form-control"
                    value={invoiceData.discount}
                    onChange={(e) => setInvoiceData({...invoiceData, discount: parseFloat(e.target.value) || 0})}
                    min="0"
                    step="0.01"
                  />
                </div>
              </div>

              <div style={{ background: '#f8f9fa', padding: '1rem', borderRadius: '5px' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '0.5rem' }}>
                  <span>المجموع الفرعي:</span>
                  <strong>{totals.subtotal.toLocaleString('ar-EG')} ج.م</strong>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '0.5rem' }}>
                  <span>الخصم:</span>
                  <strong>{totals.discount.toLocaleString('ar-EG')} ج.م</strong>
                </div>
                {invoiceData.hasTax && (
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '0.5rem' }}>
                    <span>الضريبة ({invoiceData.taxRate}%):</span>
                    <strong>{totals.taxAmount.toLocaleString('ar-EG')} ج.م</strong>
                  </div>
                )}

                <hr />
                <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '1.2rem' }}>
                  <span><strong>الإجمالي:</strong></span>
                  <strong style={{ color: '#28a745' }}>{totals.total.toLocaleString('ar-EG')} ج.م</strong>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* نافذة عرض القيد المحاسبي */}
      {showJournalEntry && journalEntryPreview && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            padding: '2rem',
            borderRadius: '10px',
            width: '90%',
            maxWidth: '800px',
            maxHeight: '90vh',
            overflow: 'auto'
          }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '1.5rem',
              borderBottom: '2px solid #007bff',
              paddingBottom: '1rem'
            }}>
              <h3 style={{ margin: 0, color: '#007bff' }}>
                📋 مراجعة القيد المحاسبي
              </h3>
              <button
                className="btn btn-secondary"
                onClick={() => setShowJournalEntry(false)}
              >
                ✕ إغلاق
              </button>
            </div>

            {/* معلومات القيد */}
            <div style={{
              background: '#f8f9fa',
              padding: '1rem',
              borderRadius: '5px',
              marginBottom: '1.5rem'
            }}>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem' }}>
                <div>
                  <strong>رقم القيد:</strong> {journalEntryPreview.entryNumber}
                </div>
                <div>
                  <strong>التاريخ:</strong> {new Date(journalEntryPreview.date).toLocaleDateString('ar-EG')}
                </div>
                <div>
                  <strong>المرجع:</strong> {journalEntryPreview.reference}
                </div>
                <div>
                  <strong>النوع:</strong> فاتورة مبيعات
                </div>
              </div>
              <div style={{ marginTop: '1rem' }}>
                <strong>الوصف:</strong> {journalEntryPreview.description}
              </div>
            </div>

            {/* جدول القيد */}
            <div style={{ overflow: 'auto', marginBottom: '1.5rem' }}>
              <table className="table">
                <thead>
                  <tr style={{ background: '#007bff', color: 'white' }}>
                    <th>رقم الحساب</th>
                    <th>اسم الحساب</th>
                    <th>البيان</th>
                    <th>مدين</th>
                    <th>دائن</th>
                  </tr>
                </thead>
                <tbody>
                  {journalEntryPreview.entries.map((entry, index) => (
                    <tr key={index} style={{
                      background: index % 2 === 0 ? '#f8f9fa' : 'white'
                    }}>
                      <td style={{
                        fontFamily: 'monospace',
                        fontWeight: 'bold',
                        color: '#007bff'
                      }}>
                        {entry.accountCode}
                      </td>
                      <td style={{ fontWeight: 'bold' }}>
                        {entry.accountName}
                      </td>
                      <td>{entry.description}</td>
                      <td style={{
                        color: entry.debit > 0 ? '#dc3545' : '#6c757d',
                        fontWeight: entry.debit > 0 ? 'bold' : 'normal'
                      }}>
                        {entry.debit > 0 ? entry.debit.toLocaleString('ar-EG') : '-'}
                      </td>
                      <td style={{
                        color: entry.credit > 0 ? '#28a745' : '#6c757d',
                        fontWeight: entry.credit > 0 ? 'bold' : 'normal'
                      }}>
                        {entry.credit > 0 ? entry.credit.toLocaleString('ar-EG') : '-'}
                      </td>
                    </tr>
                  ))}
                </tbody>
                <tfoot>
                  <tr style={{ background: '#e9ecef', fontWeight: 'bold' }}>
                    <td colSpan="3" style={{ textAlign: 'center' }}>الإجمالي</td>
                    <td style={{ color: '#dc3545' }}>
                      {journalEntryPreview.entries.reduce((sum, entry) => sum + entry.debit, 0).toLocaleString('ar-EG')} ج.م
                    </td>
                    <td style={{ color: '#28a745' }}>
                      {journalEntryPreview.entries.reduce((sum, entry) => sum + entry.credit, 0).toLocaleString('ar-EG')} ج.م
                    </td>
                  </tr>
                </tfoot>
              </table>
            </div>

            {/* التحقق من توازن القيد */}
            <div style={{
              background: '#d4edda',
              border: '1px solid #c3e6cb',
              padding: '1rem',
              borderRadius: '5px',
              marginBottom: '1.5rem',
              textAlign: 'center'
            }}>
              <div style={{ color: '#155724', fontWeight: 'bold' }}>
                ✅ القيد متوازن - يمكن المتابعة
              </div>
              <div style={{ fontSize: '0.9rem', color: '#155724', marginTop: '0.5rem' }}>
                إجمالي المدين = إجمالي الدائن = {journalEntryPreview.entries.reduce((sum, entry) => sum + entry.debit, 0).toLocaleString('ar-EG')} ج.م
              </div>
            </div>

            {/* أزرار الإجراءات */}
            <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center' }}>
              <button
                className="btn btn-success"
                onClick={handleFinalSave}
                style={{ padding: '0.75rem 2rem' }}
              >
                ✅ موافق - حفظ الفاتورة والقيد
              </button>
              <button
                className="btn btn-secondary"
                onClick={() => setShowJournalEntry(false)}
                style={{ padding: '0.75rem 2rem' }}
              >
                ❌ إلغاء
              </button>
            </div>

            {/* ملاحظة مهمة */}
            <div style={{
              marginTop: '1.5rem',
              padding: '1rem',
              background: '#fff3cd',
              border: '1px solid #ffeaa7',
              borderRadius: '5px',
              fontSize: '0.9rem',
              color: '#856404'
            }}>
              <strong>ملاحظة:</strong> بعد الموافقة سيتم حفظ الفاتورة وترحيل القيد المحاسبي تلقائياً وتحديث أرصدة المخزون.
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SalesInvoice;
