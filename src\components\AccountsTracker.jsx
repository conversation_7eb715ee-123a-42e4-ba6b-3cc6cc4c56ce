import React, { useState, useEffect } from 'react';
import { db } from '../database/db';

const AccountsTracker = () => {
  const [accounts, setAccounts] = useState([]);
  const [journalEntries, setJournalEntries] = useState([]);
  const [selectedAccount, setSelectedAccount] = useState(null);
  const [accountTransactions, setAccountTransactions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [dateFilter, setDateFilter] = useState({
    from: '',
    to: ''
  });

  useEffect(() => {
    loadAccounts();
    loadJournalEntries();
  }, []);

  const loadAccounts = async () => {
    try {
      const allAccounts = await db.accounts.toArray();
      setAccounts(allAccounts.filter(acc => acc.isActive !== false));
    } catch (error) {
      console.error('خطأ في تحميل الحسابات:', error);
    }
  };

  const loadJournalEntries = async () => {
    try {
      const allEntries = await db.journalEntries.toArray();
      setJournalEntries(allEntries);
    } catch (error) {
      console.error('خطأ في تحميل القيود:', error);
    }
  };

  const calculateAccountBalance = (accountId) => {
    let debitTotal = 0;
    let creditTotal = 0;

    journalEntries.forEach(entry => {
      entry.entries?.forEach(detail => {
        if (detail.accountId === accountId) {
          debitTotal += parseFloat(detail.debit || 0);
          creditTotal += parseFloat(detail.credit || 0);
        }
      });
    });

    return {
      debit: debitTotal,
      credit: creditTotal,
      balance: debitTotal - creditTotal
    };
  };

  const getAccountTransactions = (accountId) => {
    const transactions = [];

    journalEntries.forEach(entry => {
      entry.entries?.forEach(detail => {
        if (detail.accountId === accountId) {
          transactions.push({
            id: `${entry.id}-${detail.accountId}`,
            entryNumber: entry.entryNumber,
            date: entry.date,
            description: detail.description || entry.description,
            reference: entry.reference,
            debit: parseFloat(detail.debit || 0),
            credit: parseFloat(detail.credit || 0),
            entryType: entry.type
          });
        }
      });
    });

    // ترتيب حسب التاريخ
    return transactions.sort((a, b) => new Date(a.date) - new Date(b.date));
  };

  const handleAccountSelect = (account) => {
    setSelectedAccount(account);
    const transactions = getAccountTransactions(account.id);
    setAccountTransactions(transactions);
  };

  const getFilteredAccounts = () => {
    return accounts.filter(account => 
      account.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      account.code.includes(searchTerm)
    );
  };

  const getFilteredTransactions = () => {
    let filtered = accountTransactions;

    if (dateFilter.from) {
      filtered = filtered.filter(trans => new Date(trans.date) >= new Date(dateFilter.from));
    }

    if (dateFilter.to) {
      filtered = filtered.filter(trans => new Date(trans.date) <= new Date(dateFilter.to));
    }

    return filtered;
  };

  const exportToExcel = () => {
    if (!selectedAccount) return;

    const transactions = getFilteredTransactions();
    const balance = calculateAccountBalance(selectedAccount.id);

    let csvContent = "data:text/csv;charset=utf-8,";
    csvContent += `كشف حساب: ${selectedAccount.name} (${selectedAccount.code})\n`;
    csvContent += `الرصيد الإجمالي: ${balance.balance.toLocaleString('ar-EG')} ج.م\n\n`;
    csvContent += "رقم القيد,التاريخ,البيان,المرجع,مدين,دائن,الرصيد\n";

    let runningBalance = 0;
    transactions.forEach(trans => {
      runningBalance += trans.debit - trans.credit;
      csvContent += `${trans.entryNumber},${new Date(trans.date).toLocaleDateString('ar-EG')},${trans.description},${trans.reference || ''},${trans.debit},${trans.credit},${runningBalance}\n`;
    });

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", `كشف_حساب_${selectedAccount.name}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div style={{ padding: '2rem' }}>
      <div className="card-title" style={{ marginBottom: '2rem' }}>
        📊 متابعة الحسابات والقيود
      </div>

      <div style={{ display: 'grid', gridTemplateColumns: '1fr 2fr', gap: '2rem' }}>
        {/* قائمة الحسابات */}
        <div className="card">
          <div className="card-title">الحسابات ({getFilteredAccounts().length})</div>
          
          <div className="form-group" style={{ marginBottom: '1rem' }}>
            <input
              type="text"
              className="form-control"
              placeholder="البحث في الحسابات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <div style={{ maxHeight: '500px', overflow: 'auto' }}>
            {getFilteredAccounts().map(account => {
              const balance = calculateAccountBalance(account.id);
              return (
                <div
                  key={account.id}
                  className={`account-item ${selectedAccount?.id === account.id ? 'selected' : ''}`}
                  onClick={() => handleAccountSelect(account)}
                  style={{
                    padding: '1rem',
                    border: '1px solid #ddd',
                    borderRadius: '5px',
                    marginBottom: '0.5rem',
                    cursor: 'pointer',
                    backgroundColor: selectedAccount?.id === account.id ? '#e3f2fd' : 'white'
                  }}
                >
                  <div style={{ fontWeight: 'bold' }}>{account.code} - {account.name}</div>
                  <div style={{ fontSize: '0.9rem', color: '#666' }}>
                    النوع: {account.type} | المستوى: {account.level}
                  </div>
                  <div style={{ 
                    fontSize: '0.9rem', 
                    fontWeight: 'bold',
                    color: balance.balance >= 0 ? '#28a745' : '#dc3545'
                  }}>
                    الرصيد: {balance.balance.toLocaleString('ar-EG')} ج.م
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* تفاصيل الحساب المختار */}
        <div className="card">
          {selectedAccount ? (
            <>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
                <div className="card-title">
                  كشف حساب: {selectedAccount.name} ({selectedAccount.code})
                </div>
                <button className="btn btn-success" onClick={exportToExcel}>
                  📊 تصدير Excel
                </button>
              </div>

              {/* ملخص الحساب */}
              <div style={{ 
                display: 'grid', 
                gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', 
                gap: '1rem',
                marginBottom: '2rem',
                padding: '1rem',
                backgroundColor: '#f8f9fa',
                borderRadius: '5px'
              }}>
                {(() => {
                  const balance = calculateAccountBalance(selectedAccount.id);
                  return (
                    <>
                      <div style={{ textAlign: 'center' }}>
                        <div style={{ fontSize: '0.9rem', color: '#666' }}>إجمالي المدين</div>
                        <div style={{ fontSize: '1.2rem', fontWeight: 'bold', color: '#dc3545' }}>
                          {balance.debit.toLocaleString('ar-EG')}
                        </div>
                      </div>
                      <div style={{ textAlign: 'center' }}>
                        <div style={{ fontSize: '0.9rem', color: '#666' }}>إجمالي الدائن</div>
                        <div style={{ fontSize: '1.2rem', fontWeight: 'bold', color: '#28a745' }}>
                          {balance.credit.toLocaleString('ar-EG')}
                        </div>
                      </div>
                      <div style={{ textAlign: 'center' }}>
                        <div style={{ fontSize: '0.9rem', color: '#666' }}>الرصيد النهائي</div>
                        <div style={{ 
                          fontSize: '1.2rem', 
                          fontWeight: 'bold',
                          color: balance.balance >= 0 ? '#28a745' : '#dc3545'
                        }}>
                          {balance.balance.toLocaleString('ar-EG')}
                        </div>
                      </div>
                    </>
                  );
                })()}
              </div>

              {/* فلاتر التاريخ */}
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem', marginBottom: '1rem' }}>
                <div className="form-group">
                  <label className="form-label">من تاريخ</label>
                  <input
                    type="date"
                    className="form-control"
                    value={dateFilter.from}
                    onChange={(e) => setDateFilter({...dateFilter, from: e.target.value})}
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">إلى تاريخ</label>
                  <input
                    type="date"
                    className="form-control"
                    value={dateFilter.to}
                    onChange={(e) => setDateFilter({...dateFilter, to: e.target.value})}
                  />
                </div>
              </div>

              {/* جدول المعاملات */}
              <div style={{ overflow: 'auto', maxHeight: '400px' }}>
                <table className="table">
                  <thead>
                    <tr>
                      <th>رقم القيد</th>
                      <th>التاريخ</th>
                      <th>البيان</th>
                      <th>المرجع</th>
                      <th>مدين</th>
                      <th>دائن</th>
                      <th>الرصيد الجاري</th>
                    </tr>
                  </thead>
                  <tbody>
                    {(() => {
                      let runningBalance = 0;
                      return getFilteredTransactions().map(trans => {
                        runningBalance += trans.debit - trans.credit;
                        return (
                          <tr key={trans.id}>
                            <td>{trans.entryNumber}</td>
                            <td>{new Date(trans.date).toLocaleDateString('ar-EG')}</td>
                            <td>{trans.description}</td>
                            <td>{trans.reference || '-'}</td>
                            <td style={{ color: '#dc3545', fontWeight: 'bold' }}>
                              {trans.debit > 0 ? trans.debit.toLocaleString('ar-EG') : '-'}
                            </td>
                            <td style={{ color: '#28a745', fontWeight: 'bold' }}>
                              {trans.credit > 0 ? trans.credit.toLocaleString('ar-EG') : '-'}
                            </td>
                            <td style={{ 
                              fontWeight: 'bold',
                              color: runningBalance >= 0 ? '#28a745' : '#dc3545'
                            }}>
                              {runningBalance.toLocaleString('ar-EG')}
                            </td>
                          </tr>
                        );
                      });
                    })()}
                  </tbody>
                </table>
              </div>
            </>
          ) : (
            <div style={{ textAlign: 'center', padding: '3rem', color: '#6c757d' }}>
              <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📊</div>
              <div style={{ fontSize: '1.2rem', marginBottom: '0.5rem' }}>اختر حساباً لعرض تفاصيله</div>
              <div>انقر على أي حساب من القائمة لعرض كشف الحساب والمعاملات</div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AccountsTracker;
