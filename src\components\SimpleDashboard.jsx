import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { db, resetDatabase } from '../database/db';
import InventoryManagement from './InventoryManagement';
import CustomersSuppliers from './CustomersSuppliers';
import PurchaseInvoice from './PurchaseInvoice';
import SalesInvoice from './SalesInvoice';
import FinancialReports from './FinancialReports';
import ContractManagement from './ContractManagement';
import LaborManagement from './LaborManagement';
import PayrollSystem from './PayrollSystem';
import SystemSettings from './SystemSettings';
import GlobalSearch from './GlobalSearch';
import ChartOfAccounts from './ChartOfAccounts';
import JournalEntries from './JournalEntries';
import AccountsTracker from './AccountsTracker';
import PaymentManagement from './PaymentManagement';
import JournalReports from './JournalReports';

const SimpleDashboard = () => {
  const { user, logout, hasPermission } = useAuth();
  const [currentView, setCurrentView] = useState('dashboard');
  const [stats, setStats] = useState({
    totalCustomers: 0,
    totalSuppliers: 0,
    totalItems: 0,
    totalSalesInvoices: 0,
    totalPurchaseInvoices: 0,
    totalContracts: 0
  });

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      const [
        totalCustomers,
        totalSuppliers,
        totalItems,
        totalSalesInvoices,
        totalPurchaseInvoices,
        totalContracts
      ] = await Promise.all([
        db.customers.count(),
        db.suppliers.count(),
        db.items.count(),
        db.salesInvoices.count(),
        db.purchaseInvoices.count(),
        db.contracts.count()
      ]);

      setStats({
        totalCustomers,
        totalSuppliers,
        totalItems,
        totalSalesInvoices,
        totalPurchaseInvoices,
        totalContracts
      });
    } catch (error) {
      console.error('خطأ في تحميل الإحصائيات:', error);
    }
  };

  const StatCard = ({ title, value, color, icon }) => (
    <div className="card" style={{ borderTop: `4px solid ${color}` }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <h3 style={{ color, fontSize: '2rem', fontWeight: 'bold', margin: 0 }}>
            {value}
          </h3>
          <p style={{ color: '#666', margin: '0.5rem 0 0 0' }}>{title}</p>
        </div>
        <div style={{ fontSize: '2rem', color, opacity: 0.7 }}>
          {icon}
        </div>
      </div>
    </div>
  );

  const handleResetDatabase = async () => {
    if (confirm('هل أنت متأكد من إعادة تعيين قاعدة البيانات؟ سيتم حذف جميع البيانات!')) {
      const success = await resetDatabase();
      if (success) {
        alert('تم إعادة تعيين قاعدة البيانات بنجاح');
        window.location.reload();
      } else {
        alert('حدث خطأ أثناء إعادة تعيين قاعدة البيانات');
      }
    }
  };

  const checkDatabaseStatus = async () => {
    try {
      const itemsCount = await db.items.count();
      const customersCount = await db.customers.count();
      const suppliersCount = await db.suppliers.count();
      const accountsCount = await db.accounts.count();

      const status = `
حالة قاعدة البيانات:
- الأصناف: ${itemsCount}
- العملاء: ${customersCount}
- الموردين: ${suppliersCount}
- الحسابات: ${accountsCount}
      `;

      alert(status);
      console.log('حالة قاعدة البيانات:', {
        items: itemsCount,
        customers: customersCount,
        suppliers: suppliersCount,
        accounts: accountsCount
      });
    } catch (error) {
      console.error('خطأ في فحص قاعدة البيانات:', error);
      alert('حدث خطأ أثناء فحص قاعدة البيانات');
    }
  };

  const renderContent = () => {
    switch (currentView) {
      case 'inventory':
        return <InventoryManagement />;
      case 'customers-suppliers':
        return <CustomersSuppliers />;
      case 'purchase-invoice':
        return <PurchaseInvoice />;
      case 'sales-invoice':
        return <SalesInvoice />;
      case 'financial-reports':
        return <FinancialReports />;
      case 'contract-management':
        return <ContractManagement />;
      case 'labor-management':
        return <LaborManagement />;
      case 'payroll-system':
        return <PayrollSystem />;
      case 'settings':
        return <SystemSettings user={user} />;
      case 'search':
        return <GlobalSearch />;
      case 'chart-of-accounts':
        return <ChartOfAccounts />;
      case 'journal-entries':
        return <JournalEntries />;
      case 'accounts-tracker':
        return <AccountsTracker />;
      case 'payment-management':
        return <PaymentManagement />;
      case 'journal-reports':
        return <JournalReports />;
      default:
        return renderDashboard();
    }
  };

  const renderDashboard = () => (
    <div className="container">
      {/* الإحصائيات */}
      <div style={{ marginBottom: '2rem' }}>
        <h2 style={{ marginBottom: '1rem', color: '#333' }}>لوحة المعلومات</h2>
        <div className="grid grid-3">
          <StatCard
            title="العملاء"
            value={stats.totalCustomers}
            color="#007bff"
            icon="👥"
          />
          <StatCard
            title="الموردين"
            value={stats.totalSuppliers}
            color="#28a745"
            icon="🏢"
          />
          <StatCard
            title="الأصناف"
            value={stats.totalItems}
            color="#ffc107"
            icon="📦"
          />
          <StatCard
            title="فواتير المبيعات"
            value={stats.totalSalesInvoices}
            color="#17a2b8"
            icon="📄"
          />
          <StatCard
            title="فواتير المشتريات"
            value={stats.totalPurchaseInvoices}
            color="#6f42c1"
            icon="📋"
          />
          <StatCard
            title="العقود"
            value={stats.totalContracts}
            color="#fd7e14"
            icon="📝"
          />
        </div>
      </div>

      {/* الوصول السريع */}
      <div className="card">
        <div className="card-title">الوصول السريع</div>
        {(() => {
          const availableSections = [
            hasPermission('dashboardChartOfAccounts'),
            hasPermission('dashboardJournalEntries'),
            hasPermission('dashboardAccountsTracker'),
            hasPermission('dashboardPaymentManagement'),
            hasPermission('dashboardJournalReports'),
            hasPermission('dashboardInventoryManagement'),
            hasPermission('dashboardSalesInvoice'),
            hasPermission('dashboardPurchaseInvoice'),
            hasPermission('dashboardCustomersSuppliers'),
            hasPermission('dashboardFinancialReports'),
            hasPermission('dashboardContractManagement'),
            hasPermission('dashboardDatabaseCheck'),
            hasPermission('dashboardLaborManagement'),
            hasPermission('dashboardPayrollSystem'),
            hasPermission('dashboardGlobalSearch'),
            hasPermission('dashboardSystemSettings')
          ].filter(Boolean).length;

          if (availableSections === 0) {
            return (
              <div style={{
                textAlign: 'center',
                padding: '3rem',
                color: '#6c757d',
                background: '#f8f9fa',
                borderRadius: '8px',
                border: '2px dashed #dee2e6'
              }}>
                <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>🔒</div>
                <div style={{ fontSize: '1.2rem', marginBottom: '0.5rem' }}>
                  لا توجد أقسام متاحة
                </div>
                <div>
                  يرجى التواصل مع المدير لتفعيل الصلاحيات المطلوبة
                </div>
              </div>
            );
          }

          return (
            <div className="grid grid-4">
          {hasPermission('dashboardChartOfAccounts') && (
            <button
              className="btn btn-primary"
              style={{ padding: '1rem', height: 'auto' }}
              onClick={() => setCurrentView('chart-of-accounts')}
            >
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🌳</div>
                <div>دليل الحسابات</div>
              </div>
            </button>
          )}

          {hasPermission('dashboardJournalEntries') && (
            <button
              className="btn"
              style={{ padding: '1rem', height: 'auto', background: '#dc3545', color: 'white' }}
              onClick={() => setCurrentView('journal-entries')}
            >
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>📝</div>
                <div>القيود اليومية</div>
              </div>
            </button>
          )}

          {hasPermission('dashboardAccountsTracker') && (
            <button
              className="btn"
              style={{ padding: '1rem', height: 'auto', background: '#17a2b8', color: 'white' }}
              onClick={() => setCurrentView('accounts-tracker')}
            >
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>📊</div>
                <div>متابعة الحسابات</div>
              </div>
            </button>
          )}

          {hasPermission('dashboardPaymentManagement') && (
            <button
              className="btn"
              style={{ padding: '1rem', height: 'auto', background: '#28a745', color: 'white' }}
              onClick={() => setCurrentView('payment-management')}
            >
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>💳</div>
                <div>إدارة المدفوعات</div>
              </div>
            </button>
          )}

          {hasPermission('dashboardJournalReports') && (
            <button
              className="btn"
              style={{ padding: '1rem', height: 'auto', background: '#007bff', color: 'white' }}
              onClick={() => setCurrentView('journal-reports')}
            >
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>📈</div>
                <div>تقارير القيود</div>
              </div>
            </button>
          )}

          {hasPermission('dashboardInventoryManagement') && (
            <button
              className="btn btn-success"
              style={{ padding: '1rem', height: 'auto' }}
              onClick={() => setCurrentView('inventory')}
            >
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>📦</div>
                <div>إدارة المخزون</div>
              </div>
            </button>
          )}

          {hasPermission('dashboardSalesInvoice') && (
            <button
              className="btn btn-info"
              style={{ padding: '1rem', height: 'auto', background: '#17a2b8', color: 'white' }}
              onClick={() => setCurrentView('sales-invoice')}
            >
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>💰</div>
                <div>فاتورة مبيعات</div>
              </div>
            </button>
          )}

          {hasPermission('dashboardPurchaseInvoice') && (
            <button
              className="btn btn-warning"
              style={{ padding: '1rem', height: 'auto', background: '#ffc107', color: '#212529' }}
              onClick={() => setCurrentView('purchase-invoice')}
            >
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🛒</div>
                <div>فاتورة مشتريات</div>
              </div>
            </button>
          )}

          {hasPermission('dashboardCustomersSuppliers') && (
            <button
              className="btn"
              style={{ padding: '1rem', height: 'auto', background: '#20c997', color: 'white' }}
              onClick={() => setCurrentView('customers-suppliers')}
            >
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>👥</div>
                <div>العملاء والموردين</div>
              </div>
            </button>
          )}

          {hasPermission('dashboardFinancialReports') && (
            <button
              className="btn"
              style={{ padding: '1rem', height: 'auto', background: '#6f42c1', color: 'white' }}
              onClick={() => setCurrentView('financial-reports')}
            >
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>📊</div>
                <div>التقارير المالية</div>
              </div>
            </button>
          )}

          {hasPermission('dashboardContractManagement') && (
            <button
              className="btn"
              style={{ padding: '1rem', height: 'auto', background: '#17a2b8', color: 'white' }}
              onClick={() => setCurrentView('contract-management')}
            >
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>📋</div>
                <div>إدارة العقود</div>
              </div>
            </button>
          )}

          {hasPermission('dashboardDatabaseCheck') && (
            <button
              className="btn"
              style={{ padding: '1rem', height: 'auto', background: '#17a2b8', color: 'white' }}
              onClick={checkDatabaseStatus}
            >
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🔍</div>
                <div>فحص قاعدة البيانات</div>
              </div>
            </button>
          )}

          {hasPermission('dashboardLaborManagement') && (
            <button
              className="btn"
              style={{ padding: '1rem', height: 'auto', background: '#fd7e14', color: 'white' }}
              onClick={() => setCurrentView('labor-management')}
            >
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>👷</div>
                <div>إدارة العمالة</div>
              </div>
            </button>
          )}

          {hasPermission('dashboardPayrollSystem') && (
            <button
              className="btn"
              style={{ padding: '1rem', height: 'auto', background: '#20c997', color: 'white' }}
              onClick={() => setCurrentView('payroll-system')}
            >
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>💰</div>
                <div>نظام الرواتب</div>
              </div>
            </button>
          )}

          {hasPermission('dashboardGlobalSearch') && (
            <button
              className="btn"
              style={{ padding: '1rem', height: 'auto', background: '#17a2b8', color: 'white' }}
              onClick={() => setCurrentView('search')}
            >
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🔍</div>
                <div>البحث الشامل</div>
              </div>
            </button>
          )}

          {hasPermission('dashboardSystemSettings') && (
            <button
              className="btn"
              style={{ padding: '1rem', height: 'auto', background: '#6c757d', color: 'white' }}
              onClick={() => setCurrentView('settings')}
            >
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>⚙️</div>
                <div>إعدادات النظام</div>
              </div>
            </button>
          )}
            </div>
          );
        })()}
      </div>

      {/* آخر العمليات */}
      <div className="grid grid-2">
        <div className="card">
          <div className="card-title">آخر فواتير المبيعات</div>
          <div style={{ color: '#666', textAlign: 'center', padding: '2rem' }}>
            لا توجد فواتير حتى الآن
          </div>
        </div>

        <div className="card">
          <div className="card-title">آخر فواتير المشتريات</div>
          <div style={{ color: '#666', textAlign: 'center', padding: '2rem' }}>
            لا توجد فواتير حتى الآن
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div>
      {/* Header */}
      <div className="header">
        <div className="container">
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
              <h1>نظام المحاسبة</h1>
              {currentView !== 'dashboard' && (
                <button
                  className="btn btn-secondary"
                  onClick={() => setCurrentView('dashboard')}
                  style={{ padding: '0.5rem 1rem' }}
                >
                  العودة للرئيسية
                </button>
              )}
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
              <span>مرحباً، {user.username} ({user.role})</span>
              <button
                className="btn btn-secondary"
                onClick={logout}
                style={{ padding: '0.5rem 1rem' }}
              >
                تسجيل الخروج
              </button>
            </div>
          </div>
        </div>
      </div>

      {renderContent()}
    </div>
  );
};

export default SimpleDashboard;
