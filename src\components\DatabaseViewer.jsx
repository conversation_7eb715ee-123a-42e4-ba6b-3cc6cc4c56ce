import React, { useState, useEffect } from 'react';
import { db } from '../database/db';

const DatabaseViewer = () => {
  const [loading, setLoading] = useState(false);
  const [selectedTable, setSelectedTable] = useState('customers');
  const [tableData, setTableData] = useState([]);
  const [tableStructure, setTableStructure] = useState([]);
  const [editingRecord, setEditingRecord] = useState(null);
  const [currentView, setCurrentView] = useState('data');

  const tables = {
    customers: 'العملاء',
    suppliers: 'الموردين',
    items: 'الأصناف',
    accounts: 'الحسابات',
    contracts: 'العقود',
    workers: 'العمال',
    salesInvoices: 'فواتير البيع',
    purchaseInvoices: 'فواتير الشراء',
    journalEntries: 'القيود المحاسبية',
    stockMovements: 'حركات المخزون',
    contractExpenses: 'مصروفات العقود',
    laborCosts: 'تكاليف العمالة',
    payrolls: 'الرواتب',
    workerPayments: 'مدفوعات العمال',
    settings: 'الإعدادات',
    users: 'المستخدمين',
    backups: 'النسخ الاحتياطية'
  };

  const tableRelations = {
    salesInvoices: [
      { field: 'customerId', relatedTable: 'customers', relatedField: 'id', description: 'العميل' },
      { field: 'items.itemId', relatedTable: 'items', relatedField: 'id', description: 'الأصناف' }
    ],
    purchaseInvoices: [
      { field: 'supplierId', relatedTable: 'suppliers', relatedField: 'id', description: 'المورد' },
      { field: 'items.itemId', relatedTable: 'items', relatedField: 'id', description: 'الأصناف' }
    ],
    contracts: [
      { field: 'customerId', relatedTable: 'customers', relatedField: 'id', description: 'العميل' }
    ],
    contractExpenses: [
      { field: 'contractId', relatedTable: 'contracts', relatedField: 'id', description: 'العقد' },
      { field: 'items.itemId', relatedTable: 'items', relatedField: 'id', description: 'الأصناف (للمواد)' },
      { field: 'items.workerId', relatedTable: 'workers', relatedField: 'id', description: 'العامل (للعمالة)' }
    ],
    laborCosts: [
      { field: 'workerId', relatedTable: 'workers', relatedField: 'id', description: 'العامل' },
      { field: 'contractId', relatedTable: 'contracts', relatedField: 'id', description: 'العقد' }
    ],
    stockMovements: [
      { field: 'itemId', relatedTable: 'items', relatedField: 'id', description: 'الصنف' }
    ],
    workerPayments: [
      { field: 'workerId', relatedTable: 'workers', relatedField: 'id', description: 'العامل' },
      { field: 'contractId', relatedTable: 'contracts', relatedField: 'id', description: 'العقد' }
    ]
  };

  useEffect(() => {
    loadTableData();
  }, [selectedTable]);

  const loadTableData = async () => {
    try {
      setLoading(true);
      const data = await db[selectedTable].toArray();
      setTableData(data);
      
      // استخراج هيكل الجدول
      if (data.length > 0) {
        const structure = Object.keys(data[0]).map(key => ({
          field: key,
          type: typeof data[0][key],
          sample: data[0][key]
        }));
        setTableStructure(structure);
      } else {
        setTableStructure([]);
      }
    } catch (error) {
      console.error('خطأ في تحميل بيانات الجدول:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (record) => {
    setEditingRecord({ ...record });
  };

  const handleSave = async () => {
    try {
      await db[selectedTable].update(editingRecord.id, editingRecord);
      setEditingRecord(null);
      loadTableData();
      alert('تم تحديث السجل بنجاح');
    } catch (error) {
      console.error('خطأ في تحديث السجل:', error);
      alert('حدث خطأ أثناء تحديث السجل');
    }
  };

  const handleDelete = async (id) => {
    if (!confirm('هل أنت متأكد من حذف هذا السجل؟')) return;
    
    try {
      await db[selectedTable].delete(id);
      loadTableData();
      alert('تم حذف السجل بنجاح');
    } catch (error) {
      console.error('خطأ في حذف السجل:', error);
      alert('حدث خطأ أثناء حذف السجل');
    }
  };

  const renderValue = (value) => {
    if (value === null || value === undefined) return '-';
    if (typeof value === 'object') {
      if (value instanceof Date) {
        return value.toLocaleString('ar-EG');
      }
      return JSON.stringify(value, null, 2);
    }
    if (typeof value === 'boolean') {
      return value ? 'نعم' : 'لا';
    }
    return value.toString();
  };

  const renderRelations = () => {
    const relations = tableRelations[selectedTable] || [];
    
    return (
      <div className="card">
        <div className="card-title">🔗 العلاقات - {tables[selectedTable]}</div>
        
        {relations.length > 0 ? (
          <div style={{ overflow: 'auto' }}>
            <table className="table">
              <thead>
                <tr>
                  <th>الحقل</th>
                  <th>الجدول المرتبط</th>
                  <th>الحقل المرتبط</th>
                  <th>الوصف</th>
                </tr>
              </thead>
              <tbody>
                {relations.map((relation, index) => (
                  <tr key={index}>
                    <td><code>{relation.field}</code></td>
                    <td>{tables[relation.relatedTable]}</td>
                    <td><code>{relation.relatedField}</code></td>
                    <td>{relation.description}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div style={{ textAlign: 'center', color: '#666', padding: '2rem' }}>
            لا توجد علاقات محددة لهذا الجدول
          </div>
        )}
      </div>
    );
  };

  const renderStructure = () => {
    return (
      <div className="card">
        <div className="card-title">🏗️ هيكل الجدول - {tables[selectedTable]}</div>
        
        {tableStructure.length > 0 ? (
          <div style={{ overflow: 'auto' }}>
            <table className="table">
              <thead>
                <tr>
                  <th>اسم الحقل</th>
                  <th>النوع</th>
                  <th>مثال</th>
                </tr>
              </thead>
              <tbody>
                {tableStructure.map((field, index) => (
                  <tr key={index}>
                    <td><code>{field.field}</code></td>
                    <td>
                      <span style={{
                        padding: '0.25rem 0.5rem',
                        borderRadius: '4px',
                        fontSize: '0.8rem',
                        background: field.type === 'string' ? '#d4edda' : 
                                   field.type === 'number' ? '#d1ecf1' :
                                   field.type === 'boolean' ? '#fff3cd' : '#f8d7da',
                        color: field.type === 'string' ? '#155724' : 
                              field.type === 'number' ? '#0c5460' :
                              field.type === 'boolean' ? '#856404' : '#721c24'
                      }}>
                        {field.type}
                      </span>
                    </td>
                    <td style={{ maxWidth: '200px', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                      {renderValue(field.sample)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div style={{ textAlign: 'center', color: '#666', padding: '2rem' }}>
            الجدول فارغ
          </div>
        )}
      </div>
    );
  };

  const renderData = () => {
    if (loading) {
      return (
        <div className="card">
          <div style={{ textAlign: 'center', padding: '2rem' }}>
            <div className="spinner"></div>
            <div>جاري تحميل البيانات...</div>
          </div>
        </div>
      );
    }

    return (
      <div className="card">
        <div className="card-title">
          📊 بيانات الجدول - {tables[selectedTable]} ({tableData.length})
        </div>
        
        {tableData.length > 0 ? (
          <div style={{ overflow: 'auto', maxHeight: '600px' }}>
            <table className="table">
              <thead>
                <tr>
                  {tableStructure.map(field => (
                    <th key={field.field}>{field.field}</th>
                  ))}
                  <th>إجراءات</th>
                </tr>
              </thead>
              <tbody>
                {tableData.map((record, index) => (
                  <tr key={record.id || index}>
                    {tableStructure.map(field => (
                      <td key={field.field} style={{ 
                        maxWidth: '150px', 
                        overflow: 'hidden', 
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap'
                      }}>
                        {editingRecord && editingRecord.id === record.id ? (
                          <input
                            type="text"
                            value={editingRecord[field.field] || ''}
                            onChange={(e) => setEditingRecord({
                              ...editingRecord,
                              [field.field]: e.target.value
                            })}
                            style={{ width: '100%', padding: '0.25rem' }}
                          />
                        ) : (
                          renderValue(record[field.field])
                        )}
                      </td>
                    ))}
                    <td>
                      {editingRecord && editingRecord.id === record.id ? (
                        <div style={{ display: 'flex', gap: '0.25rem' }}>
                          <button
                            className="btn btn-success btn-sm"
                            onClick={handleSave}
                          >
                            ✅
                          </button>
                          <button
                            className="btn btn-secondary btn-sm"
                            onClick={() => setEditingRecord(null)}
                          >
                            ❌
                          </button>
                        </div>
                      ) : (
                        <div style={{ display: 'flex', gap: '0.25rem' }}>
                          <button
                            className="btn btn-primary btn-sm"
                            onClick={() => handleEdit(record)}
                          >
                            ✏️
                          </button>
                          <button
                            className="btn btn-danger btn-sm"
                            onClick={() => handleDelete(record.id)}
                          >
                            🗑️
                          </button>
                        </div>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div style={{ textAlign: 'center', color: '#666', padding: '2rem' }}>
            لا توجد بيانات في هذا الجدول
          </div>
        )}
      </div>
    );
  };

  return (
    <div>
      {/* Table Selection */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div className="card-title">📋 اختيار الجدول</div>
        <select
          className="form-control"
          value={selectedTable}
          onChange={(e) => setSelectedTable(e.target.value)}
          style={{ maxWidth: '300px' }}
        >
          {Object.keys(tables).map(tableName => (
            <option key={tableName} value={tableName}>
              {tables[tableName]} ({tableName})
            </option>
          ))}
        </select>
      </div>

      {/* View Tabs */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div style={{ display: 'flex', gap: '1rem' }}>
          <button
            className={`btn ${currentView === 'data' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setCurrentView('data')}
          >
            📊 البيانات
          </button>
          <button
            className={`btn ${currentView === 'structure' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setCurrentView('structure')}
          >
            🏗️ الهيكل
          </button>
          <button
            className={`btn ${currentView === 'relations' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setCurrentView('relations')}
          >
            🔗 العلاقات
          </button>
        </div>
      </div>

      {/* Content */}
      {currentView === 'data' && renderData()}
      {currentView === 'structure' && renderStructure()}
      {currentView === 'relations' && renderRelations()}

      {/* Warning */}
      <div style={{ 
        background: '#fff3cd', 
        border: '1px solid #ffeaa7', 
        borderRadius: '5px', 
        padding: '1rem',
        marginTop: '2rem'
      }}>
        <div style={{ color: '#856404', fontWeight: 'bold', marginBottom: '0.5rem' }}>
          ⚠️ تحذير مهم
        </div>
        <div style={{ color: '#856404', fontSize: '0.9rem' }}>
          • تعديل البيانات مباشرة قد يؤثر على سلامة النظام<br/>
          • تأكد من صحة البيانات قبل الحفظ<br/>
          • يُنصح بإنشاء نسخة احتياطية قبل التعديل<br/>
          • حذف السجلات لا يمكن التراجع عنه
        </div>
      </div>
    </div>
  );
};

export default DatabaseViewer;
