import React, { createContext, useContext, useState, useEffect } from 'react';
import { db } from '../database/db';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // التحقق من وجود مستخدم مسجل الدخول في localStorage
    const savedUser = localStorage.getItem('currentUser');
    if (savedUser) {
      setUser(JSON.parse(savedUser));
    }
    setLoading(false);
  }, []);

  const login = async (username, password) => {
    try {
      const foundUser = await db.users
        .where('username')
        .equals(username)
        .and(user => user.password === password && user.isActive)
        .first();

      if (foundUser) {
        const userSession = {
          id: foundUser.id,
          username: foundUser.username,
          role: foundUser.role,
          permissions: foundUser.permissions
        };
        
        setUser(userSession);
        localStorage.setItem('currentUser', JSON.stringify(userSession));
        return { success: true };
      } else {
        return { success: false, message: 'اسم المستخدم أو كلمة المرور غير صحيحة' };
      }
    } catch (error) {
      console.error('خطأ في تسجيل الدخول:', error);
      return { success: false, message: 'حدث خطأ أثناء تسجيل الدخول' };
    }
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('currentUser');
  };

  const hasPermission = (permission) => {
    if (!user) return false;
    if (user.permissions?.includes && user.permissions.includes('all')) return true;
    if (user.role === 'super-admin') return true;
    return user.permissions?.[permission] || false;
  };

  const canEditPostedData = () => {
    if (!user) return false;
    if (user.role === 'super-admin') return true;
    return user.permissions?.editPostedEntries || false;
  };

  const canEditAllData = () => {
    if (!user) return false;
    if (user.role === 'super-admin') return true;
    return user.permissions?.editAllData || false;
  };

  const value = {
    user,
    login,
    logout,
    hasPermission,
    canEditPostedData,
    canEditAllData,
    loading
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
