import React, { useState } from 'react';
import { db } from '../database/db';
import * as XLSX from 'xlsx';

const ItemsList = ({ items, onRefresh }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('');
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState('asc'); // 'asc' للصاعد، 'desc' للهابط
  const [editingItem, setEditingItem] = useState(null);
  const [showEditForm, setShowEditForm] = useState(false);

  const filteredItems = items
    .filter(item => {
      const matchesSearch = !searchTerm ||
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.code.includes(searchTerm);

      const matchesCategory = !filterCategory || item.category === filterCategory;

      return matchesSearch && matchesCategory;
    })
    .sort((a, b) => {
      let result = 0;

      switch (sortBy) {
        case 'name':
          result = a.name.localeCompare(b.name, 'ar');
          break;
        case 'code':
          result = a.code.localeCompare(b.code);
          break;
        case 'unit':
          result = a.unit.localeCompare(b.unit, 'ar');
          break;
        case 'category':
          result = (a.category || '').localeCompare(b.category || '', 'ar');
          break;
        case 'stock':
          result = a.currentStock - b.currentStock;
          break;
        case 'cost':
          result = a.currentCost - b.currentCost;
          break;
        case 'value':
          result = a.totalValue - b.totalValue;
          break;
        default:
          return 0;
      }

      return sortOrder === 'asc' ? result : -result;
    });

  const categories = [...new Set(items.map(item => item.category).filter(Boolean))];

  const handleSort = (column) => {
    if (sortBy === column) {
      // إذا كان نفس العمود، غير اتجاه الترتيب
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      // إذا كان عمود جديد، ابدأ بالترتيب الصاعد
      setSortBy(column);
      setSortOrder('asc');
    }
  };

  const getSortIcon = (column) => {
    if (sortBy !== column) {
      return '↕️'; // أيقونة عامة للترتيب
    }
    return sortOrder === 'asc' ? '↑' : '↓';
  };

  const handleEdit = (item) => {
    setEditingItem({
      id: item.id,
      code: item.code,
      name: item.name,
      unit: item.unit,
      category: item.category,
      minStock: item.minStock,
      isActive: item.isActive
    });
    setShowEditForm(true);
  };

  const handleSaveEdit = async (e) => {
    e.preventDefault();
    try {
      await db.items.update(editingItem.id, {
        code: editingItem.code,
        name: editingItem.name,
        unit: editingItem.unit,
        category: editingItem.category,
        minStock: editingItem.minStock,
        isActive: editingItem.isActive,
        updatedAt: new Date()
      });
      
      setShowEditForm(false);
      setEditingItem(null);
      onRefresh();
      alert('تم تحديث الصنف بنجاح');
    } catch (error) {
      console.error('خطأ في تحديث الصنف:', error);
      alert('حدث خطأ أثناء تحديث الصنف');
    }
  };

  const handleDelete = async (itemId) => {
    if (!confirm('هل أنت متأكد من حذف هذا الصنف؟')) return;
    
    try {
      // التحقق من وجود حركات مخزون
      const movements = await db.stockMovements.where('itemId').equals(itemId).count();
      if (movements > 0) {
        alert('لا يمكن حذف صنف له حركات مخزون');
        return;
      }
      
      await db.items.delete(itemId);
      onRefresh();
      alert('تم حذف الصنف بنجاح');
    } catch (error) {
      console.error('خطأ في حذف الصنف:', error);
      alert('حدث خطأ أثناء حذف الصنف');
    }
  };

  const exportToExcel = () => {
    try {
      const exportData = filteredItems.map(item => ({
        'كود الصنف': item.code,
        'اسم الصنف': item.name,
        'الوحدة': item.unit,
        'الفئة': item.category || '',
        'الرصيد الحالي': item.currentStock,
        'التكلفة الحالية': item.currentCost,
        'إجمالي القيمة': item.totalValue,
        'الحد الأدنى': item.minStock,
        'الحالة': item.isActive ? 'نشط' : 'غير نشط',
        'تاريخ الإنشاء': new Date(item.createdAt).toLocaleDateString('ar-EG')
      }));

      const ws = XLSX.utils.json_to_sheet(exportData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'الأصناف');
      
      const fileName = `الأصناف_${new Date().toISOString().split('T')[0]}.xlsx`;
      XLSX.writeFile(wb, fileName);
      
      alert('تم تصدير قائمة الأصناف بنجاح');
    } catch (error) {
      console.error('خطأ في تصدير البيانات:', error);
      alert('حدث خطأ أثناء تصدير البيانات');
    }
  };

  const getStockStatus = (item) => {
    if (item.currentStock === 0) {
      return { text: 'نفد المخزون', color: '#dc3545' };
    } else if (item.currentStock <= item.minStock) {
      return { text: 'مخزون منخفض', color: '#ffc107' };
    } else {
      return { text: 'متوفر', color: '#28a745' };
    }
  };

  return (
    <div>
      {/* أدوات البحث والتصفية */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div className="card-title">البحث والتصفية</div>
        <div className="grid grid-4">
          <div className="form-group">
            <label className="form-label">البحث</label>
            <input
              type="text"
              className="form-control"
              placeholder="ابحث بالاسم أو الكود..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          <div className="form-group">
            <label className="form-label">تصفية حسب الفئة</label>
            <select
              className="form-control"
              value={filterCategory}
              onChange={(e) => setFilterCategory(e.target.value)}
            >
              <option value="">جميع الفئات</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>
          
          <div className="form-group">
            <label className="form-label">ترتيب حسب</label>
            <div style={{ display: 'flex', gap: '0.5rem' }}>
              <select
                className="form-control"
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                style={{ flex: 2 }}
              >
                <option value="name">الاسم</option>
                <option value="code">الكود</option>
                <option value="unit">الوحدة</option>
                <option value="category">الفئة</option>
                <option value="stock">الرصيد</option>
                <option value="cost">التكلفة</option>
                <option value="value">القيمة</option>
              </select>
              <select
                className="form-control"
                value={sortOrder}
                onChange={(e) => setSortOrder(e.target.value)}
                style={{ flex: 1 }}
              >
                <option value="asc">صاعد ↑</option>
                <option value="desc">هابط ↓</option>
              </select>
            </div>
          </div>
          
          <div className="form-group">
            <label className="form-label">إجراءات</label>
            <div style={{ display: 'flex', gap: '0.5rem' }}>
              <button
                className="btn btn-success"
                onClick={exportToExcel}
                style={{ flex: 1 }}
              >
                📊 تصدير Excel
              </button>
              <button
                className="btn btn-secondary"
                onClick={() => {
                  setSearchTerm('');
                  setFilterCategory('');
                  setSortBy('name');
                  setSortOrder('asc');
                }}
                style={{ flex: 1 }}
              >
                🔄 مسح
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* قائمة الأصناف */}
      <div className="card">
        <div className="card-title">
          قائمة الأصناف ({filteredItems.length} صنف)
        </div>
        
        {filteredItems.length > 0 ? (
          <div style={{ overflow: 'auto' }}>
            <table className="table">
              <thead>
                <tr>
                  <th
                    onClick={() => handleSort('code')}
                    style={{
                      cursor: 'pointer',
                      userSelect: 'none',
                      background: sortBy === 'code' ? '#e3f2fd' : 'transparent',
                      transition: 'background-color 0.2s'
                    }}
                    onMouseEnter={(e) => e.target.style.background = '#f5f5f5'}
                    onMouseLeave={(e) => e.target.style.background = sortBy === 'code' ? '#e3f2fd' : 'transparent'}
                  >
                    الكود {getSortIcon('code')}
                  </th>
                  <th
                    onClick={() => handleSort('name')}
                    style={{
                      cursor: 'pointer',
                      userSelect: 'none',
                      background: sortBy === 'name' ? '#e3f2fd' : 'transparent',
                      transition: 'background-color 0.2s'
                    }}
                    onMouseEnter={(e) => e.target.style.background = '#f5f5f5'}
                    onMouseLeave={(e) => e.target.style.background = sortBy === 'name' ? '#e3f2fd' : 'transparent'}
                  >
                    اسم الصنف {getSortIcon('name')}
                  </th>
                  <th
                    onClick={() => handleSort('unit')}
                    style={{
                      cursor: 'pointer',
                      userSelect: 'none',
                      background: sortBy === 'unit' ? '#e3f2fd' : 'transparent',
                      transition: 'background-color 0.2s'
                    }}
                    onMouseEnter={(e) => e.target.style.background = '#f5f5f5'}
                    onMouseLeave={(e) => e.target.style.background = sortBy === 'unit' ? '#e3f2fd' : 'transparent'}
                  >
                    الوحدة {getSortIcon('unit')}
                  </th>
                  <th
                    onClick={() => handleSort('category')}
                    style={{
                      cursor: 'pointer',
                      userSelect: 'none',
                      background: sortBy === 'category' ? '#e3f2fd' : 'transparent',
                      transition: 'background-color 0.2s'
                    }}
                    onMouseEnter={(e) => e.target.style.background = '#f5f5f5'}
                    onMouseLeave={(e) => e.target.style.background = sortBy === 'category' ? '#e3f2fd' : 'transparent'}
                  >
                    الفئة {getSortIcon('category')}
                  </th>
                  <th
                    onClick={() => handleSort('stock')}
                    style={{
                      cursor: 'pointer',
                      userSelect: 'none',
                      background: sortBy === 'stock' ? '#e3f2fd' : 'transparent',
                      transition: 'background-color 0.2s'
                    }}
                    onMouseEnter={(e) => e.target.style.background = '#f5f5f5'}
                    onMouseLeave={(e) => e.target.style.background = sortBy === 'stock' ? '#e3f2fd' : 'transparent'}
                  >
                    الرصيد الحالي {getSortIcon('stock')}
                  </th>
                  <th
                    onClick={() => handleSort('cost')}
                    style={{
                      cursor: 'pointer',
                      userSelect: 'none',
                      background: sortBy === 'cost' ? '#e3f2fd' : 'transparent',
                      transition: 'background-color 0.2s'
                    }}
                    onMouseEnter={(e) => e.target.style.background = '#f5f5f5'}
                    onMouseLeave={(e) => e.target.style.background = sortBy === 'cost' ? '#e3f2fd' : 'transparent'}
                  >
                    التكلفة {getSortIcon('cost')}
                  </th>
                  <th
                    onClick={() => handleSort('value')}
                    style={{
                      cursor: 'pointer',
                      userSelect: 'none',
                      background: sortBy === 'value' ? '#e3f2fd' : 'transparent',
                      transition: 'background-color 0.2s'
                    }}
                    onMouseEnter={(e) => e.target.style.background = '#f5f5f5'}
                    onMouseLeave={(e) => e.target.style.background = sortBy === 'value' ? '#e3f2fd' : 'transparent'}
                  >
                    إجمالي القيمة {getSortIcon('value')}
                  </th>
                  <th style={{ cursor: 'default' }}>الحالة</th>
                  <th style={{ cursor: 'default' }}>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {filteredItems.map(item => {
                  const status = getStockStatus(item);
                  return (
                    <tr key={item.id}>
                      <td><strong>{item.code}</strong></td>
                      <td>{item.name}</td>
                      <td>{item.unit}</td>
                      <td>{item.category || '-'}</td>
                      <td>
                        <span style={{ 
                          color: status.color, 
                          fontWeight: 'bold' 
                        }}>
                          {item.currentStock}
                        </span>
                      </td>
                      <td>{item.currentCost.toLocaleString('ar-EG')} ج.م</td>
                      <td>{item.totalValue.toLocaleString('ar-EG')} ج.م</td>
                      <td>
                        <span style={{ 
                          background: status.color, 
                          color: 'white', 
                          padding: '0.25rem 0.5rem', 
                          borderRadius: '3px',
                          fontSize: '0.8rem'
                        }}>
                          {status.text}
                        </span>
                      </td>
                      <td>
                        <div style={{ display: 'flex', gap: '0.5rem' }}>
                          <button
                            className="btn btn-primary"
                            style={{ padding: '0.25rem 0.5rem', fontSize: '0.8rem' }}
                            onClick={() => handleEdit(item)}
                          >
                            تعديل
                          </button>
                          <button
                            className="btn btn-danger"
                            style={{ padding: '0.25rem 0.5rem', fontSize: '0.8rem' }}
                            onClick={() => handleDelete(item.id)}
                          >
                            حذف
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        ) : (
          <div style={{ textAlign: 'center', color: '#666', padding: '2rem' }}>
            لا توجد أصناف تطابق معايير البحث
          </div>
        )}
      </div>

      {/* نموذج تعديل الصنف */}
      {showEditForm && editingItem && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            padding: '2rem',
            borderRadius: '10px',
            width: '90%',
            maxWidth: '500px',
            maxHeight: '90vh',
            overflow: 'auto'
          }}>
            <h3 style={{ marginBottom: '1.5rem' }}>تعديل الصنف</h3>

            <form onSubmit={handleSaveEdit}>
              <div className="form-group">
                <label className="form-label">كود الصنف</label>
                <input
                  type="text"
                  className="form-control"
                  value={editingItem.code}
                  onChange={(e) => setEditingItem({...editingItem, code: e.target.value})}
                  required
                />
              </div>

              <div className="form-group">
                <label className="form-label">اسم الصنف</label>
                <input
                  type="text"
                  className="form-control"
                  value={editingItem.name}
                  onChange={(e) => setEditingItem({...editingItem, name: e.target.value})}
                  required
                />
              </div>

              <div className="form-group">
                <label className="form-label">الوحدة</label>
                <input
                  type="text"
                  className="form-control"
                  value={editingItem.unit}
                  onChange={(e) => setEditingItem({...editingItem, unit: e.target.value})}
                  required
                />
              </div>

              <div className="form-group">
                <label className="form-label">الفئة</label>
                <input
                  type="text"
                  className="form-control"
                  value={editingItem.category}
                  onChange={(e) => setEditingItem({...editingItem, category: e.target.value})}
                />
              </div>

              <div className="form-group">
                <label className="form-label">الحد الأدنى للمخزون</label>
                <input
                  type="number"
                  className="form-control"
                  value={editingItem.minStock}
                  onChange={(e) => setEditingItem({...editingItem, minStock: parseInt(e.target.value) || 0})}
                  min="0"
                />
              </div>

              <div className="form-group">
                <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                  <input
                    type="checkbox"
                    checked={editingItem.isActive}
                    onChange={(e) => setEditingItem({...editingItem, isActive: e.target.checked})}
                  />
                  الصنف نشط
                </label>
              </div>

              <div style={{ display: 'flex', gap: '1rem', marginTop: '1.5rem' }}>
                <button type="submit" className="btn btn-primary">
                  حفظ التغييرات
                </button>
                <button 
                  type="button" 
                  className="btn btn-secondary"
                  onClick={() => {
                    setShowEditForm(false);
                    setEditingItem(null);
                  }}
                >
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default ItemsList;
