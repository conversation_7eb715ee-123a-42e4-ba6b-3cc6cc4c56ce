import React, { useState, useEffect } from 'react';
import { db, createPaymentJournalEntry } from '../database/db';
import { useAuth } from '../contexts/AuthContext';
import * as XLSX from 'xlsx';

const PaymentManagement = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('journal-entry');
  const [customers, setCustomers] = useState([]);
  const [suppliers, setSuppliers] = useState([]);
  const [payments, setPayments] = useState([]);
  const [laborVouchers, setLaborVouchers] = useState([]);
  const [purchaseInvoices, setPurchaseInvoices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showJournalEntry, setShowJournalEntry] = useState(false);
  const [journalEntryPreview, setJournalEntryPreview] = useState(null);

  // بيانات القيد اليومي
  const [journalData, setJournalData] = useState({
    entryNumber: '',
    date: new Date().toISOString().split('T')[0],
    description: '',
    reference: '',
    type: 'general',
    entries: []
  });

  // بيانات بند القيد الحالي
  const [currentEntry, setCurrentEntry] = useState({
    accountCode: '',
    accountName: '',
    description: '',
    debit: '',
    credit: ''
  });

  const [paymentData, setPaymentData] = useState({
    type: 'receipt', // 'receipt' أو 'payment'
    amount: 0,
    customerId: '',
    supplierId: '',
    paymentMethod: 'cash', // 'cash' أو 'bank'
    description: '',
    reference: '',
    date: new Date().toISOString().split('T')[0]
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const [customersData, suppliersData, paymentsData, laborVouchersData, purchaseInvoicesData] = await Promise.all([
        db.customers.toArray(),
        db.suppliers.toArray(),
        db.journalEntries.where('type').anyOf(['receipt', 'payment']).toArray(),
        db.laborVouchers.toArray(),
        db.purchaseInvoices.toArray()
      ]);

      setCustomers(customersData);
      setSuppliers(suppliersData);
      setPayments(paymentsData);
      setLaborVouchers(laborVouchersData);
      setPurchaseInvoices(purchaseInvoicesData);

      // إنشاء رقم قيد تلقائي
      const entriesCount = await db.journalEntries.count();
      setJournalData(prev => ({
        ...prev,
        entryNumber: `JE-${String(entriesCount + 1).padStart(6, '0')}`
      }));
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
    } finally {
      setLoading(false);
    }
  };

  // دالة للحصول على اسم الحساب تلقائياً
  const getAccountName = (accountCode) => {
    const accounts = {
      '1111001': 'الصندوق',
      '1111002': 'البنك - الحساب الجاري',
      '1111003': 'البنك - حساب التوفير',
      '1121001': 'العملاء',
      '1121002': 'أوراق القبض',
      '1131001': 'المخزون - مواد خام',
      '1131002': 'المخزون - منتجات تامة',
      '1141001': 'المصروفات المدفوعة مقدماً',
      '1151001': 'الأصول الثابتة - أراضي',
      '1151002': 'الأصول الثابتة - مباني',
      '1151003': 'الأصول الثابتة - معدات',
      '1161001': 'ضريبة القيمة المضافة المدفوعة',
      '2111001': 'الموردون',
      '2111002': 'أوراق الدفع',
      '2121001': 'المصروفات المستحقة',
      '2131001': 'القروض قصيرة الأجل',
      '2141001': 'ضريبة القيمة المضافة المستحقة',
      '3111001': 'رأس المال',
      '3121001': 'الأرباح المحتجزة',
      '3131001': 'احتياطيات',
      '4111001': 'المبيعات',
      '4121001': 'إيرادات أخرى',
      '5111001': 'تكلفة البضاعة المباعة',
      '5121001': 'أجور العمالة',
      '5131001': 'مصروفات إدارية',
      '5141001': 'مصروفات تسويقية',
      '5151001': 'مصروفات مالية'
    };

    return accounts[accountCode] || '';
  };

  // دوال إدارة القيود اليومية
  const addEntryLine = () => {
    if (!currentEntry.accountCode.trim()) {
      alert('يرجى إدخال رقم الحساب');
      return;
    }

    if (!currentEntry.debit && !currentEntry.credit) {
      alert('يرجى إدخال مبلغ في المدين أو الدائن');
      return;
    }

    if (currentEntry.debit && currentEntry.credit) {
      alert('لا يمكن أن يكون البند مدين ودائن في نفس الوقت');
      return;
    }

    const debitAmount = parseFloat(currentEntry.debit) || 0;
    const creditAmount = parseFloat(currentEntry.credit) || 0;

    if (debitAmount <= 0 && creditAmount <= 0) {
      alert('يرجى إدخال مبلغ صحيح أكبر من صفر');
      return;
    }

    const newEntry = {
      ...currentEntry,
      accountCode: currentEntry.accountCode.trim(),
      accountName: currentEntry.accountName.trim() || getAccountName(currentEntry.accountCode.trim()),
      description: currentEntry.description.trim(),
      debit: debitAmount,
      credit: creditAmount
    };

    setJournalData(prev => ({
      ...prev,
      entries: [...prev.entries, newEntry]
    }));

    // مسح البيانات الحالية
    setCurrentEntry({
      accountCode: '',
      accountName: '',
      description: '',
      debit: '',
      credit: ''
    });
  };

  const removeEntryLine = (index) => {
    setJournalData(prev => ({
      ...prev,
      entries: prev.entries.filter((_, i) => i !== index)
    }));
  };

  const getTotalDebit = () => {
    return journalData.entries.reduce((sum, entry) => sum + entry.debit, 0);
  };

  const getTotalCredit = () => {
    return journalData.entries.reduce((sum, entry) => sum + entry.credit, 0);
  };

  const isBalanced = () => {
    return getTotalDebit() === getTotalCredit() && getTotalDebit() > 0;
  };

  const saveJournalEntry = async () => {
    try {
      if (!journalData.description.trim()) {
        alert('يرجى إدخال وصف القيد');
        return;
      }

      if (journalData.entries.length === 0) {
        alert('يرجى إضافة بنود للقيد');
        return;
      }

      if (!isBalanced()) {
        alert('القيد غير متوازن - يجب أن يكون إجمالي المدين = إجمالي الدائن');
        return;
      }

      await db.journalEntries.add({
        ...journalData,
        totalDebit: getTotalDebit(),
        totalCredit: getTotalCredit(),
        status: 'posted',
        userId: user.id,
        createdAt: new Date()
      });

      alert('تم حفظ القيد بنجاح');
      resetJournalForm();
      loadData();
    } catch (error) {
      console.error('خطأ في حفظ القيد:', error);
      alert('حدث خطأ أثناء حفظ القيد');
    }
  };

  const resetJournalForm = () => {
    setJournalData({
      entryNumber: '',
      date: new Date().toISOString().split('T')[0],
      description: '',
      reference: '',
      type: 'general',
      entries: []
    });
    setCurrentEntry({
      accountCode: '',
      accountName: '',
      description: '',
      debit: '',
      credit: ''
    });
    loadData(); // لإعادة تحديد رقم القيد
  };

  // دالة ترحيل إذن صرف العمالة
  const postLaborVoucher = async (voucher) => {
    try {
      const journalEntry = {
        entryNumber: `JE-${String(await db.journalEntries.count() + 1).padStart(6, '0')}`,
        date: voucher.date,
        description: `صرف عمالة - ${voucher.workerName} - ${voucher.projectName}`,
        reference: voucher.voucherNumber,
        type: 'payment',
        entries: [
          {
            accountCode: '5121001', // حساب أجور العمالة
            accountName: 'أجور العمالة',
            description: `صرف عمالة - ${voucher.workerName}`,
            debit: voucher.amount,
            credit: 0
          },
          {
            accountCode: '1111001', // حساب الصندوق
            accountName: 'الصندوق',
            description: `صرف نقدي - ${voucher.workerName}`,
            debit: 0,
            credit: voucher.amount
          }
        ],
        totalDebit: voucher.amount,
        totalCredit: voucher.amount,
        status: 'posted',
        userId: user.id,
        createdAt: new Date()
      };

      await db.journalEntries.add(journalEntry);

      // تحديث حالة الإذن
      await db.laborVouchers.update(voucher.id, { status: 'posted' });

      alert('تم ترحيل إذن صرف العمالة بنجاح');
      loadData();
    } catch (error) {
      console.error('خطأ في ترحيل إذن العمالة:', error);
      alert('حدث خطأ أثناء ترحيل إذن العمالة');
    }
  };

  // دالة تسوية فاتورة المشتريات
  const settlePurchaseInvoice = async (invoice) => {
    const remainingAmount = invoice.total - (invoice.paidAmount || 0);
    const paymentAmount = prompt(`أدخل مبلغ الدفع (المتبقي: ${remainingAmount.toLocaleString('ar-EG')} ج.م):`);

    if (!paymentAmount || isNaN(paymentAmount) || parseFloat(paymentAmount) <= 0) {
      return;
    }

    const amount = parseFloat(paymentAmount);
    if (amount > remainingAmount) {
      alert('مبلغ الدفع أكبر من المبلغ المتبقي');
      return;
    }

    try {
      const supplier = suppliers.find(s => s.id === invoice.supplierId);
      const supplierAccountCode = `2211${String(supplier.id).padStart(3, '0')}`;

      const journalEntry = {
        entryNumber: `JE-${String(await db.journalEntries.count() + 1).padStart(6, '0')}`,
        date: new Date().toISOString().split('T')[0],
        description: `تسوية فاتورة مشتريات - ${supplier?.name || 'غير محدد'}`,
        reference: invoice.invoiceNumber,
        type: 'payment',
        entries: [
          {
            accountCode: supplierAccountCode,
            accountName: supplier?.name || 'مورد',
            description: `تسوية فاتورة ${invoice.invoiceNumber}`,
            debit: amount,
            credit: 0
          },
          {
            accountCode: '1111001', // حساب الصندوق
            accountName: 'الصندوق',
            description: `دفع نقدي - ${supplier?.name || 'مورد'}`,
            debit: 0,
            credit: amount
          }
        ],
        totalDebit: amount,
        totalCredit: amount,
        status: 'posted',
        userId: user.id,
        createdAt: new Date()
      };

      await db.journalEntries.add(journalEntry);

      // تحديث حالة الفاتورة
      const newPaidAmount = (invoice.paidAmount || 0) + amount;
      const newStatus = newPaidAmount >= invoice.total ? 'paid' : 'partial';

      await db.purchaseInvoices.update(invoice.id, {
        paidAmount: newPaidAmount,
        paymentStatus: newStatus
      });

      alert('تم تسوية الفاتورة بنجاح');
      loadData();
    } catch (error) {
      console.error('خطأ في تسوية الفاتورة:', error);
      alert('حدث خطأ أثناء تسوية الفاتورة');
    }
  };

  const handleSave = async () => {
    try {
      // التحقق من البيانات
      if (!paymentData.amount || paymentData.amount <= 0) {
        alert('يرجى إدخال مبلغ صحيح');
        return;
      }

      if (paymentData.type === 'receipt' && !paymentData.customerId) {
        alert('يرجى اختيار العميل');
        return;
      }

      if (paymentData.type === 'payment' && !paymentData.supplierId) {
        alert('يرجى اختيار المورد');
        return;
      }

      if (!paymentData.description.trim()) {
        alert('يرجى إدخال وصف العملية');
        return;
      }

      // إنشاء القيد المحاسبي للمراجعة
      const journalEntry = await createPaymentJournalEntry({
        ...paymentData,
        userId: user.id
      });

      // عرض القيد المحاسبي للمراجعة
      setJournalEntryPreview(journalEntry);
      setShowJournalEntry(true);

    } catch (error) {
      console.error('خطأ في إنشاء القيد المحاسبي:', error);
      alert('حدث خطأ أثناء إنشاء القيد المحاسبي: ' + error.message);
    }
  };

  const handleFinalSave = async () => {
    try {
      // حفظ القيد المحاسبي
      await db.journalEntries.add({
        ...journalEntryPreview,
        status: 'posted'
      });

      // تحديث رصيد العميل أو المورد
      if (paymentData.type === 'receipt' && paymentData.customerId) {
        const customer = await db.customers.get(parseInt(paymentData.customerId));
        const newBalance = (customer.balance || 0) - paymentData.amount;
        await db.customers.update(parseInt(paymentData.customerId), { balance: newBalance });
      } else if (paymentData.type === 'payment' && paymentData.supplierId) {
        const supplier = await db.suppliers.get(parseInt(paymentData.supplierId));
        const newBalance = (supplier.balance || 0) - paymentData.amount;
        await db.suppliers.update(parseInt(paymentData.supplierId), { balance: newBalance });
      }

      alert(`تم حفظ ${paymentData.type === 'receipt' ? 'التحصيل' : 'الدفع'} والقيد المحاسبي بنجاح`);
      setShowJournalEntry(false);
      setJournalEntryPreview(null);
      resetForm();
      loadData();
    } catch (error) {
      console.error('خطأ في حفظ العملية:', error);
      alert('حدث خطأ أثناء حفظ العملية');
    }
  };

  const resetForm = () => {
    setPaymentData({
      type: activeTab === 'receipts' ? 'receipt' : 'payment',
      amount: 0,
      customerId: '',
      supplierId: '',
      paymentMethod: 'cash',
      description: '',
      reference: '',
      date: new Date().toISOString().split('T')[0]
    });
  };

  const exportToExcel = () => {
    const filteredPayments = payments.filter(p => p.type === (activeTab === 'receipts' ? 'receipt' : 'payment'));
    
    const data = filteredPayments.map(payment => ({
      'رقم القيد': payment.entryNumber,
      'التاريخ': new Date(payment.date).toLocaleDateString('ar-EG'),
      'النوع': payment.type === 'receipt' ? 'تحصيل' : 'دفع',
      'المبلغ': payment.totalDebit,
      'طريقة الدفع': payment.entries.find(e => e.accountCode.includes('111'))?.accountName || '',
      'الوصف': payment.description,
      'المرجع': payment.reference || '',
      'المستخدم': payment.userId
    }));

    const ws = XLSX.utils.json_to_sheet(data);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, activeTab === 'receipts' ? 'التحصيلات' : 'المدفوعات');
    XLSX.writeFile(wb, `${activeTab === 'receipts' ? 'التحصيلات' : 'المدفوعات'}_${new Date().toLocaleDateString('ar-EG')}.xlsx`);
  };

  if (loading) {
    return (
      <div className="container">
        <div className="loading">
          <div className="spinner"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container">
      {/* Header */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h2 style={{ margin: 0, color: '#333' }}>📝 نظام القيود المحاسبية المتقدم</h2>
          <div style={{ display: 'flex', gap: '1rem' }}>
            <button className="btn btn-success" onClick={exportToExcel}>
              📊 تصدير Excel
            </button>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <div style={{ display: 'flex', borderBottom: '1px solid #ddd', flexWrap: 'wrap' }}>
          <button
            className={`tab-button ${activeTab === 'journal-entry' ? 'active' : ''}`}
            onClick={() => setActiveTab('journal-entry')}
          >
            📝 قيد يومي عام
          </button>
          <button
            className={`tab-button ${activeTab === 'receipts' ? 'active' : ''}`}
            onClick={() => {
              setActiveTab('receipts');
              setPaymentData({...paymentData, type: 'receipt'});
            }}
          >
            💰 تحصيل من عميل
          </button>
          <button
            className={`tab-button ${activeTab === 'payments' ? 'active' : ''}`}
            onClick={() => {
              setActiveTab('payments');
              setPaymentData({...paymentData, type: 'payment'});
            }}
          >
            💸 دفع لمورد
          </button>
          <button
            className={`tab-button ${activeTab === 'labor-vouchers' ? 'active' : ''}`}
            onClick={() => setActiveTab('labor-vouchers')}
          >
            👷 أذون صرف العمالة
          </button>
          <button
            className={`tab-button ${activeTab === 'purchase-settlement' ? 'active' : ''}`}
            onClick={() => setActiveTab('purchase-settlement')}
          >
            🛒 تسوية فواتير المشتريات
          </button>
          <button
            className={`tab-button ${activeTab === 'history' ? 'active' : ''}`}
            onClick={() => setActiveTab('history')}
          >
            📋 سجل العمليات
          </button>
        </div>

        <div style={{ padding: '2rem' }}>
          {/* تبويب القيد اليومي العام */}
          {activeTab === 'journal-entry' && (
            <div>
              <h3 style={{ marginBottom: '1.5rem', color: '#007bff' }}>
                📝 إنشاء قيد يومي عام
              </h3>

              {/* معلومات القيد */}
              <div className="grid grid-2" style={{ gap: '2rem', marginBottom: '2rem' }}>
                <div>
                  <div className="form-group">
                    <label className="form-label">رقم القيد</label>
                    <input
                      type="text"
                      className="form-control"
                      value={journalData.entryNumber}
                      onChange={(e) => setJournalData({...journalData, entryNumber: e.target.value})}
                      placeholder="رقم القيد"
                    />
                  </div>

                  <div className="form-group">
                    <label className="form-label">التاريخ</label>
                    <input
                      type="date"
                      className="form-control"
                      value={journalData.date}
                      onChange={(e) => setJournalData({...journalData, date: e.target.value})}
                    />
                  </div>

                  <div className="form-group">
                    <label className="form-label">نوع القيد</label>
                    <select
                      className="form-control"
                      value={journalData.type}
                      onChange={(e) => setJournalData({...journalData, type: e.target.value})}
                    >
                      <option value="general">عام</option>
                      <option value="adjustment">تسوية</option>
                      <option value="opening">افتتاحي</option>
                      <option value="closing">ختامي</option>
                    </select>
                  </div>
                </div>

                <div>
                  <div className="form-group">
                    <label className="form-label">وصف القيد *</label>
                    <textarea
                      className="form-control"
                      value={journalData.description}
                      onChange={(e) => setJournalData({...journalData, description: e.target.value})}
                      rows="3"
                      placeholder="أدخل وصف القيد"
                    />
                  </div>

                  <div className="form-group">
                    <label className="form-label">المرجع</label>
                    <input
                      type="text"
                      className="form-control"
                      value={journalData.reference}
                      onChange={(e) => setJournalData({...journalData, reference: e.target.value})}
                      placeholder="رقم المرجع أو الوثيقة"
                    />
                  </div>
                </div>
              </div>

              {/* إضافة بند جديد */}
              <div style={{
                background: '#f8f9fa',
                padding: '1.5rem',
                borderRadius: '5px',
                marginBottom: '2rem',
                border: '2px dashed #007bff'
              }}>
                <h4 style={{ marginBottom: '1rem', color: '#007bff' }}>➕ إضافة بند جديد</h4>

                {/* تلميحات مساعدة */}
                <div style={{
                  background: '#e3f2fd',
                  padding: '0.75rem',
                  borderRadius: '3px',
                  marginBottom: '1rem',
                  fontSize: '0.9rem',
                  color: '#1976d2'
                }}>
                  💡 <strong>تلميح:</strong> أدخل رقم الحساب وسيتم ملء اسم الحساب تلقائياً.
                  يمكن إدخال مبلغ في المدين أو الدائن فقط، وليس كلاهما.
                </div>

                {/* أرقام الحسابات الشائعة */}
                <details style={{ marginBottom: '1rem' }}>
                  <summary style={{
                    cursor: 'pointer',
                    color: '#007bff',
                    fontWeight: 'bold',
                    padding: '0.5rem',
                    background: '#f8f9fa',
                    borderRadius: '3px'
                  }}>
                    📋 أرقام الحسابات الشائعة
                  </summary>
                  <div style={{
                    marginTop: '0.5rem',
                    padding: '1rem',
                    background: 'white',
                    borderRadius: '3px',
                    border: '1px solid #dee2e6'
                  }}>
                    <div className="grid grid-3" style={{ gap: '1rem', fontSize: '0.85rem' }}>
                      <div>
                        <strong>الأصول:</strong><br/>
                        1111001 - الصندوق<br/>
                        1111002 - البنك<br/>
                        1121001 - العملاء<br/>
                        1131001 - المخزون
                      </div>
                      <div>
                        <strong>الخصوم:</strong><br/>
                        2111001 - الموردون<br/>
                        2121001 - المصروفات المستحقة<br/>
                        2141001 - ضريبة ق.م. مستحقة
                      </div>
                      <div>
                        <strong>الإيرادات والمصروفات:</strong><br/>
                        4111001 - المبيعات<br/>
                        5111001 - تكلفة البضاعة<br/>
                        5121001 - أجور العمالة<br/>
                        5131001 - مصروفات إدارية
                      </div>
                    </div>
                  </div>
                </details>

                <div className="grid grid-5" style={{ gap: '1rem', marginBottom: '1rem' }}>
                  <div className="form-group">
                    <label className="form-label">رقم الحساب *</label>
                    <input
                      type="text"
                      className="form-control"
                      value={currentEntry.accountCode}
                      onChange={(e) => {
                        const accountCode = e.target.value;
                        setCurrentEntry({
                          ...currentEntry,
                          accountCode: accountCode,
                          accountName: getAccountName(accountCode)
                        });
                      }}
                      placeholder="أدخل رقم الحساب (مثال: 1111001)"
                      style={{ fontFamily: 'monospace', fontSize: '1rem' }}
                    />
                  </div>

                  <div className="form-group">
                    <label className="form-label">اسم الحساب</label>
                    <input
                      type="text"
                      className="form-control"
                      value={currentEntry.accountName}
                      onChange={(e) => setCurrentEntry({...currentEntry, accountName: e.target.value})}
                      placeholder="اسم الحساب (يتم ملؤه تلقائياً)"
                      style={{ background: currentEntry.accountName ? '#f8f9fa' : 'white' }}
                    />
                  </div>

                  <div className="form-group">
                    <label className="form-label">البيان</label>
                    <input
                      type="text"
                      className="form-control"
                      value={currentEntry.description}
                      onChange={(e) => setCurrentEntry({...currentEntry, description: e.target.value})}
                      placeholder="بيان البند"
                    />
                  </div>

                  <div className="form-group">
                    <label className="form-label">مدين</label>
                    <input
                      type="number"
                      className="form-control"
                      value={currentEntry.debit}
                      onChange={(e) => setCurrentEntry({...currentEntry, debit: e.target.value, credit: ''})}
                      min="0"
                      step="0.01"
                      placeholder="0.00"
                      style={{
                        color: currentEntry.debit ? '#dc3545' : '#6c757d',
                        fontWeight: currentEntry.debit ? 'bold' : 'normal'
                      }}
                    />
                  </div>

                  <div className="form-group">
                    <label className="form-label">دائن</label>
                    <input
                      type="number"
                      className="form-control"
                      value={currentEntry.credit}
                      onChange={(e) => setCurrentEntry({...currentEntry, credit: e.target.value, debit: ''})}
                      min="0"
                      step="0.01"
                      placeholder="0.00"
                      style={{
                        color: currentEntry.credit ? '#28a745' : '#6c757d',
                        fontWeight: currentEntry.credit ? 'bold' : 'normal'
                      }}
                    />
                  </div>
                </div>

                <div style={{ textAlign: 'center' }}>
                  <button
                    className="btn btn-primary"
                    onClick={addEntryLine}
                    disabled={!currentEntry.accountCode.trim() || (!currentEntry.debit && !currentEntry.credit)}
                    style={{
                      padding: '0.75rem 2rem',
                      fontSize: '1rem',
                      opacity: (!currentEntry.accountCode.trim() || (!currentEntry.debit && !currentEntry.credit)) ? 0.6 : 1
                    }}
                  >
                    ➕ إضافة البند
                  </button>
                </div>
              </div>

              {/* جدول بنود القيد */}
              {journalData.entries.length > 0 && (
                <div style={{ marginBottom: '2rem' }}>
                  <h4 style={{ marginBottom: '1rem', color: '#007bff' }}>📋 بنود القيد</h4>

                  <div style={{ overflow: 'auto' }}>
                    <table className="table">
                      <thead>
                        <tr style={{ background: '#007bff', color: 'white' }}>
                          <th>رقم الحساب</th>
                          <th>اسم الحساب</th>
                          <th>البيان</th>
                          <th>مدين</th>
                          <th>دائن</th>
                          <th>الإجراءات</th>
                        </tr>
                      </thead>
                      <tbody>
                        {journalData.entries.map((entry, index) => (
                          <tr key={index}>
                            <td style={{ fontFamily: 'monospace', fontWeight: 'bold', color: '#007bff' }}>
                              {entry.accountCode}
                            </td>
                            <td style={{ fontWeight: 'bold' }}>{entry.accountName}</td>
                            <td>{entry.description}</td>
                            <td style={{
                              color: entry.debit > 0 ? '#dc3545' : '#6c757d',
                              fontWeight: entry.debit > 0 ? 'bold' : 'normal'
                            }}>
                              {entry.debit > 0 ? entry.debit.toLocaleString('ar-EG') : '-'}
                            </td>
                            <td style={{
                              color: entry.credit > 0 ? '#28a745' : '#6c757d',
                              fontWeight: entry.credit > 0 ? 'bold' : 'normal'
                            }}>
                              {entry.credit > 0 ? entry.credit.toLocaleString('ar-EG') : '-'}
                            </td>
                            <td>
                              <button
                                className="btn btn-sm btn-danger"
                                onClick={() => removeEntryLine(index)}
                                style={{ padding: '0.25rem 0.5rem' }}
                              >
                                🗑️ حذف
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                      <tfoot>
                        <tr style={{
                          background: isBalanced() ? '#d4edda' : '#f8d7da',
                          fontWeight: 'bold'
                        }}>
                          <td colSpan="3" style={{ textAlign: 'center' }}>الإجمالي</td>
                          <td style={{ color: '#dc3545' }}>
                            {getTotalDebit().toLocaleString('ar-EG')} ج.م
                          </td>
                          <td style={{ color: '#28a745' }}>
                            {getTotalCredit().toLocaleString('ar-EG')} ج.م
                          </td>
                          <td>
                            {isBalanced() ? '✅ متوازن' : '❌ غير متوازن'}
                          </td>
                        </tr>
                      </tfoot>
                    </table>
                  </div>
                </div>
              )}

              {/* أزرار الحفظ */}
              <div style={{ textAlign: 'center', marginTop: '2rem' }}>
                <button
                  className="btn btn-success"
                  onClick={saveJournalEntry}
                  disabled={!isBalanced()}
                  style={{ padding: '0.75rem 2rem', marginRight: '1rem' }}
                >
                  💾 حفظ القيد
                </button>
                <button
                  className="btn btn-secondary"
                  onClick={resetJournalForm}
                  style={{ padding: '0.75rem 2rem' }}
                >
                  🔄 مسح البيانات
                </button>
              </div>
            </div>
          )}

          {(activeTab === 'receipts' || activeTab === 'payments') && (
            <div>
              <h3 style={{ marginBottom: '1.5rem', color: '#007bff' }}>
                {activeTab === 'receipts' ? '💰 تحصيل من عميل' : '💸 دفع لمورد'}
              </h3>

              <div className="grid grid-2" style={{ gap: '2rem' }}>
                <div>
                  <div className="form-group">
                    <label className="form-label">المبلغ *</label>
                    <input
                      type="number"
                      className="form-control"
                      value={paymentData.amount}
                      onChange={(e) => setPaymentData({...paymentData, amount: parseFloat(e.target.value) || 0})}
                      min="0"
                      step="0.01"
                      placeholder="أدخل المبلغ"
                    />
                  </div>

                  {activeTab === 'receipts' ? (
                    <div className="form-group">
                      <label className="form-label">العميل *</label>
                      <select
                        className="form-control"
                        value={paymentData.customerId}
                        onChange={(e) => setPaymentData({...paymentData, customerId: e.target.value})}
                      >
                        <option value="">اختر العميل</option>
                        {customers.map(customer => (
                          <option key={customer.id} value={customer.id}>
                            {customer.name} - رصيد: {(customer.balance || 0).toLocaleString('ar-EG')} ج.م
                          </option>
                        ))}
                      </select>
                    </div>
                  ) : (
                    <div className="form-group">
                      <label className="form-label">المورد *</label>
                      <select
                        className="form-control"
                        value={paymentData.supplierId}
                        onChange={(e) => setPaymentData({...paymentData, supplierId: e.target.value})}
                      >
                        <option value="">اختر المورد</option>
                        {suppliers.map(supplier => (
                          <option key={supplier.id} value={supplier.id}>
                            {supplier.name} - رصيد: {(supplier.balance || 0).toLocaleString('ar-EG')} ج.م
                          </option>
                        ))}
                      </select>
                    </div>
                  )}

                  <div className="form-group">
                    <label className="form-label">طريقة الدفع</label>
                    <select
                      className="form-control"
                      value={paymentData.paymentMethod}
                      onChange={(e) => setPaymentData({...paymentData, paymentMethod: e.target.value})}
                    >
                      <option value="cash">نقدي</option>
                      <option value="bank">بنكي</option>
                    </select>
                  </div>
                </div>

                <div>
                  <div className="form-group">
                    <label className="form-label">وصف العملية *</label>
                    <textarea
                      className="form-control"
                      value={paymentData.description}
                      onChange={(e) => setPaymentData({...paymentData, description: e.target.value})}
                      rows="3"
                      placeholder={`أدخل وصف ${activeTab === 'receipts' ? 'التحصيل' : 'الدفع'}`}
                    />
                  </div>

                  <div className="form-group">
                    <label className="form-label">المرجع</label>
                    <input
                      type="text"
                      className="form-control"
                      value={paymentData.reference}
                      onChange={(e) => setPaymentData({...paymentData, reference: e.target.value})}
                      placeholder="رقم الإيصال أو المرجع"
                    />
                  </div>

                  <div className="form-group">
                    <label className="form-label">التاريخ</label>
                    <input
                      type="date"
                      className="form-control"
                      value={paymentData.date}
                      onChange={(e) => setPaymentData({...paymentData, date: e.target.value})}
                    />
                  </div>
                </div>
              </div>

              <div style={{ marginTop: '2rem', textAlign: 'center' }}>
                <button className="btn btn-success" onClick={handleSave} style={{ padding: '0.75rem 2rem' }}>
                  💾 حفظ {activeTab === 'receipts' ? 'التحصيل' : 'الدفع'}
                </button>
                <button className="btn btn-secondary" onClick={resetForm} style={{ padding: '0.75rem 2rem', marginLeft: '1rem' }}>
                  🔄 مسح البيانات
                </button>
              </div>
            </div>
          )}

          {/* تبويب أذون صرف العمالة */}
          {activeTab === 'labor-vouchers' && (
            <div>
              <h3 style={{ marginBottom: '1.5rem', color: '#007bff' }}>👷 أذون صرف العمالة</h3>

              <div style={{ overflow: 'auto', marginBottom: '2rem' }}>
                <table className="table">
                  <thead>
                    <tr style={{ background: '#fd7e14', color: 'white' }}>
                      <th>رقم الإذن</th>
                      <th>التاريخ</th>
                      <th>العامل</th>
                      <th>المشروع</th>
                      <th>المبلغ</th>
                      <th>الحالة</th>
                      <th>الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {laborVouchers.map(voucher => (
                      <tr key={voucher.id}>
                        <td style={{ fontFamily: 'monospace', fontWeight: 'bold' }}>
                          {voucher.voucherNumber}
                        </td>
                        <td>{new Date(voucher.date).toLocaleDateString('ar-EG')}</td>
                        <td>{voucher.workerName}</td>
                        <td>{voucher.projectName}</td>
                        <td style={{ fontWeight: 'bold', color: '#dc3545' }}>
                          {voucher.amount.toLocaleString('ar-EG')} ج.م
                        </td>
                        <td>
                          <span style={{
                            padding: '0.25rem 0.5rem',
                            borderRadius: '3px',
                            fontSize: '0.8rem',
                            fontWeight: 'bold',
                            background: voucher.status === 'posted' ? '#28a745' : '#ffc107',
                            color: 'white'
                          }}>
                            {voucher.status === 'posted' ? 'مرحل' : 'معلق'}
                          </span>
                        </td>
                        <td>
                          {voucher.status !== 'posted' && (
                            <button
                              className="btn btn-sm btn-success"
                              onClick={() => postLaborVoucher(voucher)}
                              style={{ padding: '0.25rem 0.5rem' }}
                            >
                              📝 ترحيل
                            </button>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>

                {laborVouchers.length === 0 && (
                  <div style={{
                    textAlign: 'center',
                    padding: '3rem',
                    color: '#666',
                    background: '#f8f9fa',
                    borderRadius: '5px'
                  }}>
                    <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>👷</div>
                    <div style={{ fontSize: '1.2rem', fontWeight: 'bold', marginBottom: '0.5rem' }}>
                      لا توجد أذون صرف عمالة
                    </div>
                    <div>لم يتم العثور على أذون صرف عمالة</div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* تبويب تسوية فواتير المشتريات */}
          {activeTab === 'purchase-settlement' && (
            <div>
              <h3 style={{ marginBottom: '1.5rem', color: '#007bff' }}>🛒 تسوية فواتير المشتريات</h3>

              <div style={{ overflow: 'auto', marginBottom: '2rem' }}>
                <table className="table">
                  <thead>
                    <tr style={{ background: '#6f42c1', color: 'white' }}>
                      <th>رقم الفاتورة</th>
                      <th>التاريخ</th>
                      <th>المورد</th>
                      <th>إجمالي الفاتورة</th>
                      <th>المدفوع</th>
                      <th>المتبقي</th>
                      <th>الحالة</th>
                      <th>الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {purchaseInvoices.filter(invoice => invoice.paymentStatus !== 'paid').map(invoice => (
                      <tr key={invoice.id}>
                        <td style={{ fontFamily: 'monospace', fontWeight: 'bold' }}>
                          {invoice.invoiceNumber}
                        </td>
                        <td>{new Date(invoice.date).toLocaleDateString('ar-EG')}</td>
                        <td>{suppliers.find(s => s.id === invoice.supplierId)?.name || '-'}</td>
                        <td style={{ fontWeight: 'bold', color: '#dc3545' }}>
                          {invoice.total.toLocaleString('ar-EG')} ج.م
                        </td>
                        <td style={{ fontWeight: 'bold', color: '#28a745' }}>
                          {(invoice.paidAmount || 0).toLocaleString('ar-EG')} ج.م
                        </td>
                        <td style={{ fontWeight: 'bold', color: '#fd7e14' }}>
                          {(invoice.total - (invoice.paidAmount || 0)).toLocaleString('ar-EG')} ج.م
                        </td>
                        <td>
                          <span style={{
                            padding: '0.25rem 0.5rem',
                            borderRadius: '3px',
                            fontSize: '0.8rem',
                            fontWeight: 'bold',
                            background: invoice.paymentStatus === 'paid' ? '#28a745' :
                                       invoice.paymentStatus === 'partial' ? '#ffc107' : '#dc3545',
                            color: 'white'
                          }}>
                            {invoice.paymentStatus === 'paid' ? 'مدفوع' :
                             invoice.paymentStatus === 'partial' ? 'جزئي' : 'معلق'}
                          </span>
                        </td>
                        <td>
                          <button
                            className="btn btn-sm btn-primary"
                            onClick={() => settlePurchaseInvoice(invoice)}
                            style={{ padding: '0.25rem 0.5rem' }}
                          >
                            💰 تسوية
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>

                {purchaseInvoices.filter(invoice => invoice.paymentStatus !== 'paid').length === 0 && (
                  <div style={{
                    textAlign: 'center',
                    padding: '3rem',
                    color: '#666',
                    background: '#f8f9fa',
                    borderRadius: '5px'
                  }}>
                    <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>🛒</div>
                    <div style={{ fontSize: '1.2rem', fontWeight: 'bold', marginBottom: '0.5rem' }}>
                      لا توجد فواتير مشتريات معلقة
                    </div>
                    <div>جميع فواتير المشتريات مدفوعة</div>
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'history' && (
            <div>
              <h3 style={{ marginBottom: '1.5rem', color: '#007bff' }}>📋 سجل المدفوعات والتحصيلات</h3>
              
              <div style={{ overflow: 'auto' }}>
                <table className="table">
                  <thead>
                    <tr>
                      <th>رقم القيد</th>
                      <th>التاريخ</th>
                      <th>النوع</th>
                      <th>المبلغ</th>
                      <th>الطرف</th>
                      <th>طريقة الدفع</th>
                      <th>الوصف</th>
                    </tr>
                  </thead>
                  <tbody>
                    {payments.map(payment => {
                      const isReceipt = payment.type === 'receipt';
                      const cashOrBankEntry = payment.entries.find(e => 
                        e.accountCode.includes('1111') || e.accountCode.includes('1121')
                      );
                      const customerOrSupplierEntry = payment.entries.find(e => 
                        e.accountCode.includes('1211') || e.accountCode.includes('2111')
                      );
                      
                      return (
                        <tr key={payment.id}>
                          <td style={{ fontFamily: 'monospace', fontWeight: 'bold' }}>
                            {payment.entryNumber}
                          </td>
                          <td>{new Date(payment.date).toLocaleDateString('ar-EG')}</td>
                          <td>
                            <span style={{
                              padding: '0.25rem 0.5rem',
                              borderRadius: '3px',
                              fontSize: '0.8rem',
                              fontWeight: 'bold',
                              background: isReceipt ? '#d4edda' : '#f8d7da',
                              color: isReceipt ? '#155724' : '#721c24'
                            }}>
                              {isReceipt ? '💰 تحصيل' : '💸 دفع'}
                            </span>
                          </td>
                          <td style={{ fontWeight: 'bold', color: isReceipt ? '#28a745' : '#dc3545' }}>
                            {payment.totalDebit.toLocaleString('ar-EG')} ج.م
                          </td>
                          <td>{customerOrSupplierEntry?.accountName || '-'}</td>
                          <td>
                            <span style={{
                              padding: '0.25rem 0.5rem',
                              background: cashOrBankEntry?.accountCode.includes('1111') ? '#fff3cd' : '#cce5ff',
                              borderRadius: '3px',
                              fontSize: '0.8rem'
                            }}>
                              {cashOrBankEntry?.accountCode.includes('1111') ? '💵 نقدي' : '🏦 بنكي'}
                            </span>
                          </td>
                          <td>{payment.description}</td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* نافذة عرض القيد المحاسبي */}
      {showJournalEntry && journalEntryPreview && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            padding: '2rem',
            borderRadius: '10px',
            width: '90%',
            maxWidth: '800px',
            maxHeight: '90vh',
            overflow: 'auto'
          }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '1.5rem',
              borderBottom: '2px solid #17a2b8',
              paddingBottom: '1rem'
            }}>
              <h3 style={{ margin: 0, color: '#17a2b8' }}>
                📋 مراجعة القيد المحاسبي - {paymentData.type === 'receipt' ? 'تحصيل' : 'دفع'}
              </h3>
              <button
                className="btn btn-secondary"
                onClick={() => setShowJournalEntry(false)}
              >
                ✕ إغلاق
              </button>
            </div>

            {/* معلومات القيد */}
            <div style={{
              background: '#f8f9fa',
              padding: '1rem',
              borderRadius: '5px',
              marginBottom: '1.5rem'
            }}>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem' }}>
                <div>
                  <strong>رقم القيد:</strong> {journalEntryPreview.entryNumber}
                </div>
                <div>
                  <strong>التاريخ:</strong> {new Date(journalEntryPreview.date).toLocaleDateString('ar-EG')}
                </div>
                <div>
                  <strong>المرجع:</strong> {journalEntryPreview.reference || '-'}
                </div>
                <div>
                  <strong>النوع:</strong> {paymentData.type === 'receipt' ? 'تحصيل من عميل' : 'دفع لمورد'}
                </div>
              </div>
              <div style={{ marginTop: '1rem' }}>
                <strong>الوصف:</strong> {journalEntryPreview.description}
              </div>
            </div>

            {/* جدول القيد */}
            <div style={{ overflow: 'auto', marginBottom: '1.5rem' }}>
              <table className="table">
                <thead>
                  <tr style={{ background: '#17a2b8', color: 'white' }}>
                    <th>رقم الحساب</th>
                    <th>اسم الحساب</th>
                    <th>البيان</th>
                    <th>مدين</th>
                    <th>دائن</th>
                  </tr>
                </thead>
                <tbody>
                  {journalEntryPreview.entries.map((entry, index) => (
                    <tr key={index} style={{
                      background: index % 2 === 0 ? '#f8f9fa' : 'white'
                    }}>
                      <td style={{
                        fontFamily: 'monospace',
                        fontWeight: 'bold',
                        color: '#17a2b8'
                      }}>
                        {entry.accountCode}
                      </td>
                      <td style={{ fontWeight: 'bold' }}>
                        {entry.accountName}
                      </td>
                      <td>{entry.description}</td>
                      <td style={{
                        color: entry.debit > 0 ? '#dc3545' : '#6c757d',
                        fontWeight: entry.debit > 0 ? 'bold' : 'normal'
                      }}>
                        {entry.debit > 0 ? entry.debit.toLocaleString('ar-EG') : '-'}
                      </td>
                      <td style={{
                        color: entry.credit > 0 ? '#28a745' : '#6c757d',
                        fontWeight: entry.credit > 0 ? 'bold' : 'normal'
                      }}>
                        {entry.credit > 0 ? entry.credit.toLocaleString('ar-EG') : '-'}
                      </td>
                    </tr>
                  ))}
                </tbody>
                <tfoot>
                  <tr style={{ background: '#e9ecef', fontWeight: 'bold' }}>
                    <td colSpan="3" style={{ textAlign: 'center' }}>الإجمالي</td>
                    <td style={{ color: '#dc3545' }}>
                      {journalEntryPreview.entries.reduce((sum, entry) => sum + entry.debit, 0).toLocaleString('ar-EG')} ج.م
                    </td>
                    <td style={{ color: '#28a745' }}>
                      {journalEntryPreview.entries.reduce((sum, entry) => sum + entry.credit, 0).toLocaleString('ar-EG')} ج.م
                    </td>
                  </tr>
                </tfoot>
              </table>
            </div>

            {/* التحقق من توازن القيد */}
            <div style={{
              background: '#d4edda',
              border: '1px solid #c3e6cb',
              padding: '1rem',
              borderRadius: '5px',
              marginBottom: '1.5rem',
              textAlign: 'center'
            }}>
              <div style={{ color: '#155724', fontWeight: 'bold' }}>
                ✅ القيد متوازن - يمكن المتابعة
              </div>
              <div style={{ fontSize: '0.9rem', color: '#155724', marginTop: '0.5rem' }}>
                إجمالي المدين = إجمالي الدائن = {journalEntryPreview.entries.reduce((sum, entry) => sum + entry.debit, 0).toLocaleString('ar-EG')} ج.م
              </div>
            </div>

            {/* أزرار الإجراءات */}
            <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center' }}>
              <button
                className="btn btn-success"
                onClick={handleFinalSave}
                style={{ padding: '0.75rem 2rem' }}
              >
                ✅ موافق - حفظ {paymentData.type === 'receipt' ? 'التحصيل' : 'الدفع'} والقيد
              </button>
              <button
                className="btn btn-secondary"
                onClick={() => setShowJournalEntry(false)}
                style={{ padding: '0.75rem 2rem' }}
              >
                ❌ إلغاء
              </button>
            </div>

            {/* ملاحظة مهمة */}
            <div style={{
              marginTop: '1.5rem',
              padding: '1rem',
              background: '#fff3cd',
              border: '1px solid #ffeaa7',
              borderRadius: '5px',
              fontSize: '0.9rem',
              color: '#856404'
            }}>
              <strong>ملاحظة:</strong> بعد الموافقة سيتم حفظ العملية وترحيل القيد المحاسبي تلقائياً وتحديث رصيد {paymentData.type === 'receipt' ? 'العميل' : 'المورد'}.
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PaymentManagement;
